[{"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\D_\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCGeolocationSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCGeolocationSpec.dir\\RNCGeolocationSpec-generated.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\RNCGeolocationSpec-generated.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\RNCGeolocationSpec-generated.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCGeolocationSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCGeolocationSpec.dir\\react\\renderer\\components\\RNCGeolocationSpec\\ComponentDescriptors.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\ComponentDescriptors.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\ComponentDescriptors.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCGeolocationSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCGeolocationSpec.dir\\react\\renderer\\components\\RNCGeolocationSpec\\EventEmitters.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\EventEmitters.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\EventEmitters.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCGeolocationSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCGeolocationSpec.dir\\react\\renderer\\components\\RNCGeolocationSpec\\Props.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\Props.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\Props.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCGeolocationSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCGeolocationSpec.dir\\f3ee4c34af00dbdcf11b60ec1cbc8a99\\RNCGeolocationSpecJSI-generated.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\RNCGeolocationSpecJSI-generated.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\RNCGeolocationSpecJSI-generated.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCGeolocationSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCGeolocationSpec.dir\\react\\renderer\\components\\RNCGeolocationSpec\\ShadowNodes.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\ShadowNodes.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\ShadowNodes.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCGeolocationSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCGeolocationSpec.dir\\react\\renderer\\components\\RNCGeolocationSpec\\States.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\States.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCGeolocationSpec\\States.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o lottiereactnative_autolinked_build\\CMakeFiles\\react_codegen_lottiereactnative.dir\\lottiereactnative-generated.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\lottiereactnative-generated.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\lottiereactnative-generated.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o lottiereactnative_autolinked_build\\CMakeFiles\\react_codegen_lottiereactnative.dir\\react\\renderer\\components\\lottiereactnative\\ComponentDescriptors.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\ComponentDescriptors.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\ComponentDescriptors.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o lottiereactnative_autolinked_build\\CMakeFiles\\react_codegen_lottiereactnative.dir\\react\\renderer\\components\\lottiereactnative\\EventEmitters.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\EventEmitters.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\EventEmitters.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o lottiereactnative_autolinked_build\\CMakeFiles\\react_codegen_lottiereactnative.dir\\react\\renderer\\components\\lottiereactnative\\Props.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\Props.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\Props.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o lottiereactnative_autolinked_build\\CMakeFiles\\react_codegen_lottiereactnative.dir\\react\\renderer\\components\\lottiereactnative\\ShadowNodes.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\ShadowNodes.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\ShadowNodes.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o lottiereactnative_autolinked_build\\CMakeFiles\\react_codegen_lottiereactnative.dir\\react\\renderer\\components\\lottiereactnative\\States.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\States.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\States.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o lottiereactnative_autolinked_build\\CMakeFiles\\react_codegen_lottiereactnative.dir\\b078f66e3fc321a06b399965439ac881\\lottiereactnativeJSI-generated.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\lottiereactnativeJSI-generated.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\lottiereactnative\\lottiereactnativeJSI-generated.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\ComponentDescriptors.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\EventEmitters.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\Props.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\ShadowNodes.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\States.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\rngesturehandler_codegen-generated.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_autolinked_build\\CMakeFiles\\react_codegen_RNMmkvSpec.dir\\RNMmkvSpec-generated.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\android\\build\\generated\\source\\codegen\\jni\\RNMmkvSpec-generated.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\android\\build\\generated\\source\\codegen\\jni\\RNMmkvSpec-generated.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_autolinked_build\\CMakeFiles\\react_codegen_RNMmkvSpec.dir\\react\\renderer\\components\\RNMmkvSpec\\ComponentDescriptors.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNMmkvSpec\\ComponentDescriptors.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNMmkvSpec\\ComponentDescriptors.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_autolinked_build\\CMakeFiles\\react_codegen_RNMmkvSpec.dir\\react\\renderer\\components\\RNMmkvSpec\\EventEmitters.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNMmkvSpec\\EventEmitters.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNMmkvSpec\\EventEmitters.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_autolinked_build\\CMakeFiles\\react_codegen_RNMmkvSpec.dir\\react\\renderer\\components\\RNMmkvSpec\\Props.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNMmkvSpec\\Props.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNMmkvSpec\\Props.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_autolinked_build\\CMakeFiles\\react_codegen_RNMmkvSpec.dir\\react\\renderer\\components\\RNMmkvSpec\\RNMmkvSpecJSI-generated.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNMmkvSpec\\RNMmkvSpecJSI-generated.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNMmkvSpec\\RNMmkvSpecJSI-generated.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_autolinked_build\\CMakeFiles\\react_codegen_RNMmkvSpec.dir\\react\\renderer\\components\\RNMmkvSpec\\ShadowNodes.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNMmkvSpec\\ShadowNodes.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNMmkvSpec\\ShadowNodes.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_autolinked_build\\CMakeFiles\\react_codegen_RNMmkvSpec.dir\\react\\renderer\\components\\RNMmkvSpec\\States.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNMmkvSpec\\States.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNMmkvSpec\\States.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_native_mmkv_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_cxxmodule_autolinked_build\\CMakeFiles\\react-native-mmkv.dir\\src\\main\\cpp\\AndroidLogger.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\android\\src\\main\\cpp\\AndroidLogger.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\android\\src\\main\\cpp\\AndroidLogger.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_native_mmkv_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_cxxmodule_autolinked_build\\CMakeFiles\\react-native-mmkv.dir\\e43a40a6aaf7464539a6fc7c6d856cb1\\react-native-mmkv\\cpp\\MmkvHostObject.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\cpp\\MmkvHostObject.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\cpp\\MmkvHostObject.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_native_mmkv_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNMmkvSpec_cxxmodule_autolinked_build\\CMakeFiles\\react-native-mmkv.dir\\e43a40a6aaf7464539a6fc7c6d856cb1\\react-native-mmkv\\cpp\\NativeMmkvModule.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\cpp\\NativeMmkvModule.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\cpp\\NativeMmkvModule.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\MMKV.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MMKV.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MMKV.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\MMKV_Android.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MMKV_Android.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MMKV_Android.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\MMKV_IO.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MMKV_IO.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MMKV_IO.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\MMKV_OSX.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MMKV_OSX.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MMKV_OSX.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\MMKVLog.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MMKVLog.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MMKVLog.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\MMKVLog_Android.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MMKVLog_Android.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MMKVLog_Android.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\CodedInputData.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\CodedInputData.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\CodedInputData.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\CodedInputData_OSX.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\CodedInputData_OSX.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\CodedInputData_OSX.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\CodedInputDataCrypt.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\CodedInputDataCrypt.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\CodedInputDataCrypt.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\CodedInputDataCrypt_OSX.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\CodedInputDataCrypt_OSX.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\CodedInputDataCrypt_OSX.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\CodedOutputData.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\CodedOutputData.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\CodedOutputData.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\KeyValueHolder.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\KeyValueHolder.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\KeyValueHolder.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\PBUtility.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\PBUtility.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\PBUtility.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\MiniPBCoder.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MiniPBCoder.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MiniPBCoder.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\MiniPBCoder_OSX.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MiniPBCoder_OSX.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MiniPBCoder_OSX.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\MMBuffer.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MMBuffer.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MMBuffer.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\InterProcessLock.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\InterProcessLock.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\InterProcessLock.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\InterProcessLock_Win32.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\InterProcessLock_Win32.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\InterProcessLock_Win32.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\InterProcessLock_Android.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\InterProcessLock_Android.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\InterProcessLock_Android.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\MemoryFile.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MemoryFile.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MemoryFile.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\MemoryFile_Android.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MemoryFile_Android.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MemoryFile_Android.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\MemoryFile_Linux.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MemoryFile_Linux.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MemoryFile_Linux.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\MemoryFile_Win32.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MemoryFile_Win32.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MemoryFile_Win32.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\MemoryFile_OSX.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MemoryFile_OSX.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\MemoryFile_OSX.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\ThreadLock.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\ThreadLock.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\ThreadLock.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\ThreadLock_Win32.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\ThreadLock_Win32.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\ThreadLock_Win32.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\aes\\AESCrypt.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\aes\\AESCrypt.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\aes\\AESCrypt.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\aes\\openssl\\openssl_aes_core.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\aes\\openssl\\openssl_aes_core.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\aes\\openssl\\openssl_aes_core.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\aes\\openssl\\openssl_cfb128.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\aes\\openssl\\openssl_cfb128.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\aes\\openssl\\openssl_cfb128.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\aes\\openssl\\openssl_md5_dgst.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\aes\\openssl\\openssl_md5_dgst.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\aes\\openssl\\openssl_md5_dgst.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\aes\\openssl\\openssl_md5_one.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\aes\\openssl\\openssl_md5_one.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\aes\\openssl\\openssl_md5_one.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\crc32\\crc32_armv8.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\crc32\\crc32_armv8.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\crc32\\crc32_armv8.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\crc32\\zlib\\crc32.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\crc32\\zlib\\crc32.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\crc32\\zlib\\crc32.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -target armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core  -x assembler-with-cpp -march=armv7a -D__ANDROID__ -fno-limit-debug-info  -fPIC -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\aes\\openssl\\openssl_aesv8-armx.S.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\aes\\openssl\\openssl_aesv8-armx.S", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\aes\\openssl\\openssl_aesv8-armx.S"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe -target armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core  -x assembler-with-cpp -march=armv7a -D__ANDROID__ -fno-limit-debug-info  -fPIC -o RNMmkvSpec_cxxmodule_autolinked_build\\core\\CMakeFiles\\core.dir\\aes\\openssl\\openssl_aes-armv4.S.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\aes\\openssl\\openssl_aes-armv4.S", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\aes\\openssl\\openssl_aes-armv4.S"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\Props.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\States.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\rnreanimated-generated.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\a079583fbad2ccb00d24b5a3377a5b45\\RNCSafeAreaViewShadowNode.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\c90756518d0c833d1eb9bebd6ceb0d0f\\safeareacontext\\RNCSafeAreaViewState.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\7c00d21af07c291807a849cf68ba53cf\\safeareacontext\\ComponentDescriptors.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\7c00d21af07c291807a849cf68ba53cf\\safeareacontext\\EventEmitters.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\8230730d5d98182474333995fc1e12d8\\components\\safeareacontext\\Props.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\8230730d5d98182474333995fc1e12d8\\components\\safeareacontext\\ShadowNodes.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\8230730d5d98182474333995fc1e12d8\\components\\safeareacontext\\States.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\23e67fc329de7dccb14d0961fa9e0c77\\safeareacontextJSI-generated.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\114bcbba536a9b1bb6fa5330e32c5c43\\codegen\\jni\\safeareacontext-generated.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a0e01e1003fe7ae2710d0f50dc63072c\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\d996dceb211187fa93fa79b8fdad8ee2\\components\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\c33c22593dee6ccd8dcb5bc340f46ad6\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a0e01e1003fe7ae2710d0f50dc63072c\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a0e01e1003fe7ae2710d0f50dc63072c\\rnscreens\\RNSScreenStackHeaderConfigState.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a0e01e1003fe7ae2710d0f50dc63072c\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a0e01e1003fe7ae2710d0f50dc63072c\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\9fe063b77578968dff0561a066b60480\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\71f4d03358aa51ab3ac956a1012f0049\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\5fb4b083101ddeea553a84cc890606b8\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\21b202d6f80ef16f9737c67dbe58a374\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\5fb4b083101ddeea553a84cc890606b8\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\21b202d6f80ef16f9737c67dbe58a374\\jni\\react\\renderer\\components\\rnscreens\\States.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\a8043f6570c8a7b648f2ff9f5734fd1d\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\ef4ef394e63c6eb45574ed2682627aa5\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\7454bf9ae9b1e127d86000e90e066e75\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\650e8db975c2a08ac07a388fdb640f2d\\react\\renderer\\components\\rnsvg\\RNSVGLayoutableShadowNode.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGLayoutableShadowNode.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGLayoutableShadowNode.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\ef4ef394e63c6eb45574ed2682627aa5\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGShadowNodes.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGShadowNodes.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGShadowNodes.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\rnsvg.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\android\\src\\main\\jni\\rnsvg.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\android\\src\\main\\jni\\rnsvg.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\0f394722275748a3b318be03e4260359\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\35550c58ca4a58409094a9f47e98b328\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\88f36a3b35e07696b8128f1742f031b9\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\35550c58ca4a58409094a9f47e98b328\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\88f36a3b35e07696b8128f1742f031b9\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnsvg_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\0f394722275748a3b318be03e4260359\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\RNVectorIconsSpec-generated.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\796ad459e5c56493b6ccb0d610a9a963\\RNVectorIconsSpecJSI-generated.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp"}]