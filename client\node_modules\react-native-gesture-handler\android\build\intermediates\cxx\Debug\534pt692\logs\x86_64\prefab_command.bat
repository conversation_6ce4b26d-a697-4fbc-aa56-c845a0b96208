@echo off
"C:\\Program Files\\Java\\jdk-17\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  x86_64 ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  26 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging12117247775019834851\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495638402d0fc5d3374d8dd464a0b611\\transformed\\react-android-0.77.0-debug\\prefab" ^
  "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\refs\\react-native-reanimated\\3s1h186l" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9cd38d273aa80e0d66466b04adf6ea09\\transformed\\fbjni-0.7.0\\prefab"
