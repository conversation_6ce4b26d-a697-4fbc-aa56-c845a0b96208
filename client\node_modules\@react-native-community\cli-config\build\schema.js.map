{"version": 3, "names": ["map", "key", "value", "t", "object", "unknown", "pattern", "command", "name", "string", "required", "description", "usage", "func", "options", "array", "items", "parse", "default", "alternatives", "try", "bool", "number", "allow", "rename", "ignoreUndefined", "examples", "desc", "cmd", "healthCheck", "label", "healthchecks", "isRequired", "getDiagnostics", "win32AutomaticFix", "darwinAutomaticFix", "linuxAutomaticFix", "runAutomaticFix", "dependencyConfig", "dependency", "platforms", "any", "keys", "ios", "scriptPhases", "configurations", "android", "sourceDir", "manifestPath", "packageName", "packageImportPath", "packageInstance", "dependencyConfiguration", "buildTypes", "libraryName", "componentDescriptors", "cmakeListsPath", "cxxModuleCMakeListsModuleName", "cxxModuleCMakeListsPath", "cxxModuleHeaderName", "npmPackageName", "optional", "projectConfig", "linkConfig", "commands", "healthChecks", "dependencies", "root", "podspecPath", "version", "reactNativePath", "project", "watchModeCommandParams", "automaticPodsInstallation", "assets", "appName"], "sources": ["../src/schema.ts"], "sourcesContent": ["/**\n * This schema is used by `cli-config` to validate the structure. Make sure\n * this file stays up to date with `cli-types` package.\n *\n * In the future, it would be great to generate this file automatically from the\n * Typescript types.\n */\nimport t, {SchemaLike} from 'joi';\n\nconst map = (key: RegExp | SchemaLike, value: SchemaLike) =>\n  t.object().unknown(true).pattern(key, value);\n\n/**\n * Schema for CommandT\n */\nconst command = t.object({\n  name: t.string().required(),\n  description: t.string(),\n  usage: t.string(),\n  func: t.func().required(),\n  options: t.array().items(\n    t\n      .object({\n        name: t.string().required(),\n        description: t.string(),\n        parse: t.func(),\n        default: t\n          .alternatives()\n          .try(t.bool(), t.number(), t.string().allow(''), t.func()),\n      })\n      .rename('command', 'name', {ignoreUndefined: true}),\n  ),\n  examples: t.array().items(\n    t.object({\n      desc: t.string().required(),\n      cmd: t.string().required(),\n    }),\n  ),\n});\n\n/**\n * Schema for HealthChecksT\n */\nconst healthCheck = t.object({\n  label: t.string().required(),\n  healthchecks: t.array().items(\n    t.object({\n      label: t.string().required(),\n      isRequired: t.bool(),\n      description: t.string(),\n      getDiagnostics: t.func(),\n      win32AutomaticFix: t.func(),\n      darwinAutomaticFix: t.func(),\n      linuxAutomaticFix: t.func(),\n      runAutomaticFix: t.func().required(),\n    }),\n  ),\n});\n\n/**\n * Schema for UserDependencyConfig\n */\nexport const dependencyConfig = t\n  .object({\n    dependency: t\n      .object({\n        platforms: map(t.string(), t.any())\n          .keys({\n            ios: t\n              // IOSDependencyParams\n              .object({\n                scriptPhases: t.array().items(t.object()),\n                configurations: t.array().items(t.string()).default([]),\n              })\n              .allow(null),\n            android: t\n              // AndroidDependencyParams\n              .object({\n                sourceDir: t.string(),\n                manifestPath: t.string(),\n                packageName: t.string(),\n                packageImportPath: t.string(),\n                packageInstance: t.string(),\n                dependencyConfiguration: t.string(),\n                buildTypes: t.array().items(t.string()).default([]),\n                libraryName: t.string().allow(null),\n                componentDescriptors: t.array().items(t.string()).allow(null),\n                cmakeListsPath: t.string().allow(null),\n                cxxModuleCMakeListsModuleName: t.string().allow(null),\n                cxxModuleCMakeListsPath: t.string().allow(null),\n                cxxModuleHeaderName: t.string().allow(null),\n              })\n              .allow(null),\n          })\n          .default(),\n      })\n      .default(),\n    platforms: map(\n      t.string(),\n      t.object({\n        npmPackageName: t.string().optional(),\n        dependencyConfig: t.func(),\n        projectConfig: t.func(),\n        linkConfig: t.func(),\n      }),\n    ).default({}),\n    commands: t.array().items(command).default([]),\n    healthChecks: t.array().items(healthCheck).default([]),\n  })\n  .unknown(true)\n  .default();\n\n/**\n * Schema for ProjectConfig\n */\nexport const projectConfig = t\n  .object({\n    dependencies: map(\n      t.string(),\n      t\n        .object({\n          root: t.string(),\n          platforms: map(t.string(), t.any()).keys({\n            ios: t\n              // IOSDependencyConfig\n              .object({\n                podspecPath: t.string(),\n                version: t.string(),\n                configurations: t.array().items(t.string()).default([]),\n                scriptPhases: t.array().items(t.object()).default([]),\n              })\n              .allow(null),\n            android: t\n              // AndroidDependencyConfig\n              .object({\n                sourceDir: t.string(),\n                packageImportPath: t.string(),\n                packageInstance: t.string(),\n                dependencyConfiguration: t.string(),\n                buildTypes: t.array().items(t.string()).default([]),\n                libraryName: t.string().allow(null),\n                componentDescriptors: t.array().items(t.string()).allow(null),\n                cmakeListsPath: t.string().allow(null),\n              })\n              .allow(null),\n          }),\n        })\n        .allow(null),\n    ).default({}),\n    reactNativePath: t.string(),\n    project: map(t.string(), t.any())\n      .keys({\n        ios: t\n          // IOSProjectParams\n          .object({\n            sourceDir: t.string(),\n            watchModeCommandParams: t.array().items(t.string()),\n            automaticPodsInstallation: t.bool().default(true),\n            assets: t.array().items(t.string()).default([]),\n          })\n          .default({}),\n        android: t\n          // AndroidProjectParams\n          .object({\n            sourceDir: t.string(),\n            appName: t.string(),\n            manifestPath: t.string(),\n            packageName: t.string(),\n            dependencyConfiguration: t.string(),\n            watchModeCommandParams: t.array().items(t.string()),\n            assets: t.array().items(t.string()).default([]),\n          })\n          .default({}),\n      })\n      .default(),\n    assets: t.array().items(t.string()).default([]),\n    commands: t.array().items(command).default([]),\n    platforms: map(\n      t.string(),\n      t.object({\n        npmPackageName: t.string().optional(),\n        dependencyConfig: t.func(),\n        projectConfig: t.func(),\n        linkConfig: t.func(),\n      }),\n    ).default({}),\n  })\n  .unknown(true)\n  .default();\n"], "mappings": ";;;;;;AAOA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAkC;AAPlC;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,MAAMA,GAAG,GAAG,CAACC,GAAwB,EAAEC,KAAiB,KACtDC,cAAC,CAACC,MAAM,EAAE,CAACC,OAAO,CAAC,IAAI,CAAC,CAACC,OAAO,CAACL,GAAG,EAAEC,KAAK,CAAC;;AAE9C;AACA;AACA;AACA,MAAMK,OAAO,GAAGJ,cAAC,CAACC,MAAM,CAAC;EACvBI,IAAI,EAAEL,cAAC,CAACM,MAAM,EAAE,CAACC,QAAQ,EAAE;EAC3BC,WAAW,EAAER,cAAC,CAACM,MAAM,EAAE;EACvBG,KAAK,EAAET,cAAC,CAACM,MAAM,EAAE;EACjBI,IAAI,EAAEV,cAAC,CAACU,IAAI,EAAE,CAACH,QAAQ,EAAE;EACzBI,OAAO,EAAEX,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CACtBb,cAAC,CACEC,MAAM,CAAC;IACNI,IAAI,EAAEL,cAAC,CAACM,MAAM,EAAE,CAACC,QAAQ,EAAE;IAC3BC,WAAW,EAAER,cAAC,CAACM,MAAM,EAAE;IACvBQ,KAAK,EAAEd,cAAC,CAACU,IAAI,EAAE;IACfK,OAAO,EAAEf,cAAC,CACPgB,YAAY,EAAE,CACdC,GAAG,CAACjB,cAAC,CAACkB,IAAI,EAAE,EAAElB,cAAC,CAACmB,MAAM,EAAE,EAAEnB,cAAC,CAACM,MAAM,EAAE,CAACc,KAAK,CAAC,EAAE,CAAC,EAAEpB,cAAC,CAACU,IAAI,EAAE;EAC7D,CAAC,CAAC,CACDW,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE;IAACC,eAAe,EAAE;EAAI,CAAC,CAAC,CACtD;EACDC,QAAQ,EAAEvB,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CACvBb,cAAC,CAACC,MAAM,CAAC;IACPuB,IAAI,EAAExB,cAAC,CAACM,MAAM,EAAE,CAACC,QAAQ,EAAE;IAC3BkB,GAAG,EAAEzB,cAAC,CAACM,MAAM,EAAE,CAACC,QAAQ;EAC1B,CAAC,CAAC;AAEN,CAAC,CAAC;;AAEF;AACA;AACA;AACA,MAAMmB,WAAW,GAAG1B,cAAC,CAACC,MAAM,CAAC;EAC3B0B,KAAK,EAAE3B,cAAC,CAACM,MAAM,EAAE,CAACC,QAAQ,EAAE;EAC5BqB,YAAY,EAAE5B,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CAC3Bb,cAAC,CAACC,MAAM,CAAC;IACP0B,KAAK,EAAE3B,cAAC,CAACM,MAAM,EAAE,CAACC,QAAQ,EAAE;IAC5BsB,UAAU,EAAE7B,cAAC,CAACkB,IAAI,EAAE;IACpBV,WAAW,EAAER,cAAC,CAACM,MAAM,EAAE;IACvBwB,cAAc,EAAE9B,cAAC,CAACU,IAAI,EAAE;IACxBqB,iBAAiB,EAAE/B,cAAC,CAACU,IAAI,EAAE;IAC3BsB,kBAAkB,EAAEhC,cAAC,CAACU,IAAI,EAAE;IAC5BuB,iBAAiB,EAAEjC,cAAC,CAACU,IAAI,EAAE;IAC3BwB,eAAe,EAAElC,cAAC,CAACU,IAAI,EAAE,CAACH,QAAQ;EACpC,CAAC,CAAC;AAEN,CAAC,CAAC;;AAEF;AACA;AACA;AACO,MAAM4B,gBAAgB,GAAGnC,cAAC,CAC9BC,MAAM,CAAC;EACNmC,UAAU,EAAEpC,cAAC,CACVC,MAAM,CAAC;IACNoC,SAAS,EAAExC,GAAG,CAACG,cAAC,CAACM,MAAM,EAAE,EAAEN,cAAC,CAACsC,GAAG,EAAE,CAAC,CAChCC,IAAI,CAAC;MACJC,GAAG,EAAExC;MACH;MAAA,CACCC,MAAM,CAAC;QACNwC,YAAY,EAAEzC,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CAACb,cAAC,CAACC,MAAM,EAAE,CAAC;QACzCyC,cAAc,EAAE1C,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CAACb,cAAC,CAACM,MAAM,EAAE,CAAC,CAACS,OAAO,CAAC,EAAE;MACxD,CAAC,CAAC,CACDK,KAAK,CAAC,IAAI,CAAC;MACduB,OAAO,EAAE3C;MACP;MAAA,CACCC,MAAM,CAAC;QACN2C,SAAS,EAAE5C,cAAC,CAACM,MAAM,EAAE;QACrBuC,YAAY,EAAE7C,cAAC,CAACM,MAAM,EAAE;QACxBwC,WAAW,EAAE9C,cAAC,CAACM,MAAM,EAAE;QACvByC,iBAAiB,EAAE/C,cAAC,CAACM,MAAM,EAAE;QAC7B0C,eAAe,EAAEhD,cAAC,CAACM,MAAM,EAAE;QAC3B2C,uBAAuB,EAAEjD,cAAC,CAACM,MAAM,EAAE;QACnC4C,UAAU,EAAElD,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CAACb,cAAC,CAACM,MAAM,EAAE,CAAC,CAACS,OAAO,CAAC,EAAE,CAAC;QACnDoC,WAAW,EAAEnD,cAAC,CAACM,MAAM,EAAE,CAACc,KAAK,CAAC,IAAI,CAAC;QACnCgC,oBAAoB,EAAEpD,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CAACb,cAAC,CAACM,MAAM,EAAE,CAAC,CAACc,KAAK,CAAC,IAAI,CAAC;QAC7DiC,cAAc,EAAErD,cAAC,CAACM,MAAM,EAAE,CAACc,KAAK,CAAC,IAAI,CAAC;QACtCkC,6BAA6B,EAAEtD,cAAC,CAACM,MAAM,EAAE,CAACc,KAAK,CAAC,IAAI,CAAC;QACrDmC,uBAAuB,EAAEvD,cAAC,CAACM,MAAM,EAAE,CAACc,KAAK,CAAC,IAAI,CAAC;QAC/CoC,mBAAmB,EAAExD,cAAC,CAACM,MAAM,EAAE,CAACc,KAAK,CAAC,IAAI;MAC5C,CAAC,CAAC,CACDA,KAAK,CAAC,IAAI;IACf,CAAC,CAAC,CACDL,OAAO;EACZ,CAAC,CAAC,CACDA,OAAO,EAAE;EACZsB,SAAS,EAAExC,GAAG,CACZG,cAAC,CAACM,MAAM,EAAE,EACVN,cAAC,CAACC,MAAM,CAAC;IACPwD,cAAc,EAAEzD,cAAC,CAACM,MAAM,EAAE,CAACoD,QAAQ,EAAE;IACrCvB,gBAAgB,EAAEnC,cAAC,CAACU,IAAI,EAAE;IAC1BiD,aAAa,EAAE3D,cAAC,CAACU,IAAI,EAAE;IACvBkD,UAAU,EAAE5D,cAAC,CAACU,IAAI;EACpB,CAAC,CAAC,CACH,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC;EACb8C,QAAQ,EAAE7D,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CAACT,OAAO,CAAC,CAACW,OAAO,CAAC,EAAE,CAAC;EAC9C+C,YAAY,EAAE9D,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CAACa,WAAW,CAAC,CAACX,OAAO,CAAC,EAAE;AACvD,CAAC,CAAC,CACDb,OAAO,CAAC,IAAI,CAAC,CACba,OAAO,EAAE;;AAEZ;AACA;AACA;AAFA;AAGO,MAAM4C,aAAa,GAAG3D,cAAC,CAC3BC,MAAM,CAAC;EACN8D,YAAY,EAAElE,GAAG,CACfG,cAAC,CAACM,MAAM,EAAE,EACVN,cAAC,CACEC,MAAM,CAAC;IACN+D,IAAI,EAAEhE,cAAC,CAACM,MAAM,EAAE;IAChB+B,SAAS,EAAExC,GAAG,CAACG,cAAC,CAACM,MAAM,EAAE,EAAEN,cAAC,CAACsC,GAAG,EAAE,CAAC,CAACC,IAAI,CAAC;MACvCC,GAAG,EAAExC;MACH;MAAA,CACCC,MAAM,CAAC;QACNgE,WAAW,EAAEjE,cAAC,CAACM,MAAM,EAAE;QACvB4D,OAAO,EAAElE,cAAC,CAACM,MAAM,EAAE;QACnBoC,cAAc,EAAE1C,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CAACb,cAAC,CAACM,MAAM,EAAE,CAAC,CAACS,OAAO,CAAC,EAAE,CAAC;QACvD0B,YAAY,EAAEzC,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CAACb,cAAC,CAACC,MAAM,EAAE,CAAC,CAACc,OAAO,CAAC,EAAE;MACtD,CAAC,CAAC,CACDK,KAAK,CAAC,IAAI,CAAC;MACduB,OAAO,EAAE3C;MACP;MAAA,CACCC,MAAM,CAAC;QACN2C,SAAS,EAAE5C,cAAC,CAACM,MAAM,EAAE;QACrByC,iBAAiB,EAAE/C,cAAC,CAACM,MAAM,EAAE;QAC7B0C,eAAe,EAAEhD,cAAC,CAACM,MAAM,EAAE;QAC3B2C,uBAAuB,EAAEjD,cAAC,CAACM,MAAM,EAAE;QACnC4C,UAAU,EAAElD,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CAACb,cAAC,CAACM,MAAM,EAAE,CAAC,CAACS,OAAO,CAAC,EAAE,CAAC;QACnDoC,WAAW,EAAEnD,cAAC,CAACM,MAAM,EAAE,CAACc,KAAK,CAAC,IAAI,CAAC;QACnCgC,oBAAoB,EAAEpD,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CAACb,cAAC,CAACM,MAAM,EAAE,CAAC,CAACc,KAAK,CAAC,IAAI,CAAC;QAC7DiC,cAAc,EAAErD,cAAC,CAACM,MAAM,EAAE,CAACc,KAAK,CAAC,IAAI;MACvC,CAAC,CAAC,CACDA,KAAK,CAAC,IAAI;IACf,CAAC;EACH,CAAC,CAAC,CACDA,KAAK,CAAC,IAAI,CAAC,CACf,CAACL,OAAO,CAAC,CAAC,CAAC,CAAC;EACboD,eAAe,EAAEnE,cAAC,CAACM,MAAM,EAAE;EAC3B8D,OAAO,EAAEvE,GAAG,CAACG,cAAC,CAACM,MAAM,EAAE,EAAEN,cAAC,CAACsC,GAAG,EAAE,CAAC,CAC9BC,IAAI,CAAC;IACJC,GAAG,EAAExC;IACH;IAAA,CACCC,MAAM,CAAC;MACN2C,SAAS,EAAE5C,cAAC,CAACM,MAAM,EAAE;MACrB+D,sBAAsB,EAAErE,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CAACb,cAAC,CAACM,MAAM,EAAE,CAAC;MACnDgE,yBAAyB,EAAEtE,cAAC,CAACkB,IAAI,EAAE,CAACH,OAAO,CAAC,IAAI,CAAC;MACjDwD,MAAM,EAAEvE,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CAACb,cAAC,CAACM,MAAM,EAAE,CAAC,CAACS,OAAO,CAAC,EAAE;IAChD,CAAC,CAAC,CACDA,OAAO,CAAC,CAAC,CAAC,CAAC;IACd4B,OAAO,EAAE3C;IACP;IAAA,CACCC,MAAM,CAAC;MACN2C,SAAS,EAAE5C,cAAC,CAACM,MAAM,EAAE;MACrBkE,OAAO,EAAExE,cAAC,CAACM,MAAM,EAAE;MACnBuC,YAAY,EAAE7C,cAAC,CAACM,MAAM,EAAE;MACxBwC,WAAW,EAAE9C,cAAC,CAACM,MAAM,EAAE;MACvB2C,uBAAuB,EAAEjD,cAAC,CAACM,MAAM,EAAE;MACnC+D,sBAAsB,EAAErE,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CAACb,cAAC,CAACM,MAAM,EAAE,CAAC;MACnDiE,MAAM,EAAEvE,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CAACb,cAAC,CAACM,MAAM,EAAE,CAAC,CAACS,OAAO,CAAC,EAAE;IAChD,CAAC,CAAC,CACDA,OAAO,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CACDA,OAAO,EAAE;EACZwD,MAAM,EAAEvE,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CAACb,cAAC,CAACM,MAAM,EAAE,CAAC,CAACS,OAAO,CAAC,EAAE,CAAC;EAC/C8C,QAAQ,EAAE7D,cAAC,CAACY,KAAK,EAAE,CAACC,KAAK,CAACT,OAAO,CAAC,CAACW,OAAO,CAAC,EAAE,CAAC;EAC9CsB,SAAS,EAAExC,GAAG,CACZG,cAAC,CAACM,MAAM,EAAE,EACVN,cAAC,CAACC,MAAM,CAAC;IACPwD,cAAc,EAAEzD,cAAC,CAACM,MAAM,EAAE,CAACoD,QAAQ,EAAE;IACrCvB,gBAAgB,EAAEnC,cAAC,CAACU,IAAI,EAAE;IAC1BiD,aAAa,EAAE3D,cAAC,CAACU,IAAI,EAAE;IACvBkD,UAAU,EAAE5D,cAAC,CAACU,IAAI;EACpB,CAAC,CAAC,CACH,CAACK,OAAO,CAAC,CAAC,CAAC;AACd,CAAC,CAAC,CACDb,OAAO,CAAC,IAAI,CAAC,CACba,OAAO,EAAE;AAAC"}