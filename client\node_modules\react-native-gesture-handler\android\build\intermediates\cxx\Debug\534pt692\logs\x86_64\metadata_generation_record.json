[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: x86_64", "file_": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\534pt692\\x86_64\\android_gradle_build.json due to:", "file_": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\Java\\\\jdk-17\\\\bin\\\\java\" ^\n  --class-path ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  x86_64 ^\n  --os-version ^\n  24 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  26 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging12117247775019834851\\\\staged-cli-output\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.10.2\\\\transforms\\\\495638402d0fc5d3374d8dd464a0b611\\\\transformed\\\\react-android-0.77.0-debug\\\\prefab\" ^\n  \"D:\\\\Zave\\\\aC5d55GwXZEbEbfnCDrKUR\\\\ZAVE\\\\29-05\\\\client\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\build\\\\intermediates\\\\cxx\\\\refs\\\\react-native-reanimated\\\\3s1h186l\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.10.2\\\\transforms\\\\9cd38d273aa80e0d66466b04adf6ea09\\\\transformed\\\\fbjni-0.7.0\\\\prefab\"\n", "file_": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\534pt692\\x86_64'", "file_": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\534pt692\\x86_64'", "file_": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\Zave\\\\aC5d55GwXZEbEbfnCDrKUR\\\\ZAVE\\\\29-05\\\\client\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\src\\\\main\\\\jni\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=x86_64\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86_64\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_CXX_FLAGS=-O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\Zave\\\\aC5d55GwXZEbEbfnCDrKUR\\\\ZAVE\\\\29-05\\\\client\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\534pt692\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\Zave\\\\aC5d55GwXZEbEbfnCDrKUR\\\\ZAVE\\\\29-05\\\\client\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\534pt692\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=D:\\\\Zave\\\\aC5d55GwXZEbEbfnCDrKUR\\\\ZAVE\\\\29-05\\\\client\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\.cxx\\\\Debug\\\\534pt692\\\\prefab\\\\x86_64\\\\prefab\" ^\n  \"-BD:\\\\Zave\\\\aC5d55GwXZEbEbfnCDrKUR\\\\ZAVE\\\\29-05\\\\client\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\.cxx\\\\Debug\\\\534pt692\\\\x86_64\" ^\n  -GNinja ^\n  \"-DREACT_NATIVE_DIR=D:\\\\Zave\\\\aC5d55GwXZEbEbfnCDrKUR\\\\ZAVE\\\\29-05\\\\client\\\\node_modules\\\\react-native\" ^\n  \"-DREACT_NATIVE_MINOR_VERSION=77\" ^\n  \"-DANDROID_STL=c++_shared\"\n", "file_": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\Zave\\\\aC5d55GwXZEbEbfnCDrKUR\\\\ZAVE\\\\29-05\\\\client\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\src\\\\main\\\\jni\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=x86_64\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86_64\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\26.1.10909125\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_CXX_FLAGS=-O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\Zave\\\\aC5d55GwXZEbEbfnCDrKUR\\\\ZAVE\\\\29-05\\\\client\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\534pt692\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\Zave\\\\aC5d55GwXZEbEbfnCDrKUR\\\\ZAVE\\\\29-05\\\\client\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\534pt692\\\\obj\\\\x86_64\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=D:\\\\Zave\\\\aC5d55GwXZEbEbfnCDrKUR\\\\ZAVE\\\\29-05\\\\client\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\.cxx\\\\Debug\\\\534pt692\\\\prefab\\\\x86_64\\\\prefab\" ^\n  \"-BD:\\\\Zave\\\\aC5d55GwXZEbEbfnCDrKUR\\\\ZAVE\\\\29-05\\\\client\\\\node_modules\\\\react-native-gesture-handler\\\\android\\\\.cxx\\\\Debug\\\\534pt692\\\\x86_64\" ^\n  -GNinja ^\n  \"-DREACT_NATIVE_DIR=D:\\\\Zave\\\\aC5d55GwXZEbEbfnCDrKUR\\\\ZAVE\\\\29-05\\\\client\\\\node_modules\\\\react-native\" ^\n  \"-DREACT_NATIVE_MINOR_VERSION=77\" ^\n  \"-DANDROID_STL=c++_shared\"\n", "file_": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\534pt692\\x86_64\\compile_commands.json.bin normally", "file_": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\534pt692\\x86_64\\compile_commands.json to D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\.cxx\\tools\\debug\\x86_64\\compile_commands.json", "file_": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt", "tag_": "debug|x86_64", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]