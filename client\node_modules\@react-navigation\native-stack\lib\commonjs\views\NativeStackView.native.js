"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.NativeStackView = NativeStackView;
var _elements = require("@react-navigation/elements");
var _native = require("@react-navigation/native");
var React = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _reactNativeSafeAreaContext = require("react-native-safe-area-context");
var _reactNativeScreens = require("react-native-screens");
var _debounce = require("../utils/debounce.js");
var _getModalRoutesKeys = require("../utils/getModalRoutesKeys.js");
var _useAnimatedHeaderHeight = require("../utils/useAnimatedHeaderHeight.js");
var _useDismissedRouteError = require("../utils/useDismissedRouteError.js");
var _useInvalidPreventRemoveError = require("../utils/useInvalidPreventRemoveError.js");
var _useHeaderConfigProps = require("./useHeaderConfigProps.js");
var _jsxRuntime = require("react/jsx-runtime");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
const ANDROID_DEFAULT_HEADER_HEIGHT = 56;
function isFabric() {
  return 'nativeFabricUIManager' in global;
}
const useNativeDriver = _reactNative.Platform.OS !== 'web';
const SceneView = ({
  index,
  focused,
  shouldFreeze,
  descriptor,
  previousDescriptor,
  nextDescriptor,
  isPresentationModal,
  isPreloaded,
  onWillDisappear,
  onWillAppear,
  onAppear,
  onDisappear,
  onDismissed,
  onHeaderBackButtonClicked,
  onNativeDismissCancelled,
  onGestureCancel,
  onSheetDetentChanged
}) => {
  const {
    route,
    navigation,
    options,
    render
  } = descriptor;
  let {
    animation,
    animationMatchesGesture,
    presentation = isPresentationModal ? 'modal' : 'card',
    fullScreenGestureEnabled
  } = options;
  const {
    animationDuration,
    animationTypeForReplace = 'push',
    fullScreenGestureShadowEnabled = true,
    gestureEnabled,
    gestureDirection = presentation === 'card' ? 'horizontal' : 'vertical',
    gestureResponseDistance,
    header,
    headerBackButtonMenuEnabled,
    headerShown,
    headerBackground,
    headerTransparent,
    autoHideHomeIndicator,
    keyboardHandlingEnabled,
    navigationBarColor,
    navigationBarTranslucent,
    navigationBarHidden,
    orientation,
    sheetAllowedDetents = [1.0],
    sheetLargestUndimmedDetentIndex = -1,
    sheetGrabberVisible = false,
    sheetCornerRadius = -1.0,
    sheetElevation = 24,
    sheetExpandsWhenScrolledToEdge = true,
    sheetInitialDetentIndex = 0,
    statusBarAnimation,
    statusBarHidden,
    statusBarStyle,
    statusBarTranslucent,
    statusBarBackgroundColor,
    unstable_sheetFooter,
    freezeOnBlur,
    contentStyle
  } = options;
  if (gestureDirection === 'vertical' && _reactNative.Platform.OS === 'ios') {
    // for `vertical` direction to work, we need to set `fullScreenGestureEnabled` to `true`
    // so the screen can be dismissed from any point on screen.
    // `animationMatchesGesture` needs to be set to `true` so the `animation` set by user can be used,
    // otherwise `simple_push` will be used.
    // Also, the default animation for this direction seems to be `slide_from_bottom`.
    if (fullScreenGestureEnabled === undefined) {
      fullScreenGestureEnabled = true;
    }
    if (animationMatchesGesture === undefined) {
      animationMatchesGesture = true;
    }
    if (animation === undefined) {
      animation = 'slide_from_bottom';
    }
  }

  // workaround for rn-screens where gestureDirection has to be set on both
  // current and previous screen - software-mansion/react-native-screens/pull/1509
  const nextGestureDirection = nextDescriptor?.options.gestureDirection;
  const gestureDirectionOverride = nextGestureDirection != null ? nextGestureDirection : gestureDirection;
  if (index === 0) {
    // first screen should always be treated as `card`, it resolves problems with no header animation
    // for navigator with first screen as `modal` and the next as `card`
    presentation = 'card';
  }
  const {
    colors
  } = (0, _native.useTheme)();
  const insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
  const frame = (0, _reactNativeSafeAreaContext.useSafeAreaFrame)();

  // `modal` and `formSheet` presentations do not take whole screen, so should not take the inset.
  const isModal = presentation === 'modal' || presentation === 'formSheet';

  // Modals are fullscreen in landscape only on iPhone
  const isIPhone = _reactNative.Platform.OS === 'ios' && !(_reactNative.Platform.isPad || _reactNative.Platform.isTV);
  const isLandscape = frame.width > frame.height;
  const isParentHeaderShown = React.useContext(_elements.HeaderShownContext);
  const parentHeaderHeight = React.useContext(_elements.HeaderHeightContext);
  const parentHeaderBack = React.useContext(_elements.HeaderBackContext);
  const topInset = isParentHeaderShown || _reactNative.Platform.OS === 'ios' && isModal || isIPhone && isLandscape ? 0 : insets.top;
  const {
    preventedRoutes
  } = (0, _native.usePreventRemoveContext)();
  const defaultHeaderHeight = _reactNative.Platform.select({
    // FIXME: Currently screens isn't using Material 3
    // So our `getDefaultHeaderHeight` doesn't return the correct value
    // So we hardcode the value here for now until screens is updated
    android: ANDROID_DEFAULT_HEADER_HEIGHT + topInset,
    default: (0, _elements.getDefaultHeaderHeight)(frame, isModal, topInset)
  });
  const [headerHeight, setHeaderHeight] = React.useState(defaultHeaderHeight);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const setHeaderHeightDebounced = React.useCallback(
  // Debounce the header height updates to avoid excessive re-renders
  (0, _debounce.debounce)(setHeaderHeight, 100), []);
  const hasCustomHeader = header !== undefined;
  let headerHeightCorrectionOffset = 0;
  if (_reactNative.Platform.OS === 'android' && !hasCustomHeader) {
    const statusBarHeight = _reactNative.StatusBar.currentHeight ?? 0;

    // FIXME: On Android, the native header height is not correctly calculated
    // It includes status bar height even if statusbar is not translucent
    // And the statusbar value itself doesn't match the actual status bar height
    // So we subtract the bogus status bar height and add the actual top inset
    headerHeightCorrectionOffset = -statusBarHeight + topInset;
  }
  const rawAnimatedHeaderHeight = (0, _reactNative.useAnimatedValue)(defaultHeaderHeight);
  const animatedHeaderHeight = React.useMemo(() => _reactNative.Animated.add(rawAnimatedHeaderHeight, headerHeightCorrectionOffset), [headerHeightCorrectionOffset, rawAnimatedHeaderHeight]);

  // During the very first render topInset is > 0 when running
  // in non edge-to-edge mode on Android, while on every consecutive render
  // topInset === 0, causing header content to jump, as we add padding on the first frame,
  // just to remove it in next one. To prevent this, when statusBarTranslucent is set,
  // we apply additional padding in header only if its true.
  // For more details see: https://github.com/react-navigation/react-navigation/pull/12014
  const headerTopInsetEnabled = typeof statusBarTranslucent === 'boolean' ? statusBarTranslucent : topInset !== 0;
  const canGoBack = previousDescriptor != null || parentHeaderBack != null;
  const backTitle = previousDescriptor ? (0, _elements.getHeaderTitle)(previousDescriptor.options, previousDescriptor.route.name) : parentHeaderBack?.title;
  const headerBack = React.useMemo(() => {
    if (canGoBack) {
      return {
        href: undefined,
        // No href needed for native
        title: backTitle
      };
    }
    return undefined;
  }, [canGoBack, backTitle]);
  const isRemovePrevented = preventedRoutes[route.key]?.preventRemove;
  const headerConfig = (0, _useHeaderConfigProps.useHeaderConfigProps)({
    ...options,
    route,
    headerBackButtonMenuEnabled: isRemovePrevented !== undefined ? !isRemovePrevented : headerBackButtonMenuEnabled,
    headerBackTitle: options.headerBackTitle !== undefined ? options.headerBackTitle : undefined,
    headerHeight,
    headerShown: header !== undefined ? false : headerShown,
    headerTopInsetEnabled,
    headerBack
  });
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_native.NavigationContext.Provider, {
    value: navigation,
    children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_native.NavigationRouteContext.Provider, {
      value: route,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeScreens.ScreenStackItem, {
        screenId: route.key,
        activityState: isPreloaded ? 0 : 2,
        style: _reactNative.StyleSheet.absoluteFill,
        accessibilityElementsHidden: !focused,
        importantForAccessibility: focused ? 'auto' : 'no-hide-descendants',
        customAnimationOnSwipe: animationMatchesGesture,
        fullScreenSwipeEnabled: fullScreenGestureEnabled,
        fullScreenSwipeShadowEnabled: fullScreenGestureShadowEnabled,
        freezeOnBlur: freezeOnBlur,
        gestureEnabled: _reactNative.Platform.OS === 'android' ?
        // This prop enables handling of system back gestures on Android
        // Since we handle them in JS side, we disable this
        false : gestureEnabled,
        homeIndicatorHidden: autoHideHomeIndicator,
        hideKeyboardOnSwipe: keyboardHandlingEnabled,
        navigationBarColor: navigationBarColor,
        navigationBarTranslucent: navigationBarTranslucent,
        navigationBarHidden: navigationBarHidden,
        replaceAnimation: animationTypeForReplace,
        stackPresentation: presentation === 'card' ? 'push' : presentation,
        stackAnimation: animation,
        screenOrientation: orientation,
        sheetAllowedDetents: sheetAllowedDetents,
        sheetLargestUndimmedDetentIndex: sheetLargestUndimmedDetentIndex,
        sheetGrabberVisible: sheetGrabberVisible,
        sheetInitialDetentIndex: sheetInitialDetentIndex,
        sheetCornerRadius: sheetCornerRadius,
        sheetElevation: sheetElevation,
        sheetExpandsWhenScrolledToEdge: sheetExpandsWhenScrolledToEdge,
        statusBarAnimation: statusBarAnimation,
        statusBarHidden: statusBarHidden,
        statusBarStyle: statusBarStyle,
        statusBarColor: statusBarBackgroundColor,
        statusBarTranslucent: statusBarTranslucent,
        swipeDirection: gestureDirectionOverride,
        transitionDuration: animationDuration,
        onWillAppear: onWillAppear,
        onWillDisappear: onWillDisappear,
        onAppear: onAppear,
        onDisappear: onDisappear,
        onDismissed: onDismissed,
        onGestureCancel: onGestureCancel,
        onSheetDetentChanged: onSheetDetentChanged,
        gestureResponseDistance: gestureResponseDistance,
        nativeBackButtonDismissalEnabled: false // on Android
        ,
        onHeaderBackButtonClicked: onHeaderBackButtonClicked,
        preventNativeDismiss: isRemovePrevented // on iOS
        ,
        onNativeDismissCancelled: onNativeDismissCancelled
        // Unfortunately, because of the bug that exists on Fabric, where native event drivers
        // for Animated objects are being created after the first notifications about the header height
        // from the native side, `onHeaderHeightChange` event does not notify
        // `animatedHeaderHeight` about initial values on appearing screens at the moment.
        ,
        onHeaderHeightChange: _reactNative.Animated.event([{
          nativeEvent: {
            headerHeight: rawAnimatedHeaderHeight
          }
        }], {
          useNativeDriver,
          listener: e => {
            if (_reactNative.Platform.OS === 'android' && (options.headerBackground != null || options.headerTransparent)) {
              // FIXME: On Android, we get 0 if the header is translucent
              // So we set a default height in that case
              setHeaderHeight(ANDROID_DEFAULT_HEADER_HEIGHT + topInset);
              return;
            }
            if (e.nativeEvent && typeof e.nativeEvent === 'object' && 'headerHeight' in e.nativeEvent && typeof e.nativeEvent.headerHeight === 'number') {
              const headerHeight = e.nativeEvent.headerHeight + headerHeightCorrectionOffset;

              // Only debounce if header has large title or search bar
              // As it's the only case where the header height can change frequently
              const doesHeaderAnimate = _reactNative.Platform.OS === 'ios' && (options.headerLargeTitle || options.headerSearchBarOptions);
              if (doesHeaderAnimate) {
                setHeaderHeightDebounced(headerHeight);
              } else {
                setHeaderHeight(headerHeight);
              }
            }
          }
        }),
        contentStyle: [presentation !== 'transparentModal' && presentation !== 'containedTransparentModal' && {
          backgroundColor: colors.background
        }, contentStyle],
        headerConfig: headerConfig,
        unstable_sheetFooter: unstable_sheetFooter
        // When ts-expect-error is added, it affects all the props below it
        // So we keep any props that need it at the end
        // Otherwise invalid props may not be caught by TypeScript
        // @ts-expect-error: `shouldFreeze` is not available in lower RNScreens versions
        ,
        shouldFreeze: shouldFreeze,
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_useAnimatedHeaderHeight.AnimatedHeaderHeightContext.Provider, {
          value: animatedHeaderHeight,
          children: /*#__PURE__*/(0, _jsxRuntime.jsxs)(_elements.HeaderHeightContext.Provider, {
            value: headerShown !== false ? headerHeight : parentHeaderHeight ?? 0,
            children: [headerBackground != null ?
            /*#__PURE__*/
            /**
             * To show a custom header background, we render it at the top of the screen below the header
             * The header also needs to be positioned absolutely (with `translucent` style)
             */
            (0, _jsxRuntime.jsx)(_reactNative.View, {
              style: [styles.background, headerTransparent ? styles.translucent : null, {
                height: headerHeight
              }],
              children: headerBackground()
            }) : null, header !== undefined && headerShown !== false ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
              onLayout: e => {
                const headerHeight = e.nativeEvent.layout.height;
                setHeaderHeight(headerHeight);
                rawAnimatedHeaderHeight.setValue(headerHeight);
              },
              style: [styles.header, headerTransparent ? styles.absolute : null],
              children: header({
                back: headerBack,
                options,
                route,
                navigation
              })
            }) : null, /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.HeaderShownContext.Provider, {
              value: isParentHeaderShown || headerShown !== false,
              children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.HeaderBackContext.Provider, {
                value: headerBack,
                children: render()
              })
            })]
          })
        })
      }, route.key)
    })
  });
};
function NativeStackView({
  state,
  navigation,
  descriptors,
  describe
}) {
  const {
    setNextDismissedKey
  } = (0, _useDismissedRouteError.useDismissedRouteError)(state);
  (0, _useInvalidPreventRemoveError.useInvalidPreventRemoveError)(descriptors);
  const modalRouteKeys = (0, _getModalRoutesKeys.getModalRouteKeys)(state.routes, descriptors);
  const preloadedDescriptors = state.preloadedRoutes.reduce((acc, route) => {
    acc[route.key] = acc[route.key] || describe(route, true);
    return acc;
  }, {});
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.SafeAreaProviderCompat, {
    children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeScreens.ScreenStack, {
      style: styles.container,
      children: state.routes.concat(state.preloadedRoutes).map((route, index) => {
        const descriptor = descriptors[route.key] ?? preloadedDescriptors[route.key];
        const isFocused = state.index === index;
        const isBelowFocused = state.index - 1 === index;
        const previousKey = state.routes[index - 1]?.key;
        const nextKey = state.routes[index + 1]?.key;
        const previousDescriptor = previousKey ? descriptors[previousKey] : undefined;
        const nextDescriptor = nextKey ? descriptors[nextKey] : undefined;
        const isModal = modalRouteKeys.includes(route.key);
        const isPreloaded = preloadedDescriptors[route.key] !== undefined && descriptors[route.key] === undefined;

        // On Fabric, when screen is frozen, animated and reanimated values are not updated
        // due to component being unmounted. To avoid this, we don't freeze the previous screen there
        const shouldFreeze = isFabric() ? !isPreloaded && !isFocused && !isBelowFocused : !isPreloaded && !isFocused;
        return /*#__PURE__*/(0, _jsxRuntime.jsx)(SceneView, {
          index: index,
          focused: isFocused,
          shouldFreeze: shouldFreeze,
          descriptor: descriptor,
          previousDescriptor: previousDescriptor,
          nextDescriptor: nextDescriptor,
          isPresentationModal: isModal,
          isPreloaded: isPreloaded,
          onWillDisappear: () => {
            navigation.emit({
              type: 'transitionStart',
              data: {
                closing: true
              },
              target: route.key
            });
          },
          onWillAppear: () => {
            navigation.emit({
              type: 'transitionStart',
              data: {
                closing: false
              },
              target: route.key
            });
          },
          onAppear: () => {
            navigation.emit({
              type: 'transitionEnd',
              data: {
                closing: false
              },
              target: route.key
            });
          },
          onDisappear: () => {
            navigation.emit({
              type: 'transitionEnd',
              data: {
                closing: true
              },
              target: route.key
            });
          },
          onDismissed: event => {
            navigation.dispatch({
              ..._native.StackActions.pop(event.nativeEvent.dismissCount),
              source: route.key,
              target: state.key
            });
            setNextDismissedKey(route.key);
          },
          onHeaderBackButtonClicked: () => {
            navigation.dispatch({
              ..._native.StackActions.pop(),
              source: route.key,
              target: state.key
            });
          },
          onNativeDismissCancelled: event => {
            navigation.dispatch({
              ..._native.StackActions.pop(event.nativeEvent.dismissCount),
              source: route.key,
              target: state.key
            });
          },
          onGestureCancel: () => {
            navigation.emit({
              type: 'gestureCancel',
              target: route.key
            });
          },
          onSheetDetentChanged: event => {
            navigation.emit({
              type: 'sheetDetentChange',
              target: route.key,
              data: {
                index: event.nativeEvent.index,
                stable: event.nativeEvent.isStable
              }
            });
          }
        }, route.key);
      })
    })
  });
}
const styles = _reactNative.StyleSheet.create({
  container: {
    flex: 1
  },
  header: {
    zIndex: 1
  },
  absolute: {
    position: 'absolute',
    top: 0,
    start: 0,
    end: 0
  },
  translucent: {
    position: 'absolute',
    top: 0,
    start: 0,
    end: 0,
    zIndex: 1,
    elevation: 1
  },
  background: {
    overflow: 'hidden'
  }
});
//# sourceMappingURL=NativeStackView.native.js.map