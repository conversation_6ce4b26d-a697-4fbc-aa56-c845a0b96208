// Animation utilities for the app

/**
 * Clamps a value between min and max to prevent precision issues
 */
export const clampValue = (value: number, min: number = 0, max: number = 1): number => {
  return Math.max(min, Math.min(max, value));
};

/**
 * Rounds a value to prevent sub-pixel precision issues
 */
export const roundValue = (value: number, decimals: number = 3): number => {
  return Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals);
};

/**
 * Safe interpolation that prevents precision loss
 */
export const safeInterpolate = (
  value: number,
  inputRange: number[],
  outputRange: number[],
  extrapolate: 'clamp' | 'extend' = 'clamp'
): number => {
  const [inputMin, inputMax] = inputRange;
  const [outputMin, outputMax] = outputRange;
  
  let progress = (value - inputMin) / (inputMax - inputMin);
  
  if (extrapolate === 'clamp') {
    progress = clampValue(progress, 0, 1);
  }
  
  const result = outputMin + progress * (outputMax - outputMin);
  return roundValue(result);
};

/**
 * Safe opacity value for animations
 */
export const safeOpacity = (value: number): number => {
  return clampValue(roundValue(value), 0, 1);
};

/**
 * Safe transform value for animations
 */
export const safeTransform = (value: number): number => {
  return roundValue(value);
};
