"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.FooterComponent = FooterComponent;
var _react = _interopRequireDefault(require("react"));
var _reactNativeScreens = require("react-native-screens");
var _jsxRuntime = require("react/jsx-runtime");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
function FooterComponent({
  children
}) {
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeScreens.ScreenFooter, {
    collapsable: false,
    children: children
  });
}
//# sourceMappingURL=FooterComponent.js.map