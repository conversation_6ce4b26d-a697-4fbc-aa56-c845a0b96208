{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "CLIError", "constructor", "joi<PERSON><PERSON><PERSON>", "message", "details", "map", "error", "name", "path", "join", "type", "value", "JSON", "stringify", "context", "trim", "Error", "captureStackTrace"], "sources": ["../src/errors.ts"], "sourcesContent": ["import {CLIError} from '@react-native-community/cli-tools';\nimport {ValidationError} from 'joi';\n\nexport class JoiError extends CLIError {\n  constructor(joiError: ValidationError) {\n    const message = joiError.details\n      .map((error) => {\n        const name = error.path.join('.');\n        switch (error.type) {\n          case 'object.allowUnknown': {\n            const value = JSON.stringify(error.context && error.context.value);\n            return `\n              Unknown option ${name} with value \"${value}\" was found.\n              This is either a typing error or a user mistake. Fixing it will remove this message.\n            `;\n          }\n          default:\n            return error.message;\n        }\n      })\n      .join()\n      .trim();\n\n    super(message);\n    this.name = 'Config Validation Error';\n\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, JoiError);\n    }\n  }\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAGO,MAAMA,QAAQ,SAASC,oBAAQ,CAAC;EACrCC,WAAW,CAACC,QAAyB,EAAE;IACrC,MAAMC,OAAO,GAAGD,QAAQ,CAACE,OAAO,CAC7BC,GAAG,CAAEC,KAAK,IAAK;MACd,MAAMC,IAAI,GAAGD,KAAK,CAACE,IAAI,CAACC,IAAI,CAAC,GAAG,CAAC;MACjC,QAAQH,KAAK,CAACI,IAAI;QAChB,KAAK,qBAAqB;UAAE;YAC1B,MAAMC,KAAK,GAAGC,IAAI,CAACC,SAAS,CAACP,KAAK,CAACQ,OAAO,IAAIR,KAAK,CAACQ,OAAO,CAACH,KAAK,CAAC;YAClE,OAAQ;AACpB,+BAA+BJ,IAAK,gBAAeI,KAAM;AACzD;AACA,aAAa;UACH;QACA;UACE,OAAOL,KAAK,CAACH,OAAO;MAAC;IAE3B,CAAC,CAAC,CACDM,IAAI,EAAE,CACNM,IAAI,EAAE;IAET,KAAK,CAACZ,OAAO,CAAC;IACd,IAAI,CAACI,IAAI,GAAG,yBAAyB;IAErC,IAAIS,KAAK,CAACC,iBAAiB,EAAE;MAC3BD,KAAK,CAACC,iBAAiB,CAAC,IAAI,EAAElB,QAAQ,CAAC;IACzC;EACF;AACF;AAAC"}