{"version": 3, "names": ["getAdbPath", "process", "env", "ANDROID_HOME", "path", "join"], "sources": ["../../../src/commands/runAndroid/getAdbPath.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nimport path from 'path';\n\nfunction getAdbPath() {\n  return process.env.ANDROID_HOME\n    ? path.join(process.env.ANDROID_HOME, 'platform-tools', 'adb')\n    : 'adb';\n}\n\nexport default getAdbPath;\n"], "mappings": ";;;;;;AAOA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AAPxB;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASA,UAAU,GAAG;EACpB,OAAOC,OAAO,CAACC,GAAG,CAACC,YAAY,GAC3BC,eAAI,CAACC,IAAI,CAACJ,OAAO,CAACC,GAAG,CAACC,YAAY,EAAE,gBAAgB,EAAE,KAAK,CAAC,GAC5D,KAAK;AACX;AAAC,eAEcH,UAAU;AAAA"}