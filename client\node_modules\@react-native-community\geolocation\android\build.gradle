buildscript {
  repositories {
    google()
    mavenCentral()
  }

  dependencies {
    classpath 'com.android.tools.build:gradle:3.5.3'
  }
}

def getExtOrDefault(name) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : project.properties['RNCGeolocation_' + name]
}

def getExtOrIntegerDefault(name) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : (project.properties['RNCGeolocation_' + name]).toInteger()
}

def isNewArchitectureEnabled() {
  return rootProject.hasProperty("newArchEnabled") &&  rootProject.getProperty("newArchEnabled") == "true"
}

apply plugin: 'com.android.library'

if (isNewArchitectureEnabled()) {
  apply plugin: 'com.facebook.react'
}

android {
  def agpVersion = com.android.Version.ANDROID_GRADLE_PLUGIN_VERSION
  if (agpVersion.tokenize('.')[0].toInteger() >= 7) {
    namespace = "com.reactnativecommunity.geolocation"
  }

  compileSdkVersion getExtOrIntegerDefault('compileSdkVersion')

  defaultConfig {
    minSdkVersion getExtOrIntegerDefault('minSdkVersion')
    targetSdkVersion getExtOrIntegerDefault('targetSdkVersion')
    buildConfigField "boolean", "IS_NEW_ARCHITECTURE_ENABLED", isNewArchitectureEnabled().toString()
  }
  buildTypes {
    release {
      minifyEnabled false
    }
  }

  lintOptions {
    disable 'GradleCompatible'
  }

  compileOptions {
    sourceCompatibility JavaVersion.VERSION_1_8
    targetCompatibility JavaVersion.VERSION_1_8
  }
  sourceSets {
    main {
      if (isNewArchitectureEnabled()) {
          java.srcDirs += ['src/turbo']
      } else {
          java.srcDirs += ['src/legacy']
      }
    }
  }
}

repositories {
  mavenCentral()
  google()

  def found = false
  def defaultDir = null
  def androidSourcesName = 'React Native sources'

  if (rootProject.ext.has('reactNativeAndroidRoot')) {
    defaultDir = rootProject.ext.get('reactNativeAndroidRoot')
  } else {
    defaultDir = new File(
      projectDir,
      '/../../../node_modules/react-native/android'
    )
  }

  if (defaultDir.exists()) {
    maven {
      url defaultDir.toString()
      name androidSourcesName
    }

    logger.info(":${project.name}:reactNativeAndroidRoot ${defaultDir.canonicalPath}")
    found = true
  } else {
    def parentDir = rootProject.projectDir

    1.upto(5, {
      if (found) return true
      parentDir = parentDir.parentFile

      def androidSourcesDir = new File(
        parentDir,
        'node_modules/react-native'
      )

      def androidPrebuiltBinaryDir = new File(
        parentDir,
        'node_modules/react-native/android'
      )

      if (androidPrebuiltBinaryDir.exists()) {
        maven {
          url androidPrebuiltBinaryDir.toString()
          name androidSourcesName
        }

        logger.info(":${project.name}:reactNativeAndroidRoot ${androidPrebuiltBinaryDir.canonicalPath}")
        found = true
      } else if (androidSourcesDir.exists()) {
        maven {
          url androidSourcesDir.toString()
          name androidSourcesName
        }

        logger.info(":${project.name}:reactNativeAndroidRoot ${androidSourcesDir.canonicalPath}")
        found = true
      }
    })
  }

  if (!found) {
    throw new GradleException(
      "${project.name}: unable to locate React Native android sources. " +
      "Ensure you have you installed React Native as a dependency in your project and try again."
    )
  }
}

dependencies {
  //noinspection GradleDynamicVersion
  implementation 'com.facebook.react:react-native:+'
  implementation 'com.google.android.gms:play-services-location:21.0.1'
}

if (isNewArchitectureEnabled()) {
  react {
    jsRootDir = file("../js/")
    libraryName = "RNCGeolocation"
    codegenJavaPackageName = "com.reactnativecommunity.geolocation"
  }
}
