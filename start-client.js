const { spawn } = require('child_process');
const path = require('path');

console.log('📱 Starting ZAVE Client App...');
console.log('📁 Working directory:', process.cwd());

// Change to client directory
const clientPath = path.join(__dirname, 'client');
console.log('📂 Client path:', clientPath);

// Start the Metro bundler
console.log('🚀 Starting Metro bundler...');
const metroProcess = spawn('npx', ['react-native', 'start'], {
  cwd: clientPath,
  stdio: 'inherit',
  shell: true
});

metroProcess.on('error', (error) => {
  console.error('❌ Failed to start Metro bundler:', error);
});

metroProcess.on('close', (code) => {
  console.log(`🔴 Metro bundler exited with code ${code}`);
});

console.log('✅ Metro bundler startup initiated...');
console.log('🌐 Metro bundler should be available at: http://localhost:8081');
console.log('📱 Now you can run "npm run android" or "npm run ios" in the client directory');
console.log('🔗 Client app will connect to admin server at: http://localhost:5002');
