import mongoose from 'mongoose';

const branchSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true
  },
  description: {
    type: String
  },
  address: {
    type: String
  },
  city: {
    type: String
  },
  state: {
    type: String
  },
  pincode: {
    type: String
  },
  phone: {
    type: String
  },
  location: {
    lat: {
      type: Number
    },
    lng: {
      type: Number
    }
  },
  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'active'
  },
  vendorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vendor',
    required: true
  },
  deliveryPartners: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DeliveryPartner'
  }],
  createdAt: {
    type: Date,
    default: Date.now
  }
});

export const Branch = mongoose.model('Branch', branchSchema);

export default Branch;
