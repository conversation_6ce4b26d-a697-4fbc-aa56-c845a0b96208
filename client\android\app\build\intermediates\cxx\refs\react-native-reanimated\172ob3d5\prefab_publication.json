{"installationFolder": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\prefab_package\\debug\\prefab", "gradlePath": ":react-native-reanimated", "packageInfo": {"packageName": "react-native-reanimated", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "reanimated", "moduleHeaders": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\reanimated", "moduleExportLibraries": [], "abis": [{"abiName": "arm64-v8a", "abiApi": 24, "abiNdkMajor": 26, "abiStl": "c++_shared", "abiLibrary": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\2a2e6e6h\\obj\\arm64-v8a\\libreanimated.so", "abiAndroidGradleBuildJsonFile": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2a2e6e6h\\arm64-v8a\\android_gradle_build.json"}]}, {"moduleName": "worklets", "moduleHeaders": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\worklets", "moduleExportLibraries": [], "abis": [{"abiName": "arm64-v8a", "abiApi": 24, "abiNdkMajor": 26, "abiStl": "c++_shared", "abiLibrary": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\2a2e6e6h\\obj\\arm64-v8a\\libworklets.so", "abiAndroidGradleBuildJsonFile": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2a2e6e6h\\arm64-v8a\\android_gradle_build.json"}]}]}}