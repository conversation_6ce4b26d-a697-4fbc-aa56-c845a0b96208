import { Platform } from "react-native"

// For development with Admin server (now connected to Admin backend)
export const BASE_URL = Platform.OS==='android' ? 'http://********:3000/api': 'http://localhost:3000/api'
export const SOCKET_URL =  Platform.OS==='android' ? 'http://********:3000': 'http://localhost:3000'

// Google Maps API key - replace with your actual API key
export const GOOGLE_MAP_API = "AIzaSyB3-FJ4nRfZz19GUndm70CKxH94-rZ9uKo"

// This will be dynamically set when we get branch data from the server
// For now, we'll use a placeholder that will be replaced by actual branch ID
export const BRANCH_ID = 'branch_123456789'

// USE YOUR NETWORK IP OR HOSTED URL
// export const BASE_URL = 'http://***********:3000/api'
// export const SOCKET_URL = 'http://***********:3000'

