{"version": 3, "names": ["searchPlacesForCJS", "searchPlaces", "parseUserConfig", "searchResult", "config", "undefined", "result", "schema", "projectConfig", "validate", "error", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "readConfigFromDiskAsync", "rootFolder", "explorer", "cosmiconfig", "stopDir", "search", "readConfigFromDisk", "cosmiconfigSync", "parseDependencyConfig", "dependencyName", "emptyDependencyConfig", "dependencyConfig", "abort<PERSON><PERSON><PERSON>", "validationError", "logger", "warn", "inlineString", "chalk", "bold", "message", "readDependencyConfigFromDiskAsync", "readDependencyConfigFromDisk", "dependency", "platforms", "commands"], "sources": ["../src/readConfigFromDisk.ts"], "sourcesContent": ["import type {CosmiconfigResult} from 'cosmiconfig';\nimport {cosmiconfig, cosmiconfigSync} from 'cosmiconfig';\nimport {JoiError} from './errors';\nimport * as schema from './schema';\nimport type {\n  UserConfig,\n  UserDependencyConfig,\n} from '@react-native-community/cli-types';\nimport {logger, inlineString} from '@react-native-community/cli-tools';\nimport chalk from 'chalk';\n\n/**\n * Places to look for the configuration file.\n * Note that we need different sets for CJS and ESM because the synchronous\n * version cannot contain `.mjs` files. Doing so will cause \"Error: Missing\n * loader for extension\" during runtime.\n */\nconst searchPlacesForCJS = [\n  'react-native.config.js',\n  'react-native.config.cjs',\n  'react-native.config.ts',\n];\nconst searchPlaces = [...searchPlacesForCJS, 'react-native.config.mjs'];\n\nfunction parseUserConfig(searchResult: CosmiconfigResult): UserConfig {\n  const config = searchResult ? searchResult.config : undefined;\n  const result = schema.projectConfig.validate(config);\n\n  if (result.error) {\n    throw new JoiError(result.error);\n  }\n\n  return result.value as UserConfig;\n}\n\n/**\n * Reads a project configuration as defined by the user in the current\n * workspace.\n */\nexport async function readConfigFromDiskAsync(\n  rootFolder: string,\n): Promise<UserConfig> {\n  const explorer = cosmiconfig('react-native', {\n    stopDir: rootFolder,\n    searchPlaces,\n  });\n\n  const searchResult = await explorer.search(rootFolder);\n  return parseUserConfig(searchResult);\n}\n\n/**\n * Reads a project configuration as defined by the user in the current\n * workspace synchronously.\n */\n\nexport function readConfigFromDisk(rootFolder: string): UserConfig {\n  const explorer = cosmiconfigSync('react-native', {\n    stopDir: rootFolder,\n    searchPlaces: searchPlacesForCJS,\n  });\n\n  const searchResult = explorer.search(rootFolder);\n  return parseUserConfig(searchResult);\n}\n\nfunction parseDependencyConfig(\n  dependencyName: string,\n  searchResult: CosmiconfigResult,\n): UserDependencyConfig {\n  const config = searchResult ? searchResult.config : emptyDependencyConfig;\n\n  const result = schema.dependencyConfig.validate(config, {abortEarly: false});\n\n  if (result.error) {\n    const validationError = new JoiError(result.error);\n    logger.warn(\n      inlineString(`\n        Package ${chalk.bold(\n          dependencyName,\n        )} contains invalid configuration: ${chalk.bold(\n        validationError.message,\n      )}.\n\n      Please verify it's properly linked using \"npx react-native config\" command and contact the package maintainers about this.`),\n    );\n  }\n\n  return result.value as UserDependencyConfig;\n}\n\n/**\n * Reads a dependency configuration as defined by the developer\n * inside `node_modules`.\n */\nexport async function readDependencyConfigFromDiskAsync(\n  rootFolder: string,\n  dependencyName: string,\n): Promise<UserDependencyConfig> {\n  const explorer = cosmiconfig('react-native', {\n    stopDir: rootFolder,\n    searchPlaces,\n  });\n\n  const searchResult = await explorer.search(rootFolder);\n  return parseDependencyConfig(dependencyName, searchResult);\n}\n\n/**\n * Reads a dependency configuration as defined by the developer\n * inside `node_modules` synchronously.\n */\n\nexport function readDependencyConfigFromDisk(\n  rootFolder: string,\n  dependencyName: string,\n): UserDependencyConfig {\n  const explorer = cosmiconfigSync('react-native', {\n    stopDir: rootFolder,\n    searchPlaces: searchPlacesForCJS,\n  });\n\n  const searchResult = explorer.search(rootFolder);\n  return parseDependencyConfig(dependencyName, searchResult);\n}\n\nconst emptyDependencyConfig = {\n  dependency: {\n    platforms: {},\n  },\n  commands: [],\n  platforms: {},\n};\n"], "mappings": ";;;;;;;;;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AAKA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA0B;AAAA;AAAA;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,kBAAkB,GAAG,CACzB,wBAAwB,EACxB,yBAAyB,EACzB,wBAAwB,CACzB;AACD,MAAMC,YAAY,GAAG,CAAC,GAAGD,kBAAkB,EAAE,yBAAyB,CAAC;AAEvE,SAASE,eAAe,CAACC,YAA+B,EAAc;EACpE,MAAMC,MAAM,GAAGD,YAAY,GAAGA,YAAY,CAACC,MAAM,GAAGC,SAAS;EAC7D,MAAMC,MAAM,GAAGC,MAAM,CAACC,aAAa,CAACC,QAAQ,CAACL,MAAM,CAAC;EAEpD,IAAIE,MAAM,CAACI,KAAK,EAAE;IAChB,MAAM,IAAIC,gBAAQ,CAACL,MAAM,CAACI,KAAK,CAAC;EAClC;EAEA,OAAOJ,MAAM,CAACM,KAAK;AACrB;;AAEA;AACA;AACA;AACA;AACO,eAAeC,uBAAuB,CAC3CC,UAAkB,EACG;EACrB,MAAMC,QAAQ,GAAG,IAAAC,0BAAW,EAAC,cAAc,EAAE;IAC3CC,OAAO,EAAEH,UAAU;IACnBb;EACF,CAAC,CAAC;EAEF,MAAME,YAAY,GAAG,MAAMY,QAAQ,CAACG,MAAM,CAACJ,UAAU,CAAC;EACtD,OAAOZ,eAAe,CAACC,YAAY,CAAC;AACtC;;AAEA;AACA;AACA;AACA;;AAEO,SAASgB,kBAAkB,CAACL,UAAkB,EAAc;EACjE,MAAMC,QAAQ,GAAG,IAAAK,8BAAe,EAAC,cAAc,EAAE;IAC/CH,OAAO,EAAEH,UAAU;IACnBb,YAAY,EAAED;EAChB,CAAC,CAAC;EAEF,MAAMG,YAAY,GAAGY,QAAQ,CAACG,MAAM,CAACJ,UAAU,CAAC;EAChD,OAAOZ,eAAe,CAACC,YAAY,CAAC;AACtC;AAEA,SAASkB,qBAAqB,CAC5BC,cAAsB,EACtBnB,YAA+B,EACT;EACtB,MAAMC,MAAM,GAAGD,YAAY,GAAGA,YAAY,CAACC,MAAM,GAAGmB,qBAAqB;EAEzE,MAAMjB,MAAM,GAAGC,MAAM,CAACiB,gBAAgB,CAACf,QAAQ,CAACL,MAAM,EAAE;IAACqB,UAAU,EAAE;EAAK,CAAC,CAAC;EAE5E,IAAInB,MAAM,CAACI,KAAK,EAAE;IAChB,MAAMgB,eAAe,GAAG,IAAIf,gBAAQ,CAACL,MAAM,CAACI,KAAK,CAAC;IAClDiB,kBAAM,CAACC,IAAI,CACT,IAAAC,wBAAY,EAAE;AACpB,kBAAkBC,gBAAK,CAACC,IAAI,CAClBT,cAAc,CACd,oCAAmCQ,gBAAK,CAACC,IAAI,CAC/CL,eAAe,CAACM,OAAO,CACvB;AACR;AACA,iIAAiI,CAAC,CAC7H;EACH;EAEA,OAAO1B,MAAM,CAACM,KAAK;AACrB;;AAEA;AACA;AACA;AACA;AACO,eAAeqB,iCAAiC,CACrDnB,UAAkB,EAClBQ,cAAsB,EACS;EAC/B,MAAMP,QAAQ,GAAG,IAAAC,0BAAW,EAAC,cAAc,EAAE;IAC3CC,OAAO,EAAEH,UAAU;IACnBb;EACF,CAAC,CAAC;EAEF,MAAME,YAAY,GAAG,MAAMY,QAAQ,CAACG,MAAM,CAACJ,UAAU,CAAC;EACtD,OAAOO,qBAAqB,CAACC,cAAc,EAAEnB,YAAY,CAAC;AAC5D;;AAEA;AACA;AACA;AACA;;AAEO,SAAS+B,4BAA4B,CAC1CpB,UAAkB,EAClBQ,cAAsB,EACA;EACtB,MAAMP,QAAQ,GAAG,IAAAK,8BAAe,EAAC,cAAc,EAAE;IAC/CH,OAAO,EAAEH,UAAU;IACnBb,YAAY,EAAED;EAChB,CAAC,CAAC;EAEF,MAAMG,YAAY,GAAGY,QAAQ,CAACG,MAAM,CAACJ,UAAU,CAAC;EAChD,OAAOO,qBAAqB,CAACC,cAAc,EAAEnB,YAAY,CAAC;AAC5D;AAEA,MAAMoB,qBAAqB,GAAG;EAC5BY,UAAU,EAAE;IACVC,SAAS,EAAE,CAAC;EACd,CAAC;EACDC,QAAQ,EAAE,EAAE;EACZD,SAAS,EAAE,CAAC;AACd,CAAC"}