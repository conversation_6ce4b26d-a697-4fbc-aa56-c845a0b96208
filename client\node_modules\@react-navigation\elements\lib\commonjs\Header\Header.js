"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Header = Header;
var _native = require("@react-navigation/native");
var _color = _interopRequireDefault(require("color"));
var React = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _reactNativeSafeAreaContext = require("react-native-safe-area-context");
var _searchIcon = _interopRequireDefault(require("../assets/search-icon.png"));
var _getDefaultHeaderHeight = require("./getDefaultHeaderHeight.js");
var _HeaderBackButton = require("./HeaderBackButton.js");
var _HeaderBackground = require("./HeaderBackground.js");
var _HeaderButton = require("./HeaderButton.js");
var _HeaderIcon = require("./HeaderIcon.js");
var _HeaderSearchBar = require("./HeaderSearchBar.js");
var _HeaderShownContext = require("./HeaderShownContext.js");
var _HeaderTitle = require("./HeaderTitle.js");
var _jsxRuntime = require("react/jsx-runtime");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
// Width of the screen in split layout on portrait mode on iPad Mini
const IPAD_MINI_MEDIUM_WIDTH = 414;
const warnIfHeaderStylesDefined = styles => {
  Object.keys(styles).forEach(styleProp => {
    const value = styles[styleProp];
    if (styleProp === 'position' && value === 'absolute') {
      console.warn("position: 'absolute' is not supported on headerStyle. If you would like to render content under the header, use the 'headerTransparent' option.");
    } else if (value !== undefined) {
      console.warn(`${styleProp} was given a value of ${value}, this has no effect on headerStyle.`);
    }
  });
};
function Header(props) {
  const insets = (0, _reactNativeSafeAreaContext.useSafeAreaInsets)();
  const frame = (0, _reactNativeSafeAreaContext.useSafeAreaFrame)();
  const {
    colors
  } = (0, _native.useTheme)();
  const navigation = (0, _native.useNavigation)();
  const isParentHeaderShown = React.useContext(_HeaderShownContext.HeaderShownContext);
  const [searchBarVisible, setSearchBarVisible] = React.useState(false);
  const [titleLayout, setTitleLayout] = React.useState(undefined);
  const onTitleLayout = e => {
    const {
      height,
      width
    } = e.nativeEvent.layout;
    setTitleLayout(titleLayout => {
      if (titleLayout && height === titleLayout.height && width === titleLayout.width) {
        return titleLayout;
      }
      return {
        height,
        width
      };
    });
  };
  const {
    // eslint-disable-next-line @eslint-react/no-unstable-default-props
    layout = frame,
    modal = false,
    back,
    title,
    headerTitle: customTitle,
    headerTitleAlign = _reactNative.Platform.OS === 'ios' ? 'center' : 'left',
    headerLeft = back ? props => /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderBackButton.HeaderBackButton, {
      ...props
    }) : undefined,
    headerSearchBarOptions,
    headerTransparent,
    headerTintColor,
    headerBackground,
    headerRight,
    headerTitleAllowFontScaling: titleAllowFontScaling,
    headerTitleStyle: titleStyle,
    headerLeftContainerStyle: leftContainerStyle,
    headerRightContainerStyle: rightContainerStyle,
    headerTitleContainerStyle: titleContainerStyle,
    headerBackButtonDisplayMode = _reactNative.Platform.OS === 'ios' ? 'default' : 'minimal',
    headerBackTitleStyle,
    headerBackgroundContainerStyle: backgroundContainerStyle,
    headerStyle: customHeaderStyle,
    headerShadowVisible,
    headerPressColor,
    headerPressOpacity,
    // eslint-disable-next-line @eslint-react/no-unstable-default-props
    headerStatusBarHeight = isParentHeaderShown ? 0 : insets.top
  } = props;
  const defaultHeight = (0, _getDefaultHeaderHeight.getDefaultHeaderHeight)(layout, modal, headerStatusBarHeight);
  const {
    height = defaultHeight,
    minHeight,
    maxHeight,
    backgroundColor,
    borderBottomColor,
    borderBottomEndRadius,
    borderBottomLeftRadius,
    borderBottomRightRadius,
    borderBottomStartRadius,
    borderBottomWidth,
    borderColor,
    borderEndColor,
    borderEndWidth,
    borderLeftColor,
    borderLeftWidth,
    borderRadius,
    borderRightColor,
    borderRightWidth,
    borderStartColor,
    borderStartWidth,
    borderStyle,
    borderTopColor,
    borderTopEndRadius,
    borderTopLeftRadius,
    borderTopRightRadius,
    borderTopStartRadius,
    borderTopWidth,
    borderWidth,
    boxShadow,
    elevation,
    shadowColor,
    shadowOffset,
    shadowOpacity,
    shadowRadius,
    opacity,
    transform,
    ...unsafeStyles
  } = _reactNative.StyleSheet.flatten(customHeaderStyle || {});
  if (process.env.NODE_ENV !== 'production') {
    warnIfHeaderStylesDefined(unsafeStyles);
  }
  const safeStyles = {
    backgroundColor,
    borderBottomColor,
    borderBottomEndRadius,
    borderBottomLeftRadius,
    borderBottomRightRadius,
    borderBottomStartRadius,
    borderBottomWidth,
    borderColor,
    borderEndColor,
    borderEndWidth,
    borderLeftColor,
    borderLeftWidth,
    borderRadius,
    borderRightColor,
    borderRightWidth,
    borderStartColor,
    borderStartWidth,
    borderStyle,
    borderTopColor,
    borderTopEndRadius,
    borderTopLeftRadius,
    borderTopRightRadius,
    borderTopStartRadius,
    borderTopWidth,
    borderWidth,
    boxShadow,
    elevation,
    shadowColor,
    shadowOffset,
    shadowOpacity,
    shadowRadius,
    opacity,
    transform
  };

  // Setting a property to undefined triggers default style
  // So we need to filter them out
  // Users can use `null` instead
  for (const styleProp in safeStyles) {
    // @ts-expect-error: typescript wrongly complains that styleProp cannot be used to index safeStyles
    if (safeStyles[styleProp] === undefined) {
      // @ts-expect-error don't need to care about index signature for deletion
      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete
      delete safeStyles[styleProp];
    }
  }
  const backgroundStyle = {
    ...(headerTransparent && {
      backgroundColor: 'transparent'
    }),
    ...((headerTransparent || headerShadowVisible === false) && {
      borderBottomWidth: 0,
      ..._reactNative.Platform.select({
        android: {
          elevation: 0
        },
        web: {
          boxShadow: 'none'
        },
        default: {
          shadowOpacity: 0
        }
      })
    }),
    ...safeStyles
  };
  const iconTintColor = headerTintColor ?? _reactNative.Platform.select({
    ios: colors.primary,
    default: colors.text
  });
  const leftButton = headerLeft ? headerLeft({
    tintColor: iconTintColor,
    pressColor: headerPressColor,
    pressOpacity: headerPressOpacity,
    displayMode: headerBackButtonDisplayMode,
    titleLayout,
    screenLayout: layout,
    canGoBack: Boolean(back),
    onPress: back ? navigation.goBack : undefined,
    label: back?.title,
    labelStyle: headerBackTitleStyle,
    href: back?.href
  }) : null;
  const rightButton = headerRight ? headerRight({
    tintColor: iconTintColor,
    pressColor: headerPressColor,
    pressOpacity: headerPressOpacity,
    canGoBack: Boolean(back)
  }) : null;
  const headerTitle = typeof customTitle !== 'function' ? props => /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderTitle.HeaderTitle, {
    ...props
  }) : customTitle;
  return /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.Animated.View, {
    pointerEvents: "box-none",
    style: [{
      height,
      minHeight,
      maxHeight,
      opacity,
      transform
    }],
    children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.View, {
      pointerEvents: "box-none",
      style: [_reactNative.StyleSheet.absoluteFill, backgroundContainerStyle],
      children: headerBackground ? headerBackground({
        style: backgroundStyle
      }) : /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderBackground.HeaderBackground, {
        pointerEvents:
        // Allow touch through the header when background color is transparent
        headerTransparent && (backgroundStyle.backgroundColor === 'transparent' || (0, _color.default)(backgroundStyle.backgroundColor).alpha() === 0) ? 'none' : 'auto',
        style: backgroundStyle
      })
    }), /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
      pointerEvents: "none",
      style: {
        height: headerStatusBarHeight
      }
    }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.View, {
      pointerEvents: "box-none",
      style: [styles.content, _reactNative.Platform.OS === 'ios' && frame.width >= IPAD_MINI_MEDIUM_WIDTH ? styles.large : null],
      children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.View, {
        pointerEvents: "box-none",
        style: [styles.start, headerTitleAlign === 'center' && styles.expand, {
          marginStart: insets.left
        }, leftContainerStyle],
        children: leftButton
      }), _reactNative.Platform.OS === 'ios' || !searchBarVisible ? /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
        children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Animated.View, {
          pointerEvents: "box-none",
          style: [styles.title, {
            // Avoid the title from going offscreen or overlapping buttons
            maxWidth: headerTitleAlign === 'center' ? layout.width - ((leftButton ? headerBackButtonDisplayMode !== 'minimal' ? 80 : 32 : 16) + (rightButton || headerSearchBarOptions ? 16 : 0) + Math.max(insets.left, insets.right)) * 2 : layout.width - ((leftButton ? 52 : 16) + (rightButton || headerSearchBarOptions ? 52 : 16) + insets.left - insets.right)
          }, headerTitleAlign === 'left' && leftButton ? {
            marginStart: 4
          } : {
            marginHorizontal: 16
          }, titleContainerStyle],
          children: headerTitle({
            children: title,
            allowFontScaling: titleAllowFontScaling,
            tintColor: headerTintColor,
            onLayout: onTitleLayout,
            style: titleStyle
          })
        }), /*#__PURE__*/(0, _jsxRuntime.jsxs)(_reactNative.Animated.View, {
          pointerEvents: "box-none",
          style: [styles.end, styles.expand, {
            marginEnd: insets.right
          }, rightContainerStyle],
          children: [rightButton, headerSearchBarOptions ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderButton.HeaderButton, {
            tintColor: iconTintColor,
            pressColor: headerPressColor,
            pressOpacity: headerPressOpacity,
            onPress: () => setSearchBarVisible(true),
            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderIcon.HeaderIcon, {
              source: _searchIcon.default,
              tintColor: iconTintColor
            })
          }) : null]
        })]
      }) : null, _reactNative.Platform.OS === 'ios' || searchBarVisible ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_HeaderSearchBar.HeaderSearchBar, {
        ...headerSearchBarOptions,
        visible: searchBarVisible,
        onClose: () => {
          setSearchBarVisible(false);
          headerSearchBarOptions?.onClose?.();
        },
        style: [_reactNative.Platform.OS === 'ios' ? [_reactNative.StyleSheet.absoluteFill, {
          backgroundColor: colors.card
        }] : !leftButton && {
          marginStart: 8
        }]
      }) : null]
    })]
  });
}
const styles = _reactNative.StyleSheet.create({
  content: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'stretch'
  },
  large: {
    marginHorizontal: 5
  },
  title: {
    justifyContent: 'center'
  },
  start: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start'
  },
  end: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end'
  },
  expand: {
    flexGrow: 1,
    flexBasis: 0
  }
});
//# sourceMappingURL=Header.js.map