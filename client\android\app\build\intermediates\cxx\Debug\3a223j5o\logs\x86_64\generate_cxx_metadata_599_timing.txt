# C/C++ build system timings
generate_cxx_metadata
  [gap of 13ms]
  create-invalidation-state 11ms
  generate-prefab-packages
    [gap of 17ms]
    exec-prefab 525ms
    [gap of 32ms]
  generate-prefab-packages completed in 574ms
  execute-generate-process
    exec-configure 518ms
    [gap of 49ms]
  execute-generate-process completed in 567ms
  [gap of 39ms]
generate_cxx_metadata completed in 1205ms

