{"version": 3, "names": ["resolveReactNativePath", "root", "resolveNodeModuleDir", "_ignored", "CLIError"], "sources": ["../src/resolveReactNativePath.ts"], "sourcesContent": ["import {\n  CLIError,\n  resolveNodeModuleDir,\n} from '@react-native-community/cli-tools';\n\n/**\n * Finds path to React Native inside `node_modules` or throws\n * an error otherwise.\n */\nexport default function resolveReactNativePath(root: string) {\n  try {\n    return resolveNodeModuleDir(root, 'react-native');\n  } catch (_ignored) {\n    throw new CLIError(`\n      Unable to find React Native files looking up from ${root}. Make sure \"react-native\" module is installed\n      in your project dependencies.\n\n      If you are using React Native from a non-standard location, consider setting:\n      {\n        reactNativePath: \"./path/to/react-native\"\n      }\n      in your \\`react-native.config.js\\`.\n    `);\n  }\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAKA;AACA;AACA;AACA;AACe,SAASA,sBAAsB,CAACC,IAAY,EAAE;EAC3D,IAAI;IACF,OAAO,IAAAC,gCAAoB,EAACD,IAAI,EAAE,cAAc,CAAC;EACnD,CAAC,CAAC,OAAOE,QAAQ,EAAE;IACjB,MAAM,KAAIC,oBAAQ,EAAE;AACxB,0DAA0DH,IAAK;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,CAAC;EACJ;AACF"}