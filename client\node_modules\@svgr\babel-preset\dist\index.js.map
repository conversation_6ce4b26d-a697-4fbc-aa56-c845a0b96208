{"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\nimport { ConfigAPI } from '@babel/core'\nimport addJSXAttribute, {\n  Attribute,\n} from '@svgr/babel-plugin-add-jsx-attribute'\nimport removeJSXAttribute from '@svgr/babel-plugin-remove-jsx-attribute'\nimport removeJSXEmptyExpression from '@svgr/babel-plugin-remove-jsx-empty-expression'\nimport replaceJSXAttributeValue, {\n  Value,\n} from '@svgr/babel-plugin-replace-jsx-attribute-value'\nimport svgDynamicTitle from '@svgr/babel-plugin-svg-dynamic-title'\nimport svgEmDimensions from '@svgr/babel-plugin-svg-em-dimensions'\nimport transformReactNativeSVG from '@svgr/babel-plugin-transform-react-native-svg'\nimport transformSvgComponent, {\n  Options as TransformOptions,\n} from '@svgr/babel-plugin-transform-svg-component'\n\nexport interface Options extends TransformOptions {\n  ref?: boolean\n  titleProp?: boolean\n  descProp?: boolean\n  expandProps?: boolean | 'start' | 'end'\n  dimensions?: boolean\n  icon?: boolean | string | number\n  native?: boolean\n  svgProps?: { [key: string]: string }\n  replaceAttrValues?: { [key: string]: string }\n}\n\nconst getAttributeValue = (value: string) => {\n  const literal =\n    typeof value === 'string' && value.startsWith('{') && value.endsWith('}')\n  return { value: literal ? value.slice(1, -1) : value, literal }\n}\n\nconst propsToAttributes = (props: { [key: string]: string }): Attribute[] => {\n  return Object.keys(props).map((name) => {\n    const { literal, value } = getAttributeValue(props[name])\n    return { name, literal, value }\n  })\n}\n\nfunction replaceMapToValues(replaceMap: { [key: string]: string }): Value[] {\n  return Object.keys(replaceMap).map((value) => {\n    const { literal, value: newValue } = getAttributeValue(replaceMap[value])\n    return { value, newValue, literal }\n  })\n}\n\nconst plugin = (_: ConfigAPI, opts: Options) => {\n  let toRemoveAttributes = ['version']\n  let toAddAttributes: Attribute[] = []\n\n  if (opts.svgProps) {\n    toAddAttributes = [...toAddAttributes, ...propsToAttributes(opts.svgProps)]\n  }\n\n  if (opts.ref) {\n    toAddAttributes = [\n      ...toAddAttributes,\n      {\n        name: 'ref',\n        value: 'ref',\n        literal: true,\n      },\n    ]\n  }\n\n  if (opts.titleProp) {\n    toAddAttributes = [\n      ...toAddAttributes,\n      {\n        name: 'aria-labelledby',\n        value: 'titleId',\n        literal: true,\n      },\n    ]\n  }\n\n  if (opts.descProp) {\n    toAddAttributes = [\n      ...toAddAttributes,\n      {\n        name: 'aria-describedby',\n        value: 'descId',\n        literal: true,\n      },\n    ]\n  }\n\n  if (opts.expandProps) {\n    toAddAttributes = [\n      ...toAddAttributes,\n      {\n        name: 'props',\n        spread: true,\n        position:\n          opts.expandProps === 'start' || opts.expandProps === 'end'\n            ? opts.expandProps\n            : undefined,\n      },\n    ]\n  }\n\n  if (!opts.dimensions) {\n    toRemoveAttributes = [...toRemoveAttributes, 'width', 'height']\n  }\n\n  const plugins: any[] = [\n    [transformSvgComponent, opts],\n    ...(opts.icon !== false && opts.dimensions\n      ? [\n          [\n            svgEmDimensions,\n            opts.icon !== true\n              ? { width: opts.icon, height: opts.icon }\n              : opts.native\n              ? { width: 24, height: 24 }\n              : {},\n          ],\n        ]\n      : []),\n    [\n      removeJSXAttribute,\n      { elements: ['svg', 'Svg'], attributes: toRemoveAttributes },\n    ],\n    [\n      addJSXAttribute,\n      { elements: ['svg', 'Svg'], attributes: toAddAttributes },\n    ],\n    removeJSXEmptyExpression,\n  ]\n\n  if (opts.replaceAttrValues) {\n    plugins.push([\n      replaceJSXAttributeValue,\n      { values: replaceMapToValues(opts.replaceAttrValues) },\n    ])\n  }\n\n  if (opts.titleProp) {\n    plugins.push(svgDynamicTitle)\n  }\n\n  if (opts.descProp) {\n    plugins.push([svgDynamicTitle, { tag: 'desc' }, 'desc'])\n  }\n\n  if (opts.native) {\n    plugins.push(transformReactNativeSVG)\n  }\n\n  return { plugins }\n}\n\nexport default plugin\n"], "names": [], "mappings": ";;;;;;;;;;;AA6BA,MAAM,iBAAA,GAAoB,CAAC,KAAkB,KAAA;AAC3C,EAAM,MAAA,OAAA,GACJ,OAAO,KAAA,KAAU,QAAY,IAAA,KAAA,CAAM,WAAW,GAAG,CAAA,IAAK,KAAM,CAAA,QAAA,CAAS,GAAG,CAAA,CAAA;AAC1E,EAAO,OAAA,EAAE,OAAO,OAAU,GAAA,KAAA,CAAM,MAAM,CAAG,EAAA,CAAA,CAAE,CAAI,GAAA,KAAA,EAAO,OAAQ,EAAA,CAAA;AAChE,CAAA,CAAA;AAEA,MAAM,iBAAA,GAAoB,CAAC,KAAkD,KAAA;AAC3E,EAAA,OAAO,OAAO,IAAK,CAAA,KAAK,CAAE,CAAA,GAAA,CAAI,CAAC,IAAS,KAAA;AACtC,IAAA,MAAM,EAAE,OAAS,EAAA,KAAA,KAAU,iBAAkB,CAAA,KAAA,CAAM,IAAI,CAAC,CAAA,CAAA;AACxD,IAAO,OAAA,EAAE,IAAM,EAAA,OAAA,EAAS,KAAM,EAAA,CAAA;AAAA,GAC/B,CAAA,CAAA;AACH,CAAA,CAAA;AAEA,SAAS,mBAAmB,UAAgD,EAAA;AAC1E,EAAA,OAAO,OAAO,IAAK,CAAA,UAAU,CAAE,CAAA,GAAA,CAAI,CAAC,KAAU,KAAA;AAC5C,IAAM,MAAA,EAAE,SAAS,KAAO,EAAA,QAAA,KAAa,iBAAkB,CAAA,UAAA,CAAW,KAAK,CAAC,CAAA,CAAA;AACxE,IAAO,OAAA,EAAE,KAAO,EAAA,QAAA,EAAU,OAAQ,EAAA,CAAA;AAAA,GACnC,CAAA,CAAA;AACH,CAAA;AAEM,MAAA,MAAA,GAAS,CAAC,CAAA,EAAc,IAAkB,KAAA;AAC9C,EAAI,IAAA,kBAAA,GAAqB,CAAC,SAAS,CAAA,CAAA;AACnC,EAAA,IAAI,kBAA+B,EAAC,CAAA;AAEpC,EAAA,IAAI,KAAK,QAAU,EAAA;AACjB,IAAA,eAAA,GAAkB,CAAC,GAAG,eAAA,EAAiB,GAAG,iBAAkB,CAAA,IAAA,CAAK,QAAQ,CAAC,CAAA,CAAA;AAAA,GAC5E;AAEA,EAAA,IAAI,KAAK,GAAK,EAAA;AACZ,IAAkB,eAAA,GAAA;AAAA,MAChB,GAAG,eAAA;AAAA,MACH;AAAA,QACE,IAAM,EAAA,KAAA;AAAA,QACN,KAAO,EAAA,KAAA;AAAA,QACP,OAAS,EAAA,IAAA;AAAA,OACX;AAAA,KACF,CAAA;AAAA,GACF;AAEA,EAAA,IAAI,KAAK,SAAW,EAAA;AAClB,IAAkB,eAAA,GAAA;AAAA,MAChB,GAAG,eAAA;AAAA,MACH;AAAA,QACE,IAAM,EAAA,iBAAA;AAAA,QACN,KAAO,EAAA,SAAA;AAAA,QACP,OAAS,EAAA,IAAA;AAAA,OACX;AAAA,KACF,CAAA;AAAA,GACF;AAEA,EAAA,IAAI,KAAK,QAAU,EAAA;AACjB,IAAkB,eAAA,GAAA;AAAA,MAChB,GAAG,eAAA;AAAA,MACH;AAAA,QACE,IAAM,EAAA,kBAAA;AAAA,QACN,KAAO,EAAA,QAAA;AAAA,QACP,OAAS,EAAA,IAAA;AAAA,OACX;AAAA,KACF,CAAA;AAAA,GACF;AAEA,EAAA,IAAI,KAAK,WAAa,EAAA;AACpB,IAAkB,eAAA,GAAA;AAAA,MAChB,GAAG,eAAA;AAAA,MACH;AAAA,QACE,IAAM,EAAA,OAAA;AAAA,QACN,MAAQ,EAAA,IAAA;AAAA,QACR,QAAA,EACE,KAAK,WAAgB,KAAA,OAAA,IAAW,KAAK,WAAgB,KAAA,KAAA,GACjD,KAAK,WACL,GAAA,KAAA,CAAA;AAAA,OACR;AAAA,KACF,CAAA;AAAA,GACF;AAEA,EAAI,IAAA,CAAC,KAAK,UAAY,EAAA;AACpB,IAAA,kBAAA,GAAqB,CAAC,GAAG,kBAAoB,EAAA,OAAA,EAAS,QAAQ,CAAA,CAAA;AAAA,GAChE;AAEA,EAAA,MAAM,OAAiB,GAAA;AAAA,IACrB,CAAC,uBAAuB,IAAI,CAAA;AAAA,IAC5B,GAAI,IAAA,CAAK,IAAS,KAAA,KAAA,IAAS,KAAK,UAC5B,GAAA;AAAA,MACE;AAAA,QACE,eAAA;AAAA,QACA,KAAK,IAAS,KAAA,IAAA,GACV,EAAE,KAAO,EAAA,IAAA,CAAK,MAAM,MAAQ,EAAA,IAAA,CAAK,MACjC,GAAA,IAAA,CAAK,SACL,EAAE,KAAA,EAAO,IAAI,MAAQ,EAAA,EAAA,KACrB,EAAC;AAAA,OACP;AAAA,QAEF,EAAC;AAAA,IACL;AAAA,MACE,kBAAA;AAAA,MACA,EAAE,QAAU,EAAA,CAAC,OAAO,KAAK,CAAA,EAAG,YAAY,kBAAmB,EAAA;AAAA,KAC7D;AAAA,IACA;AAAA,MACE,eAAA;AAAA,MACA,EAAE,QAAU,EAAA,CAAC,OAAO,KAAK,CAAA,EAAG,YAAY,eAAgB,EAAA;AAAA,KAC1D;AAAA,IACA,wBAAA;AAAA,GACF,CAAA;AAEA,EAAA,IAAI,KAAK,iBAAmB,EAAA;AAC1B,IAAA,OAAA,CAAQ,IAAK,CAAA;AAAA,MACX,wBAAA;AAAA,MACA,EAAE,MAAA,EAAQ,kBAAmB,CAAA,IAAA,CAAK,iBAAiB,CAAE,EAAA;AAAA,KACtD,CAAA,CAAA;AAAA,GACH;AAEA,EAAA,IAAI,KAAK,SAAW,EAAA;AAClB,IAAA,OAAA,CAAQ,KAAK,eAAe,CAAA,CAAA;AAAA,GAC9B;AAEA,EAAA,IAAI,KAAK,QAAU,EAAA;AACjB,IAAQ,OAAA,CAAA,IAAA,CAAK,CAAC,eAAiB,EAAA,EAAE,KAAK,MAAO,EAAA,EAAG,MAAM,CAAC,CAAA,CAAA;AAAA,GACzD;AAEA,EAAA,IAAI,KAAK,MAAQ,EAAA;AACf,IAAA,OAAA,CAAQ,KAAK,uBAAuB,CAAA,CAAA;AAAA,GACtC;AAEA,EAAA,OAAO,EAAE,OAAQ,EAAA,CAAA;AACnB;;;;"}