[{"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/.cxx/Debug/235h5g65/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/../cpp -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\4c5fc1969699a9d0d43817d45840bc71\\node_modules\\react-native-screens\\cpp\\RNScreensTurboModule.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-screens\\cpp\\RNScreensTurboModule.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-screens\\cpp\\RNScreensTurboModule.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/.cxx/Debug/235h5g65/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/../cpp -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\4c5fc1969699a9d0d43817d45840bc71\\node_modules\\react-native-screens\\cpp\\RNSScreenRemovalListener.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-screens\\cpp\\RNSScreenRemovalListener.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-screens\\cpp\\RNSScreenRemovalListener.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/.cxx/Debug/235h5g65/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/../cpp -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\src\\main\\cpp\\jni-adapter.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\jni-adapter.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\jni-adapter.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/.cxx/Debug/235h5g65/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/../cpp -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\src\\main\\cpp\\NativeProxy.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\NativeProxy.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\NativeProxy.cpp"}, {"directory": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/.cxx/Debug/235h5g65/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DFOLLY_NO_CONFIG=1 -Drnscreens_EXPORTS -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/../cpp -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20 -o CMakeFiles\\rnscreens.dir\\src\\main\\cpp\\OnLoad.cpp.o -c D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\OnLoad.cpp", "file": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-screens\\android\\src\\main\\cpp\\OnLoad.cpp"}]