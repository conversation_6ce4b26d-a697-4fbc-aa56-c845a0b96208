{"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\nimport { ConfigAPI, types as t, NodePath } from '@babel/core'\n\nexport interface Options {\n  elements: string[]\n  attributes: string[]\n}\n\nconst removeJSXAttribute = (_: ConfigAPI, opts: Options) => ({\n  visitor: {\n    JSXOpeningElement(path: NodePath<t.JSXOpeningElement>) {\n      if (!t.isJSXIdentifier(path.node.name)) return\n      if (!opts.elements.includes(path.node.name.name)) return\n\n      // @ts-ignore\n      path.get('attributes').forEach((attribute) => {\n        if (\n          t.isJSXAttribute(attribute.node) &&\n          t.isJSXIdentifier(attribute.node.name) &&\n          opts.attributes.includes(attribute.node.name.name)\n        ) {\n          attribute.remove()\n        }\n      })\n    },\n  },\n})\n\nexport default removeJSXAttribute\n"], "names": ["t"], "mappings": ";;;;AAQM,MAAA,kBAAA,GAAqB,CAAC,CAAA,EAAc,IAAmB,MAAA;AAAA,EAC3D,OAAS,EAAA;AAAA,IACP,kBAAkB,IAAqC,EAAA;AACrD,MAAA,IAAI,CAACA,UAAA,CAAE,eAAgB,CAAA,IAAA,CAAK,KAAK,IAAI,CAAA;AAAG,QAAA,OAAA;AACxC,MAAA,IAAI,CAAC,IAAK,CAAA,QAAA,CAAS,SAAS,IAAK,CAAA,IAAA,CAAK,KAAK,IAAI,CAAA;AAAG,QAAA,OAAA;AAGlD,MAAA,IAAA,CAAK,GAAI,CAAA,YAAY,CAAE,CAAA,OAAA,CAAQ,CAAC,SAAc,KAAA;AAC5C,QAAA,IACEA,WAAE,cAAe,CAAA,SAAA,CAAU,IAAI,CAC/B,IAAAA,UAAA,CAAE,gBAAgB,SAAU,CAAA,IAAA,CAAK,IAAI,CAAA,IACrC,KAAK,UAAW,CAAA,QAAA,CAAS,UAAU,IAAK,CAAA,IAAA,CAAK,IAAI,CACjD,EAAA;AACA,UAAA,SAAA,CAAU,MAAO,EAAA,CAAA;AAAA,SACnB;AAAA,OACD,CAAA,CAAA;AAAA,KACH;AAAA,GACF;AACF,CAAA;;;;"}