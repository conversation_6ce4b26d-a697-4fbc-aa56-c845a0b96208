"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.createStaticNavigation = createStaticNavigation;
var _core = require("@react-navigation/core");
var React = _interopRequireWildcard(require("react"));
var _NavigationContainer = require("./NavigationContainer.js");
var _jsxRuntime = require("react/jsx-runtime");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/**
 * Create a navigation component from a static navigation config.
 * The returned component is a wrapper around `NavigationContainer`.
 *
 * @param tree Static navigation config.
 * @returns Navigation component to use in your app.
 */
function createStaticNavigation(tree) {
  const Component = (0, _core.createComponentForStaticNavigation)(tree, 'RootNavigator');
  function Navigation({
    linking,
    ...rest
  }, ref) {
    const linkingConfig = React.useMemo(() => {
      const screens = (0, _core.createPathConfigForStaticNavigation)(tree, {
        initialRouteName: linking?.config?.initialRouteName
      }, linking?.enabled === 'auto');
      if (!screens) return;
      return {
        path: linking?.config?.path,
        initialRouteName: linking?.config?.initialRouteName,
        screens
      };
    }, [linking?.enabled, linking?.config?.path, linking?.config?.initialRouteName]);
    const memoizedLinking = React.useMemo(() => {
      if (!linking) {
        return undefined;
      }
      const enabled = typeof linking.enabled === 'boolean' ? linking.enabled : linkingConfig?.screens != null;
      return {
        ...linking,
        enabled,
        config: linkingConfig
      };
    }, [linking, linkingConfig]);
    if (linking?.enabled === true && linkingConfig?.screens == null) {
      throw new Error('Linking is enabled but no linking configuration was found for the screens.\n\n' + 'To solve this:\n' + "- Specify a 'linking' property for the screens you want to link to.\n" + "- Or set 'linking.enabled' to 'auto' to generate paths automatically.\n\n" + 'See usage guide: https://reactnavigation.org/docs/static-configuration#linking');
    }
    return /*#__PURE__*/(0, _jsxRuntime.jsx)(_NavigationContainer.NavigationContainer, {
      ...rest,
      ref: ref,
      linking: memoizedLinking,
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(Component, {})
    });
  }
  return /*#__PURE__*/React.forwardRef(Navigation);
}
//# sourceMappingURL=createStaticNavigation.js.map