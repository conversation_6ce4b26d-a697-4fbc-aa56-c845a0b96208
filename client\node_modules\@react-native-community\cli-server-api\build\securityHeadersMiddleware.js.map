{"version": 3, "names": ["securityHeadersMiddleware", "options", "req", "res", "next", "host", "headers", "origin", "match", "RegExp", "startsWith", "Error", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["../src/securityHeadersMiddleware.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport http from 'http';\n\ntype MiddlewareOptions = {\n  host?: string;\n};\n\ntype MiddlewareFn = (\n  req: http.IncomingMessage,\n  res: http.ServerResponse,\n  next: (err?: any) => void,\n) => void;\n\nexport default function securityHeadersMiddleware(\n  options: MiddlewareOptions,\n): MiddlewareFn {\n  return (\n    req: http.IncomingMessage,\n    res: http.ServerResponse,\n    next: (err?: any) => void,\n  ) => {\n    const host = options.host ? options.host : 'localhost';\n    // Block any cross origin request.\n    if (\n      typeof req.headers.origin === 'string' &&\n      !req.headers.origin.match(new RegExp('^https?://' + host + ':')) &&\n      !req.headers.origin.startsWith('devtools://devtools')\n    ) {\n      next(\n        new Error(\n          'Unauthorized request from ' +\n            req.headers.origin +\n            '. This may happen because of a conflicting browser extension. Please try to disable it and try again.',\n        ),\n      );\n      return;\n    }\n\n    // Block MIME-type sniffing.\n    res.setHeader('X-Content-Type-Options', 'nosniff');\n\n    next();\n  };\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;AAae,SAASA,yBAAyB,CAC/CC,OAA0B,EACZ;EACd,OAAO,CACLC,GAAyB,EACzBC,GAAwB,EACxBC,IAAyB,KACtB;IACH,MAAMC,IAAI,GAAGJ,OAAO,CAACI,IAAI,GAAGJ,OAAO,CAACI,IAAI,GAAG,WAAW;IACtD;IACA,IACE,OAAOH,GAAG,CAACI,OAAO,CAACC,MAAM,KAAK,QAAQ,IACtC,CAACL,GAAG,CAACI,OAAO,CAACC,MAAM,CAACC,KAAK,CAAC,IAAIC,MAAM,CAAC,YAAY,GAAGJ,IAAI,GAAG,GAAG,CAAC,CAAC,IAChE,CAACH,GAAG,CAACI,OAAO,CAACC,MAAM,CAACG,UAAU,CAAC,qBAAqB,CAAC,EACrD;MACAN,IAAI,CACF,IAAIO,KAAK,CACP,4BAA4B,GAC1BT,GAAG,CAACI,OAAO,CAACC,MAAM,GAClB,uGAAuG,CAC1G,CACF;MACD;IACF;;IAEA;IACAJ,GAAG,CAACS,SAAS,CAAC,wBAAwB,EAAE,SAAS,CAAC;IAElDR,IAAI,EAAE;EACR,CAAC;AACH"}