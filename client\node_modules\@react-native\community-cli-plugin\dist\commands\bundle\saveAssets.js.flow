/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 * @oncall react_native
 */

import type { AssetData } from "metro/src/Assets";

declare function saveAssets(
  assets: $ReadOnlyArray<AssetData>,
  platform: string,
  assetsDest?: string,
  assetCatalogDest?: string
): Promise<void>;

declare export default typeof saveAssets;
