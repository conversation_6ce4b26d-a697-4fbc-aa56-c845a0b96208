{"version": 3, "names": ["indexPageMiddleware", "req", "res", "next", "method", "url", "<PERSON><PERSON><PERSON><PERSON>", "end", "fs", "readFileSync", "path", "join", "__dirname"], "sources": ["../src/indexPageMiddleware.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport http from 'http';\nimport fs from 'fs';\nimport path from 'path';\n\nexport default function indexPageMiddleware(\n  req: http.IncomingMessage,\n  res: http.ServerResponse,\n  next: (err?: any) => void,\n) {\n  if (req.method === 'GET' && req.url === '/') {\n    res.setHeader('Content-Type', 'text/html');\n    res.end(fs.readFileSync(path.join(__dirname, 'index.html')));\n  } else {\n    next();\n  }\n}\n"], "mappings": ";;;;;;AAOA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AARxB;AACA;AACA;AACA;AACA;AACA;;AAKe,SAASA,mBAAmB,CACzCC,GAAyB,EACzBC,GAAwB,EACxBC,IAAyB,EACzB;EACA,IAAIF,GAAG,CAACG,MAAM,KAAK,KAAK,IAAIH,GAAG,CAACI,GAAG,KAAK,GAAG,EAAE;IAC3CH,GAAG,CAACI,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC;IAC1CJ,GAAG,CAACK,GAAG,CAACC,aAAE,CAACC,YAAY,CAACC,eAAI,CAACC,IAAI,CAACC,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC;EAC9D,CAAC,MAAM;IACLT,IAAI,EAAE;EACR;AACF"}