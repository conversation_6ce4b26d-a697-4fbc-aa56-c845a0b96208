{"version": 3, "names": ["merge", "x", "y", "deepmerge", "arrayMerge", "_destinationArray", "sourceArray"], "sources": ["../src/merge.ts"], "sourcesContent": ["import deepmerge from 'deepmerge';\n\n/**\n * `deepmerge` concatenates arrays by default instead of overwriting them.\n * We define custom merging function for arrays to change that behaviour\n */\nexport default function merge<X, Y>(x: Partial<X>, y: Partial<Y>) {\n  return deepmerge(x, y, {\n    arrayMerge: (_destinationArray: any[], sourceArray: any[]): any[] =>\n      sourceArray,\n  });\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAkC;AAElC;AACA;AACA;AACA;AACe,SAASA,KAAK,CAAOC,CAAa,EAAEC,CAAa,EAAE;EAChE,OAAO,IAAAC,oBAAS,EAACF,CAAC,EAAEC,CAAC,EAAE;IACrBE,UAAU,EAAE,CAACC,iBAAwB,EAAEC,WAAkB,KACvDA;EACJ,CAAC,CAAC;AACJ"}