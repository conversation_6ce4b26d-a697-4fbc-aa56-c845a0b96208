{"version": 3, "names": ["name", "description", "func", "createBuild", "platformName", "examples", "desc", "cmd", "options", "getBuildOptions"], "sources": ["../../../src/commands/buildIOS/index.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {\n  getBuildOptions,\n  createBuild,\n} from '@react-native-community/cli-platform-apple';\n\nexport default {\n  name: 'build-ios',\n  description: 'builds your app for iOS platform',\n  func: createBuild({platformName: 'ios'}),\n  examples: [\n    {\n      desc: 'Build the app for all iOS devices in Release mode',\n      cmd: 'npx react-native build-ios --mode \"Release\"',\n    },\n  ],\n  options: getBuildOptions({platformName: 'ios'}),\n};\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AARA;AACA;AACA;AACA;AACA;AACA;AACA;AANA,eAae;EACbA,IAAI,EAAE,WAAW;EACjBC,WAAW,EAAE,kCAAkC;EAC/CC,IAAI,EAAE,IAAAC,+BAAW,EAAC;IAACC,YAAY,EAAE;EAAK,CAAC,CAAC;EACxCC,QAAQ,EAAE,CACR;IACEC,IAAI,EAAE,mDAAmD;IACzDC,GAAG,EAAE;EACP,CAAC,CACF;EACDC,OAAO,EAAE,IAAAC,mCAAe,EAAC;IAACL,YAAY,EAAE;EAAK,CAAC;AAChD,CAAC;AAAA"}