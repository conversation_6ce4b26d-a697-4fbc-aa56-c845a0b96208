{"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\nimport { types as t, NodePath, ConfigAPI } from '@babel/core'\n\nconst elements = ['svg', 'Svg']\n\nexport interface Options {\n  width: number | string\n  height: number | string\n}\n\nconst getValue = (raw: undefined | number | string) => {\n  if (raw === undefined) return t.stringLiteral('1em')\n  switch (typeof raw) {\n    case 'number':\n      return t.jsxExpressionContainer(t.numericLiteral(raw))\n    case 'string':\n      return t.stringLiteral(raw)\n    default:\n      return t.stringLiteral('1em')\n  }\n}\n\nconst plugin = (_: ConfigAPI, opts: Options) => ({\n  visitor: {\n    JSXOpeningElement(path: NodePath<t.JSXOpeningElement>) {\n      if (\n        !elements.some((element) =>\n          path.get('name').isJSXIdentifier({ name: element }),\n        )\n      )\n        return\n\n      const values = {\n        width: getValue(opts.width),\n        height: getValue(opts.height),\n      }\n      const requiredAttributes = Object.keys(values)\n\n      path.get('attributes').forEach((attributePath) => {\n        if (!attributePath.isJSXAttribute()) return\n        const namePath = attributePath.get('name')\n        if (!namePath.isJSXIdentifier()) return\n        const index = requiredAttributes.indexOf(namePath.node.name)\n\n        if (index === -1) return\n\n        const valuePath = attributePath.get('value')\n        valuePath.replaceWith(values[namePath.node.name as 'width' | 'height'])\n        requiredAttributes.splice(index, 1)\n      })\n\n      path.pushContainer(\n        'attributes',\n        requiredAttributes.map((attr) =>\n          t.jsxAttribute(\n            t.jsxIdentifier(attr),\n            values[attr as 'width' | 'height'],\n          ),\n        ),\n      )\n    },\n  },\n})\n\nexport default plugin\n"], "names": ["t"], "mappings": ";;;;AAGA,MAAM,QAAA,GAAW,CAAC,KAAA,EAAO,KAAK,CAAA,CAAA;AAO9B,MAAM,QAAA,GAAW,CAAC,GAAqC,KAAA;AACrD,EAAA,IAAI,GAAQ,KAAA,KAAA,CAAA;AAAW,IAAO,OAAAA,UAAA,CAAE,cAAc,KAAK,CAAA,CAAA;AACnD,EAAA,QAAQ,OAAO,GAAK;AAAA,IAClB,KAAK,QAAA;AACH,MAAA,OAAOA,UAAE,CAAA,sBAAA,CAAuBA,UAAE,CAAA,cAAA,CAAe,GAAG,CAAC,CAAA,CAAA;AAAA,IACvD,KAAK,QAAA;AACH,MAAO,OAAAA,UAAA,CAAE,cAAc,GAAG,CAAA,CAAA;AAAA,IAC5B;AACE,MAAO,OAAAA,UAAA,CAAE,cAAc,KAAK,CAAA,CAAA;AAAA,GAChC;AACF,CAAA,CAAA;AAEM,MAAA,MAAA,GAAS,CAAC,CAAA,EAAc,IAAmB,MAAA;AAAA,EAC/C,OAAS,EAAA;AAAA,IACP,kBAAkB,IAAqC,EAAA;AACrD,MAAA,IACE,CAAC,QAAS,CAAA,IAAA;AAAA,QAAK,CAAC,OACd,KAAA,IAAA,CAAK,GAAI,CAAA,MAAM,EAAE,eAAgB,CAAA,EAAE,IAAM,EAAA,OAAA,EAAS,CAAA;AAAA,OACpD;AAEA,QAAA,OAAA;AAEF,MAAA,MAAM,MAAS,GAAA;AAAA,QACb,KAAA,EAAO,QAAS,CAAA,IAAA,CAAK,KAAK,CAAA;AAAA,QAC1B,MAAA,EAAQ,QAAS,CAAA,IAAA,CAAK,MAAM,CAAA;AAAA,OAC9B,CAAA;AACA,MAAM,MAAA,kBAAA,GAAqB,MAAO,CAAA,IAAA,CAAK,MAAM,CAAA,CAAA;AAE7C,MAAA,IAAA,CAAK,GAAI,CAAA,YAAY,CAAE,CAAA,OAAA,CAAQ,CAAC,aAAkB,KAAA;AAChD,QAAI,IAAA,CAAC,cAAc,cAAe,EAAA;AAAG,UAAA,OAAA;AACrC,QAAM,MAAA,QAAA,GAAW,aAAc,CAAA,GAAA,CAAI,MAAM,CAAA,CAAA;AACzC,QAAI,IAAA,CAAC,SAAS,eAAgB,EAAA;AAAG,UAAA,OAAA;AACjC,QAAA,MAAM,KAAQ,GAAA,kBAAA,CAAmB,OAAQ,CAAA,QAAA,CAAS,KAAK,IAAI,CAAA,CAAA;AAE3D,QAAA,IAAI,KAAU,KAAA,CAAA,CAAA;AAAI,UAAA,OAAA;AAElB,QAAM,MAAA,SAAA,GAAY,aAAc,CAAA,GAAA,CAAI,OAAO,CAAA,CAAA;AAC3C,QAAA,SAAA,CAAU,WAAY,CAAA,MAAA,CAAO,QAAS,CAAA,IAAA,CAAK,IAA0B,CAAC,CAAA,CAAA;AACtE,QAAmB,kBAAA,CAAA,MAAA,CAAO,OAAO,CAAC,CAAA,CAAA;AAAA,OACnC,CAAA,CAAA;AAED,MAAK,IAAA,CAAA,aAAA;AAAA,QACH,YAAA;AAAA,QACA,kBAAmB,CAAA,GAAA;AAAA,UAAI,CAAC,SACtBA,UAAE,CAAA,YAAA;AAAA,YACAA,UAAA,CAAE,cAAc,IAAI,CAAA;AAAA,YACpB,OAAO,IAA0B,CAAA;AAAA,WACnC;AAAA,SACF;AAAA,OACF,CAAA;AAAA,KACF;AAAA,GACF;AACF,CAAA;;;;"}