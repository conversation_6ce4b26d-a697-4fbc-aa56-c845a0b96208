{"buildFiles": ["D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\src\\main\\jni\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\534pt692\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\.cxx\\Debug\\534pt692\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"gesturehandler::@6890427a1f51a3e7e1df": {"artifactName": "gesturehandler", "abi": "armeabi-v7a", "output": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native-gesture-handler\\android\\build\\intermediates\\cxx\\Debug\\534pt692\\obj\\armeabi-v7a\\libgesturehandler.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495638402d0fc5d3374d8dd464a0b611\\transformed\\react-android-0.77.0-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495638402d0fc5d3374d8dd464a0b611\\transformed\\react-android-0.77.0-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so"]}}}