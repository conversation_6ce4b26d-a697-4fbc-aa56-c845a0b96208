{"version": 3, "names": ["findPodspec", "folder", "podspecs", "glob", "sync", "cwd", "unixifyPaths", "length", "packagePodspec", "path", "basename", "podspecFile", "includes", "join"], "sources": ["../../src/config/findPodspec.ts"], "sourcesContent": ["import glob from 'fast-glob';\nimport path from 'path';\nimport {unixifyPaths} from '@react-native-community/cli-tools';\n\nexport default function findPodspec(folder: string): string | null {\n  const podspecs = glob.sync('*.podspec', {cwd: unixifyPaths(folder)});\n\n  if (podspecs.length === 0) {\n    return null;\n  }\n\n  const packagePodspec = path.basename(folder) + '.podspec';\n  const podspecFile = podspecs.includes(packagePodspec)\n    ? packagePodspec\n    : podspecs[0];\n\n  return path.join(folder, podspecFile);\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA+D;AAEhD,SAASA,WAAW,CAACC,MAAc,EAAiB;EACjE,MAAMC,QAAQ,GAAGC,mBAAI,CAACC,IAAI,CAAC,WAAW,EAAE;IAACC,GAAG,EAAE,IAAAC,wBAAY,EAACL,MAAM;EAAC,CAAC,CAAC;EAEpE,IAAIC,QAAQ,CAACK,MAAM,KAAK,CAAC,EAAE;IACzB,OAAO,IAAI;EACb;EAEA,MAAMC,cAAc,GAAGC,eAAI,CAACC,QAAQ,CAACT,MAAM,CAAC,GAAG,UAAU;EACzD,MAAMU,WAAW,GAAGT,QAAQ,CAACU,QAAQ,CAACJ,cAAc,CAAC,GACjDA,cAAc,GACdN,QAAQ,CAAC,CAAC,CAAC;EAEf,OAAOO,eAAI,CAACI,IAAI,CAACZ,MAAM,EAAEU,WAAW,CAAC;AACvC"}