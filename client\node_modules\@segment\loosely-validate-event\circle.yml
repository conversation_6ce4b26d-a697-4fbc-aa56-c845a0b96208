machine:
  node:
    version: 6

dependencies:
  pre:
    - npm config set "//registry.npmjs.org/:_authToken" $NPM_AUTH
    - sudo apt-key adv --fetch-keys http://dl.yarnpkg.com/debian/pubkey.gpg
    - echo "deb http://dl.yarnpkg.com/debian/ stable main" | sudo tee /etc/apt/sources.list.d/yarn.list
    - sudo apt-get update -qq
    - sudo apt-get install -y -qq yarn
  cache_directories:
    - ~/.yarn-cache
  override:
    - yarn

test:
  override:
    - yarn test

deployment:
  publish:
    tag: /v?[0-9]+(\.[0-9]+)*(-.+)?/
    commands:
      - npm publish
