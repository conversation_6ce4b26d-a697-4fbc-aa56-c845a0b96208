-- Merging decision tree log ---
manifest
ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:1:1-38:12
MERGED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:1:1-38:12
INJECTED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\AndroidManifest.xml:2:1-9:12
INJECTED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:lottie-react-native] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\lottie-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-gesture-handler] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-community_geolocation] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\@react-native-community\geolocation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-linear-gradient] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-linear-gradient\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-maps] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-mmkv] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-mmkv\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-svg] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vector-icons] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.77.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\495638402d0fc5d3374d8dd464a0b611\transformed\react-android-0.77.0-debug\AndroidManifest.xml:2:1-24:12
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afef5dfc50289d6b23e42e90544eb678\transformed\lottie-6.5.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4413ab5c6669427e91d406374586c5c7\transformed\material-1.6.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3ae2d37e3d0eb62ede671d2e0a4f406\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41c6c217361aea127b7e8daad55dd36e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6218e2220d2b68ce030107bd3ddca6b\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40838052881b23718ca3a39005101eec\transformed\play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e433cfd4abda164cbeb2e06c3c98800f\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c7fa47cfdf544d81d1c083c1fbfec41\transformed\play-services-base-18.2.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d0ba7040b3526665274df8803ac18\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da76261db991a83e27301b3821fedd13\transformed\play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd63068bb675e34df7cced8a72629ddf\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abe162028696023ec3040563d4dda078\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a316de1f7cbaf991be0f64c35b45f86\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:fresco:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dbf339467b513a7b76ad26cb6826e9f\transformed\fresco-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a7054facb86973433fe57d605144960\transformed\imagepipeline-okhttp3-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8e44daba1755bd206cfd52e4a43a650\transformed\drawee-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7aac540c0fd625446e5bc3c18bb312a6\transformed\nativeimagefilters-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d798d1709740f98f2fff994850a1c4b\transformed\memory-type-native-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b32457811b33089f5b6e4f4b3a6126cb\transformed\memory-type-java-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\916d4c61454be820c2dc55d11d386515\transformed\imagepipeline-native-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22548ee1aeaf6220d8f00ae0298b26c\transformed\memory-type-ashmem-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebd82354f1fd5899aea0a8b32b74966b\transformed\imagepipeline-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4673b5b736ff3c30f77b2714d003e0c8\transformed\nativeimagetranscoder-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\343d534e0671a9707f70fcf30bad4c57\transformed\imagepipeline-base-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b63cb79db98f49ff36370d0ffbb62f8e\transformed\urimod-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e61968cba374dcad099b9090129b91d7\transformed\vito-source-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\704b28858f72746154d87d76abedae46\transformed\middleware-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08f33f98186dea72e11f0dc04beea50b\transformed\ui-common-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8c3f7ef8b5513075b0079c0cae9859c\transformed\activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\050b02157236529f8db2ff8294064511\transformed\activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3027a1eb6af47acb17fec35650eea12\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf710683da7b1ed5b65c9f62dc2aea25\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2096c2bdac0d298d60bd8cc030c73d63\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ddc391a8481efeeb7744e5f0a01d17f\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e261940f64ed4eac965b00d00d0dbd9\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e4335a8a9c3625aefce29a775383318\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0dd62891993f2d657209fd86a6312e36\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\534dae8ed8a878f4590b9f7c6b64c464\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb9112698096d9f08740aa0721b6a870\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d03f2f112474d5f8764c2509c075c9c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:soloader:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a440eac8fa06f0b2b226019373cbf25\transformed\soloader-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54a1e598ce454e00e143ca2766a6ab49\transformed\fbcore-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1d646305e675f42abf44d96f19d2db8\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73dc2aa5e409b3c16e4542e4220f8db2\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44aca998dfa664c135eb035fb05ca4db\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2fc3730f0c844ade8103bca741b79de8\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2375281fbe5864d839416fe84a83f420\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fb431bee859267bf91b2b00d5a6f29c\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\562dd88d26233144b9b983b7dbeb0143\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4086f1be705906faabffa20d89a866bf\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b642c3f7d9f0e750887c36975cd2d613\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c63d363fa7ce214285128d6ed89c5c2\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79c33e0b2a455bd8ac41666e964e403e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\561d1e382e8e77cfde59c6b658038e78\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c612a3bdca29aba1cfca81001e48bd49\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bc2b624117b51bd0952e247838bdf97\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a1aa263341cf4d074595ac4b1b6da9c\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\644c8ba97f4005e26173cb7b92845a25\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77363701cbbf8388e7a4f4b35bd8750f\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0ac454e00a83ac26b157989d8fc7da0\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2389d7e667f7b2053ea58a490b160ed1\transformed\ui-core-3.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.77.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8efc2e640de319a2aa5b8b521a15efc3\transformed\hermes-android-0.77.0-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40214e9c2800572e131db14a8065b96a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378b476a4621329809f65177ea73f77e\transformed\tracing-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d2c05ebeb2e9c00c30592bdef558d9c\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2ef9a269edda955ca019008ad1a34c8\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2fcb9fa97e65fff06e4164a8491cffae\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2de8bf3ab2384f9c8fd456a65f72aad5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\630f41c2fd8d32c725653550914888a5\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3491a7d0e07a7744ec5b76c30d3c565a\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8af2864ce3c49b59f7d2d8b04296b0b4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71485965cc203ce6ddcbaef48d9d980\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47ad3b96ea6c833716c529e1b42deb4a\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26f54a1db2d46ce2bb30a623438b9600\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ff2e6e4215aaddb3c9f08d0afafea14\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c493fcb0ff35ca3634119b53386ed5be\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9cd38d273aa80e0d66466b04adf6ea09\transformed\fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b6ded0ceaee573029c2dcfb22b2cdb\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
	package
		INJECTED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:3:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
	android:name
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:3:22-64
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:4:5-79
	android:name
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:4:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:5:5-81
	android:name
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:5:22-78
uses-permission#android.permission.ACCESS_BACKGROUND_LOCATION
ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:6:5-85
	android:name
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:6:22-82
application
ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:9:5-37:19
MERGED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:9:5-37:19
MERGED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:9:5-37:19
INJECTED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\AndroidManifest.xml:5:5-8:50
MERGED from [com.facebook.react:react-android:0.77.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\495638402d0fc5d3374d8dd464a0b611\transformed\react-android-0.77.0-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.77.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\495638402d0fc5d3374d8dd464a0b611\transformed\react-android-0.77.0-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afef5dfc50289d6b23e42e90544eb678\transformed\lottie-6.5.2\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afef5dfc50289d6b23e42e90544eb678\transformed\lottie-6.5.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4413ab5c6669427e91d406374586c5c7\transformed\material-1.6.1\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4413ab5c6669427e91d406374586c5c7\transformed\material-1.6.1\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41c6c217361aea127b7e8daad55dd36e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41c6c217361aea127b7e8daad55dd36e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40838052881b23718ca3a39005101eec\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40838052881b23718ca3a39005101eec\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e433cfd4abda164cbeb2e06c3c98800f\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e433cfd4abda164cbeb2e06c3c98800f\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c7fa47cfdf544d81d1c083c1fbfec41\transformed\play-services-base-18.2.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c7fa47cfdf544d81d1c083c1fbfec41\transformed\play-services-base-18.2.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d0ba7040b3526665274df8803ac18\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d0ba7040b3526665274df8803ac18\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da76261db991a83e27301b3821fedd13\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da76261db991a83e27301b3821fedd13\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e261940f64ed4eac965b00d00d0dbd9\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e261940f64ed4eac965b00d00d0dbd9\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79c33e0b2a455bd8ac41666e964e403e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79c33e0b2a455bd8ac41666e964e403e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40214e9c2800572e131db14a8065b96a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40214e9c2800572e131db14a8065b96a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d2c05ebeb2e9c00c30592bdef558d9c\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d2c05ebeb2e9c00c30592bdef558d9c\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8af2864ce3c49b59f7d2d8b04296b0b4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8af2864ce3c49b59f7d2d8b04296b0b4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b6ded0ceaee573029c2dcfb22b2cdb\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b6ded0ceaee573029c2dcfb22b2cdb\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
	android:extractNativeLibs
		INJECTED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:18:7-33
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:18:7-33
	android:label
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:11:7-39
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:11:7-39
	android:hardwareAccelerated
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:17:7-41
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:17:7-41
	tools:ignore
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\AndroidManifest.xml:8:9-48
	android:roundIcon
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:13:7-52
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:13:7-52
	tools:targetApi
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\AndroidManifest.xml:7:9-29
	android:icon
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:12:7-41
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:12:7-41
	android:allowBackup
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:14:7-34
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:14:7-34
	android:theme
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:16:7-38
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:16:7-38
	android:usesCleartextTraffic
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:15:7-42
	android:name
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:10:7-38
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:10:7-38
meta-data#com.google.android.geo.API_KEY
ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:20:7-23:9
	android:value
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:22:7-62
	android:name
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:21:7-52
activity#com.grocery_app.MainActivity
ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:25:7-36:18
	android:label
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:27:9-41
	android:launchMode
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:29:9-40
	android:windowSoftInputMode
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:30:9-52
	android:exported
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:31:9-32
	android:configChanges
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:28:9-118
	android:name
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:26:9-37
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:32:9-35:25
action#android.intent.action.MAIN
ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:33:13-65
	android:name
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:33:21-62
category#android.intent.category.LAUNCHER
ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:34:13-73
	android:name
		ADDED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:34:23-70
uses-sdk
INJECTED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\AndroidManifest.xml
MERGED from [:lottie-react-native] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\lottie-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:lottie-react-native] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\lottie-react-native\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_geolocation] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\@react-native-community\geolocation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-community_geolocation] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\@react-native-community\geolocation\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-linear-gradient] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-linear-gradient\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-linear-gradient] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-linear-gradient\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-mmkv] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-mmkv\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-mmkv] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-mmkv\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.77.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\495638402d0fc5d3374d8dd464a0b611\transformed\react-android-0.77.0-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.77.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\495638402d0fc5d3374d8dd464a0b611\transformed\react-android-0.77.0-debug\AndroidManifest.xml:10:5-44
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afef5dfc50289d6b23e42e90544eb678\transformed\lottie-6.5.2\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.5.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\afef5dfc50289d6b23e42e90544eb678\transformed\lottie-6.5.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4413ab5c6669427e91d406374586c5c7\transformed\material-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4413ab5c6669427e91d406374586c5c7\transformed\material-1.6.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3ae2d37e3d0eb62ede671d2e0a4f406\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c3ae2d37e3d0eb62ede671d2e0a4f406\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41c6c217361aea127b7e8daad55dd36e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\41c6c217361aea127b7e8daad55dd36e\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6218e2220d2b68ce030107bd3ddca6b\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d6218e2220d2b68ce030107bd3ddca6b\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40838052881b23718ca3a39005101eec\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40838052881b23718ca3a39005101eec\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e433cfd4abda164cbeb2e06c3c98800f\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e433cfd4abda164cbeb2e06c3c98800f\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c7fa47cfdf544d81d1c083c1fbfec41\transformed\play-services-base-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c7fa47cfdf544d81d1c083c1fbfec41\transformed\play-services-base-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d0ba7040b3526665274df8803ac18\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b83d0ba7040b3526665274df8803ac18\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da76261db991a83e27301b3821fedd13\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da76261db991a83e27301b3821fedd13\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd63068bb675e34df7cced8a72629ddf\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fd63068bb675e34df7cced8a72629ddf\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abe162028696023ec3040563d4dda078\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\abe162028696023ec3040563d4dda078\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a316de1f7cbaf991be0f64c35b45f86\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a316de1f7cbaf991be0f64c35b45f86\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:fresco:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dbf339467b513a7b76ad26cb6826e9f\transformed\fresco-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5dbf339467b513a7b76ad26cb6826e9f\transformed\fresco-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a7054facb86973433fe57d605144960\transformed\imagepipeline-okhttp3-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1a7054facb86973433fe57d605144960\transformed\imagepipeline-okhttp3-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8e44daba1755bd206cfd52e4a43a650\transformed\drawee-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f8e44daba1755bd206cfd52e4a43a650\transformed\drawee-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7aac540c0fd625446e5bc3c18bb312a6\transformed\nativeimagefilters-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7aac540c0fd625446e5bc3c18bb312a6\transformed\nativeimagefilters-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d798d1709740f98f2fff994850a1c4b\transformed\memory-type-native-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2d798d1709740f98f2fff994850a1c4b\transformed\memory-type-native-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b32457811b33089f5b6e4f4b3a6126cb\transformed\memory-type-java-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b32457811b33089f5b6e4f4b3a6126cb\transformed\memory-type-java-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\916d4c61454be820c2dc55d11d386515\transformed\imagepipeline-native-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\916d4c61454be820c2dc55d11d386515\transformed\imagepipeline-native-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22548ee1aeaf6220d8f00ae0298b26c\transformed\memory-type-ashmem-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22548ee1aeaf6220d8f00ae0298b26c\transformed\memory-type-ashmem-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebd82354f1fd5899aea0a8b32b74966b\transformed\imagepipeline-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ebd82354f1fd5899aea0a8b32b74966b\transformed\imagepipeline-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4673b5b736ff3c30f77b2714d003e0c8\transformed\nativeimagetranscoder-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4673b5b736ff3c30f77b2714d003e0c8\transformed\nativeimagetranscoder-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\343d534e0671a9707f70fcf30bad4c57\transformed\imagepipeline-base-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\343d534e0671a9707f70fcf30bad4c57\transformed\imagepipeline-base-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b63cb79db98f49ff36370d0ffbb62f8e\transformed\urimod-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b63cb79db98f49ff36370d0ffbb62f8e\transformed\urimod-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e61968cba374dcad099b9090129b91d7\transformed\vito-source-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e61968cba374dcad099b9090129b91d7\transformed\vito-source-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\704b28858f72746154d87d76abedae46\transformed\middleware-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\704b28858f72746154d87d76abedae46\transformed\middleware-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08f33f98186dea72e11f0dc04beea50b\transformed\ui-common-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\08f33f98186dea72e11f0dc04beea50b\transformed\ui-common-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8c3f7ef8b5513075b0079c0cae9859c\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a8c3f7ef8b5513075b0079c0cae9859c\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\050b02157236529f8db2ff8294064511\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\050b02157236529f8db2ff8294064511\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3027a1eb6af47acb17fec35650eea12\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3027a1eb6af47acb17fec35650eea12\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf710683da7b1ed5b65c9f62dc2aea25\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\cf710683da7b1ed5b65c9f62dc2aea25\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2096c2bdac0d298d60bd8cc030c73d63\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2096c2bdac0d298d60bd8cc030c73d63\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ddc391a8481efeeb7744e5f0a01d17f\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ddc391a8481efeeb7744e5f0a01d17f\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e261940f64ed4eac965b00d00d0dbd9\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e261940f64ed4eac965b00d00d0dbd9\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e4335a8a9c3625aefce29a775383318\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3e4335a8a9c3625aefce29a775383318\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0dd62891993f2d657209fd86a6312e36\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0dd62891993f2d657209fd86a6312e36\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\534dae8ed8a878f4590b9f7c6b64c464\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\534dae8ed8a878f4590b9f7c6b64c464\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb9112698096d9f08740aa0721b6a870\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eb9112698096d9f08740aa0721b6a870\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d03f2f112474d5f8764c2509c075c9c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7d03f2f112474d5f8764c2509c075c9c\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:soloader:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a440eac8fa06f0b2b226019373cbf25\transformed\soloader-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a440eac8fa06f0b2b226019373cbf25\transformed\soloader-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54a1e598ce454e00e143ca2766a6ab49\transformed\fbcore-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\54a1e598ce454e00e143ca2766a6ab49\transformed\fbcore-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1d646305e675f42abf44d96f19d2db8\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c1d646305e675f42abf44d96f19d2db8\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73dc2aa5e409b3c16e4542e4220f8db2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\73dc2aa5e409b3c16e4542e4220f8db2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44aca998dfa664c135eb035fb05ca4db\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44aca998dfa664c135eb035fb05ca4db\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2fc3730f0c844ade8103bca741b79de8\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2fc3730f0c844ade8103bca741b79de8\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2375281fbe5864d839416fe84a83f420\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2375281fbe5864d839416fe84a83f420\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fb431bee859267bf91b2b00d5a6f29c\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fb431bee859267bf91b2b00d5a6f29c\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\562dd88d26233144b9b983b7dbeb0143\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\562dd88d26233144b9b983b7dbeb0143\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4086f1be705906faabffa20d89a866bf\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4086f1be705906faabffa20d89a866bf\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b642c3f7d9f0e750887c36975cd2d613\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b642c3f7d9f0e750887c36975cd2d613\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c63d363fa7ce214285128d6ed89c5c2\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c63d363fa7ce214285128d6ed89c5c2\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79c33e0b2a455bd8ac41666e964e403e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79c33e0b2a455bd8ac41666e964e403e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\561d1e382e8e77cfde59c6b658038e78\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\561d1e382e8e77cfde59c6b658038e78\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c612a3bdca29aba1cfca81001e48bd49\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c612a3bdca29aba1cfca81001e48bd49\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bc2b624117b51bd0952e247838bdf97\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9bc2b624117b51bd0952e247838bdf97\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a1aa263341cf4d074595ac4b1b6da9c\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5a1aa263341cf4d074595ac4b1b6da9c\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\644c8ba97f4005e26173cb7b92845a25\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\644c8ba97f4005e26173cb7b92845a25\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77363701cbbf8388e7a4f4b35bd8750f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\77363701cbbf8388e7a4f4b35bd8750f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0ac454e00a83ac26b157989d8fc7da0\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f0ac454e00a83ac26b157989d8fc7da0\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2389d7e667f7b2053ea58a490b160ed1\transformed\ui-core-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2389d7e667f7b2053ea58a490b160ed1\transformed\ui-core-3.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.77.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8efc2e640de319a2aa5b8b521a15efc3\transformed\hermes-android-0.77.0-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.77.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8efc2e640de319a2aa5b8b521a15efc3\transformed\hermes-android-0.77.0-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40214e9c2800572e131db14a8065b96a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40214e9c2800572e131db14a8065b96a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378b476a4621329809f65177ea73f77e\transformed\tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\378b476a4621329809f65177ea73f77e\transformed\tracing-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d2c05ebeb2e9c00c30592bdef558d9c\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d2c05ebeb2e9c00c30592bdef558d9c\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2ef9a269edda955ca019008ad1a34c8\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e2ef9a269edda955ca019008ad1a34c8\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2fcb9fa97e65fff06e4164a8491cffae\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2fcb9fa97e65fff06e4164a8491cffae\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2de8bf3ab2384f9c8fd456a65f72aad5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2de8bf3ab2384f9c8fd456a65f72aad5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\630f41c2fd8d32c725653550914888a5\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\630f41c2fd8d32c725653550914888a5\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3491a7d0e07a7744ec5b76c30d3c565a\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3491a7d0e07a7744ec5b76c30d3c565a\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8af2864ce3c49b59f7d2d8b04296b0b4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8af2864ce3c49b59f7d2d8b04296b0b4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71485965cc203ce6ddcbaef48d9d980\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e71485965cc203ce6ddcbaef48d9d980\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47ad3b96ea6c833716c529e1b42deb4a\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\47ad3b96ea6c833716c529e1b42deb4a\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26f54a1db2d46ce2bb30a623438b9600\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\26f54a1db2d46ce2bb30a623438b9600\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ff2e6e4215aaddb3c9f08d0afafea14\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ff2e6e4215aaddb3c9f08d0afafea14\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c493fcb0ff35ca3634119b53386ed5be\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c493fcb0ff35ca3634119b53386ed5be\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9cd38d273aa80e0d66466b04adf6ea09\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9cd38d273aa80e0d66466b04adf6ea09\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b6ded0ceaee573029c2dcfb22b2cdb\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b6ded0ceaee573029c2dcfb22b2cdb\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\AndroidManifest.xml
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from [com.facebook.react:react-android:0.77.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\495638402d0fc5d3374d8dd464a0b611\transformed\react-android-0.77.0-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from [com.facebook.react:react-android:0.77.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\495638402d0fc5d3374d8dd464a0b611\transformed\react-android-0.77.0-debug\AndroidManifest.xml:16:22-75
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.77.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\495638402d0fc5d3374d8dd464a0b611\transformed\react-android-0.77.0-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.77.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\495638402d0fc5d3374d8dd464a0b611\transformed\react-android-0.77.0-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.77.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\495638402d0fc5d3374d8dd464a0b611\transformed\react-android-0.77.0-debug\AndroidManifest.xml:20:13-77
meta-data#com.google.android.gms.version
ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e433cfd4abda164cbeb2e06c3c98800f\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da76261db991a83e27301b3821fedd13\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\da76261db991a83e27301b3821fedd13\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e433cfd4abda164cbeb2e06c3c98800f\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
	android:name
		ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e433cfd4abda164cbeb2e06c3c98800f\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:22-76
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
queries
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c7fa47cfdf544d81d1c083c1fbfec41\transformed\play-services-base-18.2.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c7fa47cfdf544d81d1c083c1fbfec41\transformed\play-services-base-18.2.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c7fa47cfdf544d81d1c083c1fbfec41\transformed\play-services-base-18.2.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c7fa47cfdf544d81d1c083c1fbfec41\transformed\play-services-base-18.2.0\AndroidManifest.xml:20:19-85
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:22-65
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:22-78
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e261940f64ed4eac965b00d00d0dbd9\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e261940f64ed4eac965b00d00d0dbd9\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79c33e0b2a455bd8ac41666e964e403e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79c33e0b2a455bd8ac41666e964e403e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40214e9c2800572e131db14a8065b96a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\40214e9c2800572e131db14a8065b96a\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e261940f64ed4eac965b00d00d0dbd9\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e261940f64ed4eac965b00d00d0dbd9\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e261940f64ed4eac965b00d00d0dbd9\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.grocery_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.grocery_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79c33e0b2a455bd8ac41666e964e403e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79c33e0b2a455bd8ac41666e964e403e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79c33e0b2a455bd8ac41666e964e403e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d2c05ebeb2e9c00c30592bdef558d9c\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d2c05ebeb2e9c00c30592bdef558d9c\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d2c05ebeb2e9c00c30592bdef558d9c\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d2c05ebeb2e9c00c30592bdef558d9c\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
meta-data#com.facebook.soloader.enabled
ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b6ded0ceaee573029c2dcfb22b2cdb\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	android:value
		ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b6ded0ceaee573029c2dcfb22b2cdb\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b6ded0ceaee573029c2dcfb22b2cdb\transformed\soloader-0.12.1\AndroidManifest.xml:13:13-57
