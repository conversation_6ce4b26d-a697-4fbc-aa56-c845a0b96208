{"version": 3, "names": ["name", "description", "func", "createRun", "platformName", "examples", "desc", "cmd", "options", "getRunOptions"], "sources": ["../../../src/commands/runIOS/index.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport {\n  createRun,\n  getRunOptions,\n} from '@react-native-community/cli-platform-apple';\n\nexport default {\n  name: 'run-ios',\n  description: 'builds your app and starts it on iOS simulator',\n  func: createRun({platformName: 'ios'}),\n  examples: [\n    {\n      desc: 'Run on a different simulator, e.g. iPhone SE (2nd generation)',\n      cmd: 'npx react-native run-ios --simulator \"iPhone SE (2nd generation)\"',\n    },\n    {\n      desc: \"Run on a connected device, e.g. Max's iPhone\",\n      cmd: 'npx react-native run-ios --device \"Max\\'s iPhone\"',\n    },\n    {\n      desc: 'Run on the AppleTV simulator',\n      cmd: 'npx react-native run-ios --simulator \"Apple TV\"  --scheme \"helloworld-tvOS\"',\n    },\n  ],\n  options: getRunOptions({platformName: 'ios'}),\n};\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AARA;AACA;AACA;AACA;AACA;AACA;AACA;AANA,eAae;EACbA,IAAI,EAAE,SAAS;EACfC,WAAW,EAAE,gDAAgD;EAC7DC,IAAI,EAAE,IAAAC,6BAAS,EAAC;IAACC,YAAY,EAAE;EAAK,CAAC,CAAC;EACtCC,QAAQ,EAAE,CACR;IACEC,IAAI,EAAE,+DAA+D;IACrEC,GAAG,EAAE;EACP,CAAC,EACD;IACED,IAAI,EAAE,8CAA8C;IACpDC,GAAG,EAAE;EACP,CAAC,EACD;IACED,IAAI,EAAE,8BAA8B;IACpCC,GAAG,EAAE;EACP,CAAC,CACF;EACDC,OAAO,EAAE,IAAAC,iCAAa,EAAC;IAACL,YAAY,EAAE;EAAK,CAAC;AAC9C,CAAC;AAAA"}