ninja: Entering directory `D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\x86'
[0/2] Re-checking globbed directories...
[1/123] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/States.cpp.o
[2/123] Building CXX object lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/lottiereactnative-generated.cpp.o
[3/123] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/Props.cpp.o
[4/123] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/EventEmitters.cpp.o
[5/123] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/f3ee4c34af00dbdcf11b60ec1cbc8a99/RNCGeolocationSpecJSI-generated.cpp.o
[6/123] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/ShadowNodes.cpp.o
[7/123] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/RNCGeolocationSpec-generated.cpp.o
[8/123] Building CXX object RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/ComponentDescriptors.cpp.o
[9/123] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[10/123] Building CXX object lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/States.cpp.o
[11/123] Building CXX object lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/EventEmitters.cpp.o
[12/123] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/States.cpp.o
[13/123] Building CXX object lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/lottiereactnativeJSI-generated.cpp.o
[14/123] Building CXX object lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ShadowNodes.cpp.o
[15/123] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[16/123] Building CXX object lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/Props.cpp.o
[17/123] Building CXX object lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ComponentDescriptors.cpp.o
[18/123] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o
[19/123] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o
[20/123] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o
[21/123] Building CXX object RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/States.cpp.o
[22/123] Building CXX object RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/RNMmkvSpecJSI-generated.cpp.o
[23/123] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o
[24/123] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o
[25/123] Building CXX object RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ShadowNodes.cpp.o
[26/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/src/main/cpp/AndroidLogger.cpp.o
[27/123] Building CXX object RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/EventEmitters.cpp.o
[28/123] Building CXX object RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ComponentDescriptors.cpp.o
[29/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_Android.cpp.o
[30/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKVLog.cpp.o
[31/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_OSX.cpp.o
[32/123] Building CXX object RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/RNMmkvSpec-generated.cpp.o
[33/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKVLog_Android.cpp.o
[34/123] Building CXX object RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/Props.cpp.o
[35/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/4c5fc1969699a9d0d43817d45840bc71/node_modules/react-native-mmkv/cpp/NativeMmkvModule.cpp.o
[36/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMBuffer.cpp.o
[37/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MiniPBCoder_OSX.cpp.o
[38/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/4c5fc1969699a9d0d43817d45840bc71/node_modules/react-native-mmkv/cpp/MmkvHostObject.cpp.o
[39/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_IO.cpp.o
[40/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock.cpp.o
[41/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock_Android.cpp.o
[42/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV.cpp.o
[43/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock_Win32.cpp.o
[44/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputData_OSX.cpp.o
[45/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MiniPBCoder.cpp.o
[46/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_md5_dgst.cpp.o
[47/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputDataCrypt_OSX.cpp.o
[48/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_md5_one.cpp.o
[49/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedOutputData.cpp.o
[50/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/KeyValueHolder.cpp.o
[51/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputDataCrypt.cpp.o
[52/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputData.cpp.o
[53/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/PBUtility.cpp.o
[54/123] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/98a1df242faf8ddf674d0e5ff6f8b78d/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o
[55/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/crc32/crc32_armv8.cpp.o
[56/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aes_core.cpp.o
[57/123] Building CXX object CMakeFiles/appmodules.dir/D_/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[58/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/AESCrypt.cpp.o
[59/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_cfb128.cpp.o
[60/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/ThreadLock_Win32.cpp.o
[61/123] Building ASM object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aes-armv4.S.o
[62/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Linux.cpp.o
[63/123] Building ASM object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aesv8-armx.S.o
[64/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Win32.cpp.o
[65/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile.cpp.o
[66/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_OSX.cpp.o
[67/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Android.cpp.o
[68/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/ThreadLock.cpp.o
[69/123] Building CXX object RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/crc32/zlib/crc32.cpp.o
[70/123] Linking CXX static library RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.a
[71/123] Linking CXX shared library D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\x86\libreact-native-mmkv.so
[72/123] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o
[73/123] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o
[74/123] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o
[75/123] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o
[76/123] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o
[77/123] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o
[78/123] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o
[79/123] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/1c6e0ef8c2f7691fa500bf969de36fd2/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[80/123] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/302295bea064dc7d8a09e6c6abfdb18f/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o
[81/123] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e0bece83b93477964dc37bc08ee394eb/renderer/components/safeareacontext/States.cpp.o
[82/123] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/cf671125db50f06123459ae738795c56/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o
[83/123] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/98a1df242faf8ddf674d0e5ff6f8b78d/safeareacontext/RNCSafeAreaViewState.cpp.o
[84/123] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e0bece83b93477964dc37bc08ee394eb/renderer/components/safeareacontext/Props.cpp.o
[85/123] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/17e501ed6c709211940c0ff9f21d6cd9/components/safeareacontext/EventEmitters.cpp.o
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:16:69: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNCSafeAreaProviderEventEmitter::onInsetsChange(OnInsetsChange $event) const {
                                                                    ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:17:34: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("insetsChange", [$event=std::move($event)](jsi::Runtime &runtime) {
                                 ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:17:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("insetsChange", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                  ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:18:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:21:38: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  insets.setProperty(runtime, "top", $event.insets.top);
                                     ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:22:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  insets.setProperty(runtime, "right", $event.insets.right);
                                       ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:23:41: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  insets.setProperty(runtime, "bottom", $event.insets.bottom);
                                        ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:24:39: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  insets.setProperty(runtime, "left", $event.insets.left);
                                      ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:25:3: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  $payload.setProperty(runtime, "insets", insets);
  ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:29:35: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  frame.setProperty(runtime, "x", $event.frame.x);
                                  ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:30:35: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  frame.setProperty(runtime, "y", $event.frame.y);
                                  ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:31:39: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  frame.setProperty(runtime, "width", $event.frame.width);
                                      ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:32:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  frame.setProperty(runtime, "height", $event.frame.height);
                                       ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:33:3: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  $payload.setProperty(runtime, "frame", frame);
  ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp:35:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
15 warnings generated.
[86/123] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/17e501ed6c709211940c0ff9f21d6cd9/components/safeareacontext/ShadowNodes.cpp.o
[87/123] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/5529b51a87512c5f446a2fec7f3427b4/source/codegen/jni/safeareacontext-generated.cpp.o
[88/123] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f59ed18b5a42e7549d6e21cda7b8aef9/safeareacontext/safeareacontextJSI-generated.cpp.o
[89/123] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/f59ed18b5a42e7549d6e21cda7b8aef9/safeareacontext/ComponentDescriptors.cpp.o
[90/123] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/55c53f7eed0f5c338585a990d809122f/codegen/jni/react/renderer/components/rnscreens/States.cpp.o
[91/123] Linking CXX shared library D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\x86\libreact_codegen_safeareacontext.so
[92/123] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7b2d769ecb0ab6bfb11a92d3f2c4692d/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o
[93/123] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f5e254acb8edbff727caa014c8e04020/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o
[94/123] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/aeb986e49880b4e75a6517a3a0c9c97c/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o
[95/123] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/cf671125db50f06123459ae738795c56/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[96/123] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/cf671125db50f06123459ae738795c56/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o
[97/123] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/cf671125db50f06123459ae738795c56/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[98/123] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/55c53f7eed0f5c338585a990d809122f/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o
[99/123] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[100/123] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/95ab72a25052308f86569b1f7a536624/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o
[101/123] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/cf671125db50f06123459ae738795c56/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[102/123] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o
[103/123] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/7b2d769ecb0ab6bfb11a92d3f2c4692d/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:17:52: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onAppear(OnAppear $event) const {
                                                   ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:19:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:21:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:26:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onDisappear(OnDisappear $event) const {
                                                         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:28:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:30:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:35:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onDismissed(OnDismissed $event) const {
                                                         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:36:31: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("dismissed", [$event=std::move($event)](jsi::Runtime &runtime) {
                              ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:36:48: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("dismissed", [$event=std::move($event)](jsi::Runtime &runtime) {
                                               ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:37:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:38:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
    ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:38:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
                                                  ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:39:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:44:84: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onNativeDismissCancelled(OnNativeDismissCancelled $event) const {
                                                                                   ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:45:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("nativeDismissCancelled", [$event=std::move($event)](jsi::Runtime &runtime) {
                                           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:45:61: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("nativeDismissCancelled", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                            ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:46:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:47:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
    ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:47:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
                                                  ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:48:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:53:60: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onWillAppear(OnWillAppear $event) const {
                                                           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:55:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:57:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:62:66: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onWillDisappear(OnWillDisappear $event) const {
                                                                 ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:64:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:66:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:71:76: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onHeaderHeightChange(OnHeaderHeightChange $event) const {
                                                                           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:72:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("headerHeightChange", [$event=std::move($event)](jsi::Runtime &runtime) {
                                       ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:72:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("headerHeightChange", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                        ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:73:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:74:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "headerHeight", $event.headerHeight);
    ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:74:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "headerHeight", $event.headerHeight);
                                                  ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:75:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:80:76: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onTransitionProgress(OnTransitionProgress $event) const {
                                                                           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:81:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("transitionProgress", [$event=std::move($event)](jsi::Runtime &runtime) {
                                       ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:81:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("transitionProgress", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                        ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:82:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:83:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "progress", $event.progress);
    ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:83:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "progress", $event.progress);
                                              ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:84:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "closing", $event.closing);
^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:84:42: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "closing", $event.closing);
                                         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:85:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "goingForward", $event.goingForward);
^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:85:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "goingForward", $event.goingForward);
                                              ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:86:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:91:66: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onGestureCancel(OnGestureCancel $event) const {
                                                                 ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:93:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:95:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:100:86: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onHeaderBackButtonClicked(OnHeaderBackButtonClicked $event) const {
                                                                                     ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:102:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:104:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:109:76: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSModalScreenEventEmitter::onSheetDetentChanged(OnSheetDetentChanged $event) const {
                                                                           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:110:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("sheetDetentChanged", [$event=std::move($event)](jsi::Runtime &runtime) {
                                       ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:110:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("sheetDetentChanged", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                        ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:111:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:112:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "index", $event.index);
    ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:112:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "index", $event.index);
                                           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:113:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "isStable", $event.isStable);
^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:113:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "isStable", $event.isStable);
                                          ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:114:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:122:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onAppear(OnAppear $event) const {
                                              ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:124:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:126:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:131:53: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onDisappear(OnDisappear $event) const {
                                                    ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:133:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:135:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:140:53: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onDismissed(OnDismissed $event) const {
                                                    ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:141:31: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("dismissed", [$event=std::move($event)](jsi::Runtime &runtime) {
                              ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:141:48: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("dismissed", [$event=std::move($event)](jsi::Runtime &runtime) {
                                               ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:142:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:143:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
    ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:143:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
                                                  ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:144:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:149:79: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onNativeDismissCancelled(OnNativeDismissCancelled $event) const {
                                                                              ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:150:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("nativeDismissCancelled", [$event=std::move($event)](jsi::Runtime &runtime) {
                                           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:150:61: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("nativeDismissCancelled", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                            ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:151:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:152:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
    ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:152:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "dismissCount", $event.dismissCount);
                                                  ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:153:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:158:55: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onWillAppear(OnWillAppear $event) const {
                                                      ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:160:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:162:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:167:61: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onWillDisappear(OnWillDisappear $event) const {
                                                            ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:169:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:171:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:176:71: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onHeaderHeightChange(OnHeaderHeightChange $event) const {
                                                                      ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:177:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("headerHeightChange", [$event=std::move($event)](jsi::Runtime &runtime) {
                                       ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:177:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("headerHeightChange", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                        ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:178:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:179:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "headerHeight", $event.headerHeight);
    ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:179:51: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "headerHeight", $event.headerHeight);
                                                  ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:180:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:185:71: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onTransitionProgress(OnTransitionProgress $event) const {
                                                                      ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:186:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("transitionProgress", [$event=std::move($event)](jsi::Runtime &runtime) {
                                       ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:186:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("transitionProgress", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                        ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:187:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:188:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "progress", $event.progress);
    ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:188:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "progress", $event.progress);
                                              ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:189:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "closing", $event.closing);
^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:189:42: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "closing", $event.closing);
                                         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:190:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "goingForward", $event.goingForward);
^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:190:47: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "goingForward", $event.goingForward);
                                              ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:191:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:196:61: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onGestureCancel(OnGestureCancel $event) const {
                                                            ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:198:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:200:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:205:81: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onHeaderBackButtonClicked(OnHeaderBackButtonClicked $event) const {
                                                                                ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:207:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:209:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:214:71: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenEventEmitter::onSheetDetentChanged(OnSheetDetentChanged $event) const {
                                                                      ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:215:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("sheetDetentChanged", [$event=std::move($event)](jsi::Runtime &runtime) {
                                       ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:215:57: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("sheetDetentChanged", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                        ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:216:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:217:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "index", $event.index);
    ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:217:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "index", $event.index);
                                           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:218:1: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "isStable", $event.isStable);
^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:218:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
$payload.setProperty(runtime, "isStable", $event.isStable);
                                          ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:219:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:225:68: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenStackHeaderConfigEventEmitter::onAttached(OnAttached $event) const {
                                                                   ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:227:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:229:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:234:68: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenStackHeaderConfigEventEmitter::onDetached(OnDetached $event) const {
                                                                   ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:236:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:238:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:244:78: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSScreenStackEventEmitter::onFinishTransitioning(OnFinishTransitioning $event) const {
                                                                             ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:246:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:248:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:253:60: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onSearchFocus(OnSearchFocus $event) const {
                                                           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:255:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:257:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:262:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onSearchBlur(OnSearchBlur $event) const {
                                                         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:264:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:266:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:271:72: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onSearchButtonPress(OnSearchButtonPress $event) const {
                                                                       ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:272:39: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("searchButtonPress", [$event=std::move($event)](jsi::Runtime &runtime) {
                                      ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:272:56: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("searchButtonPress", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                       ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:273:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:274:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "text", $event.text);
    ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:274:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "text", $event.text);
                                          ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:275:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:280:72: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onCancelButtonPress(OnCancelButtonPress $event) const {
                                                                       ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:282:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:284:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:289:58: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onChangeText(OnChangeText $event) const {
                                                         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:290:32: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("changeText", [$event=std::move($event)](jsi::Runtime &runtime) {
                               ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:290:49: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("changeText", [$event=std::move($event)](jsi::Runtime &runtime) {
                                                ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:291:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:292:5: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "text", $event.text);
    ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:292:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    $payload.setProperty(runtime, "text", $event.text);
                                          ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:293:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:298:48: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onClose(OnClose $event) const {
                                               ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:300:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:302:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:307:46: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSSearchBarEventEmitter::onOpen(OnOpen $event) const {
                                             ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:309:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp:311:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
156 warnings generated.
[104/123] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/60bf106466fdd5cbdf2ea95ef22f61ca/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o
[105/123] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/95ab72a25052308f86569b1f7a536624/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o
[106/123] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/732c2bd6dcef3ccd29a5c1075a85de02/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o
[107/123] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/732c2bd6dcef3ccd29a5c1075a85de02/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:31:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
void RNSVGImageEventEmitter::onLoad(OnLoad $event) const {
                                           ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:32:26: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("load", [$event=std::move($event)](jsi::Runtime &runtime) {
                         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:32:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  dispatchEvent("load", [$event=std::move($event)](jsi::Runtime &runtime) {
                                          ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:33:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    auto $payload = jsi::Object(runtime);
         ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:36:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  source.setProperty(runtime, "width", $event.source.width);
                                       ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:37:41: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  source.setProperty(runtime, "height", $event.source.height);
                                        ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:38:38: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  source.setProperty(runtime, "uri", $event.source.uri);
                                     ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:39:3: warning: '$' in identifier [-Wdollar-in-identifier-extension]
  $payload.setProperty(runtime, "source", source);
  ^
D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:41:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
    return $payload;
           ^
9 warnings generated.
[108/123] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/95ab72a25052308f86569b1f7a536624/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o
[109/123] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f5e254acb8edbff727caa014c8e04020/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o
[110/123] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/5a562b30f5f2c1dd088306af1979dedc/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o
[111/123] Linking CXX shared library D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\x86\libreact_codegen_rnscreens.so
[112/123] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/732c2bd6dcef3ccd29a5c1075a85de02/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o
[113/123] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/5a562b30f5f2c1dd088306af1979dedc/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o
[114/123] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o
[115/123] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o
[116/123] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o
[117/123] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o
[118/123] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp.o
[119/123] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o
[120/123] Building CXX object RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o
[121/123] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/1999c3a5620371e80772fa0a39e85ddf/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o
[122/123] Linking CXX shared library D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\x86\libreact_codegen_rnsvg.so
[123/123] Linking CXX shared library D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\x86\libappmodules.so
