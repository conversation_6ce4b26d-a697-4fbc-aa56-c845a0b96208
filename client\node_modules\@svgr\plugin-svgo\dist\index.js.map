{"version": 3, "file": "index.js", "sources": ["../src/config.ts", "../src/index.ts"], "sourcesContent": ["import { cosmiconfigSync } from 'cosmiconfig'\nimport type { Config, State } from '@svgr/core'\n\nconst explorer = cosmiconfigSync('svgo', {\n  searchPlaces: [\n    'package.json',\n    '.svgorc',\n    '.svgorc.js',\n    '.svgorc.json',\n    '.svgorc.yaml',\n    '.svgorc.yml',\n    'svgo.config.js',\n    'svgo.config.cjs',\n    '.svgo.yml',\n  ],\n  transform: (result) => result && result.config,\n  cache: true,\n})\n\nconst getSvgoConfigFromSvgrConfig = (config: Config): any => {\n  const params = { overrides: {} as any }\n  if (config.icon || config.dimensions === false) {\n    params.overrides.removeViewBox = false\n  }\n  if (config.native) {\n    params.overrides.inlineStyles = {\n      onlyMatchedOnce: false,\n    }\n  }\n\n  return {\n    plugins: [\n      {\n        name: 'preset-default',\n        params,\n      },\n      'prefixIds',\n    ],\n  }\n}\n\nexport const getSvgoConfig = (config: Config, state: State): any => {\n  const cwd = state.filePath || process.cwd()\n  if (config.svgoConfig) return config.svgoConfig\n  if (config.runtimeConfig) {\n    const userConfig = explorer.search(cwd)\n    if (userConfig) return userConfig\n  }\n  return getSvgoConfigFromSvgrConfig(config)\n}\n", "import { optimize } from 'svgo'\nimport { getSvgoConfig } from './config'\nimport type { Plugin } from '@svgr/core'\n\nconst svgoPlugin: Plugin = (code, config, state) => {\n  if (!config.svgo) return code\n  const svgoConfig = getSvgoConfig(config, state)\n  const result = optimize(code, { ...svgoConfig, path: state.filePath })\n\n  // @ts-ignore\n  if (result.modernError) {\n    // @ts-ignore\n    throw result.modernError\n  }\n\n  return result.data\n}\n\nexport default svgoPlugin\n"], "names": ["cosmiconfigSync", "optimize"], "mappings": ";;;;;AAGA,MAAM,QAAA,GAAWA,4BAAgB,MAAQ,EAAA;AAAA,EACvC,YAAc,EAAA;AAAA,IACZ,cAAA;AAAA,IACA,SAAA;AAAA,IACA,YAAA;AAAA,IACA,cAAA;AAAA,IACA,cAAA;AAAA,IACA,aAAA;AAAA,IACA,gBAAA;AAAA,IACA,iBAAA;AAAA,IACA,WAAA;AAAA,GACF;AAAA,EACA,SAAW,EAAA,CAAC,MAAW,KAAA,MAAA,IAAU,MAAO,CAAA,MAAA;AAAA,EACxC,KAAO,EAAA,IAAA;AACT,CAAC,CAAA,CAAA;AAED,MAAM,2BAAA,GAA8B,CAAC,MAAwB,KAAA;AAC3D,EAAA,MAAM,MAAS,GAAA,EAAE,SAAW,EAAA,EAAU,EAAA,CAAA;AACtC,EAAA,IAAI,MAAO,CAAA,IAAA,IAAQ,MAAO,CAAA,UAAA,KAAe,KAAO,EAAA;AAC9C,IAAA,MAAA,CAAO,UAAU,aAAgB,GAAA,KAAA,CAAA;AAAA,GACnC;AACA,EAAA,IAAI,OAAO,MAAQ,EAAA;AACjB,IAAA,MAAA,CAAO,UAAU,YAAe,GAAA;AAAA,MAC9B,eAAiB,EAAA,KAAA;AAAA,KACnB,CAAA;AAAA,GACF;AAEA,EAAO,OAAA;AAAA,IACL,OAAS,EAAA;AAAA,MACP;AAAA,QACE,IAAM,EAAA,gBAAA;AAAA,QACN,MAAA;AAAA,OACF;AAAA,MACA,WAAA;AAAA,KACF;AAAA,GACF,CAAA;AACF,CAAA,CAAA;AAEa,MAAA,aAAA,GAAgB,CAAC,MAAA,EAAgB,KAAsB,KAAA;AAClE,EAAA,MAAM,GAAM,GAAA,KAAA,CAAM,QAAY,IAAA,OAAA,CAAQ,GAAI,EAAA,CAAA;AAC1C,EAAA,IAAI,MAAO,CAAA,UAAA;AAAY,IAAA,OAAO,MAAO,CAAA,UAAA,CAAA;AACrC,EAAA,IAAI,OAAO,aAAe,EAAA;AACxB,IAAM,MAAA,UAAA,GAAa,QAAS,CAAA,MAAA,CAAO,GAAG,CAAA,CAAA;AACtC,IAAI,IAAA,UAAA;AAAY,MAAO,OAAA,UAAA,CAAA;AAAA,GACzB;AACA,EAAA,OAAO,4BAA4B,MAAM,CAAA,CAAA;AAC3C,CAAA;;;;;;;;;;;;;;;;;;;;;AC7CA,MAAM,UAAqB,GAAA,CAAC,IAAM,EAAA,MAAA,EAAQ,KAAU,KAAA;AAClD,EAAA,IAAI,CAAC,MAAO,CAAA,IAAA;AAAM,IAAO,OAAA,IAAA,CAAA;AACzB,EAAM,MAAA,UAAA,GAAa,aAAc,CAAA,MAAA,EAAQ,KAAK,CAAA,CAAA;AAC9C,EAAM,MAAA,MAAA,GAASC,cAAS,IAAM,EAAA,aAAA,CAAA,cAAA,CAAA,EAAA,EAAK,aAAL,EAAiB,IAAA,EAAM,KAAM,CAAA,QAAA,EAAU,CAAA,CAAA,CAAA;AAGrE,EAAA,IAAI,OAAO,WAAa,EAAA;AAEtB,IAAA,MAAM,MAAO,CAAA,WAAA,CAAA;AAAA,GACf;AAEA,EAAA,OAAO,MAAO,CAAA,IAAA,CAAA;AAChB;;;;"}