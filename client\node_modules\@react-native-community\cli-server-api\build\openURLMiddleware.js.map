{"version": 3, "names": ["openURLMiddleware", "req", "res", "next", "method", "body", "writeHead", "end", "url", "open", "connect", "use", "json"], "sources": ["../src/openURLMiddleware.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport type {IncomingMessage, ServerResponse} from 'http';\n\nimport {json} from 'body-parser';\nimport connect from 'connect';\nimport open from 'open';\n\n/**\n * Open a URL in the system browser.\n */\nasync function openURLMiddleware(\n  req: IncomingMessage & {\n    // Populated by body-parser\n    body?: Object;\n  },\n  res: ServerResponse,\n  next: (err?: Error) => void,\n) {\n  if (req.method === 'POST') {\n    if (req.body == null) {\n      res.writeHead(400);\n      res.end('Missing request body');\n      return;\n    }\n\n    const {url} = req.body as {url: string};\n\n    await open(url);\n\n    res.writeHead(200);\n    res.end();\n  }\n\n  next();\n}\n\nexport default connect().use(json()).use(openURLMiddleware);\n"], "mappings": ";;;;;;AASA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AAXxB;AACA;AACA;AACA;AACA;AACA;;AAQA;AACA;AACA;AACA,eAAeA,iBAAiB,CAC9BC,GAGC,EACDC,GAAmB,EACnBC,IAA2B,EAC3B;EACA,IAAIF,GAAG,CAACG,MAAM,KAAK,MAAM,EAAE;IACzB,IAAIH,GAAG,CAACI,IAAI,IAAI,IAAI,EAAE;MACpBH,GAAG,CAACI,SAAS,CAAC,GAAG,CAAC;MAClBJ,GAAG,CAACK,GAAG,CAAC,sBAAsB,CAAC;MAC/B;IACF;IAEA,MAAM;MAACC;IAAG,CAAC,GAAGP,GAAG,CAACI,IAAqB;IAEvC,MAAM,IAAAI,eAAI,EAACD,GAAG,CAAC;IAEfN,GAAG,CAACI,SAAS,CAAC,GAAG,CAAC;IAClBJ,GAAG,CAACK,GAAG,EAAE;EACX;EAEAJ,IAAI,EAAE;AACR;AAAC,eAEc,IAAAO,kBAAO,GAAE,CAACC,GAAG,CAAC,IAAAC,kBAAI,GAAE,CAAC,CAACD,GAAG,CAACX,iBAAiB,CAAC;AAAA"}