"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Branch = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const branchSchema = new mongoose_1.default.Schema({
    name: {
        type: String,
        required: true
    },
    description: {
        type: String
    },
    address: {
        type: String
    },
    city: {
        type: String
    },
    state: {
        type: String
    },
    pincode: {
        type: String
    },
    phone: {
        type: String
    },
    location: {
        lat: {
            type: Number
        },
        lng: {
            type: Number
        }
    },
    status: {
        type: String,
        enum: ['active', 'inactive'],
        default: 'active'
    },
    vendorId: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: 'Vendor',
        required: true
    },
    deliveryPartners: [{
            type: mongoose_1.default.Schema.Types.ObjectId,
            ref: 'DeliveryPartner'
        }],
    createdAt: {
        type: Date,
        default: Date.now
    }
});
exports.Branch = mongoose_1.default.model('Branch', branchSchema);
exports.default = exports.Branch;
