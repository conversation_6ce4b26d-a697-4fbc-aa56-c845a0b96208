CMake Warning in D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/./

  has 191 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


CMake Warning:
  Manually-specified variables were not used by the project:

    ANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES


