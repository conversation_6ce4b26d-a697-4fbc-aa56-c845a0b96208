{"version": 3, "names": ["getProjectConfig", "platformName", "folder", "userConfig", "src", "path", "join", "sourceDir", "podfile", "findPodfilePath", "dirname", "xcodeProject", "findXcodeProject", "fs", "readdirSync", "watchModeCommandParams", "automaticPodsInstallation", "assets", "getDependencyConfig", "podspecPath", "findPodspec", "version", "packageJson", "require", "CLIError", "chalk", "underline", "configurations", "scriptPhases", "findPodfilePaths", "findAllPodfilePaths"], "sources": ["../../src/config/index.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nimport chalk from 'chalk';\nimport path from 'path';\nimport fs from 'fs';\nimport findPodfilePath from './findPodfilePath';\nimport findXcodeProject from './findXcodeProject';\nimport findPodspec from './findPodspec';\nimport findAllPodfilePaths from './findAllPodfilePaths';\nimport {\n  IOSProjectParams,\n  IOSDependencyParams,\n  IOSProjectConfig,\n  IOSDependencyConfig,\n} from '@react-native-community/cli-types';\nimport {CLIError} from '@react-native-community/cli-tools';\nimport {BuilderCommand} from '../types';\n\n/**\n * Returns project config by analyzing given folder and applying some user defaults\n * when constructing final object\n */\nexport const getProjectConfig =\n  ({platformName}: BuilderCommand) =>\n  (folder: string, userConfig: IOSProjectParams): IOSProjectConfig | null => {\n    if (!userConfig) {\n      return null;\n    }\n\n    const src = path.join(folder, userConfig.sourceDir ?? '');\n    const podfile = findPodfilePath(src, platformName);\n\n    /**\n     * In certain repos, the Xcode project can be generated by a tool.\n     * The only file that we can assume to exist on disk is `Podfile`.\n     */\n    if (!podfile) {\n      return null;\n    }\n\n    const sourceDir = path.dirname(podfile);\n\n    const xcodeProject = findXcodeProject(fs.readdirSync(sourceDir));\n\n    return {\n      sourceDir,\n      watchModeCommandParams: userConfig.watchModeCommandParams,\n      xcodeProject,\n      automaticPodsInstallation: userConfig.automaticPodsInstallation,\n      assets: userConfig.assets ?? [],\n    };\n  };\n\n/**\n * Make getDependencyConfig follow the same pattern as getProjectConfig\n */\nexport const getDependencyConfig =\n  ({}: BuilderCommand) =>\n  (\n    folder: string,\n    userConfig: IOSDependencyParams | null = {},\n  ): IOSDependencyConfig | null => {\n    if (userConfig === null) {\n      return null;\n    }\n\n    const podspecPath = findPodspec(folder);\n\n    if (!podspecPath) {\n      return null;\n    }\n\n    let version = 'unresolved';\n\n    try {\n      const packageJson = require(path.join(folder, 'package.json'));\n\n      if (packageJson.version) {\n        version = packageJson.version;\n      }\n    } catch {\n      throw new CLIError(\n        `Failed to locate package.json file from ${chalk.underline(\n          folder,\n        )}. This is most likely issue with your node_modules folder being corrupted. Please force install dependencies and try again`,\n      );\n    }\n\n    return {\n      podspecPath,\n      version,\n      configurations: userConfig.configurations || [],\n      scriptPhases: userConfig.scriptPhases || [],\n    };\n  };\n\nexport const findPodfilePaths = findAllPodfilePaths;\n"], "mappings": ";;;;;;AAOA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AACA;AAOA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAA2D;AApB3D;AACA;AACA;AACA;AACA;AACA;AACA;;AAiBA;AACA;AACA;AACA;AACO,MAAMA,gBAAgB,GAC3B,CAAC;EAACC;AAA4B,CAAC,KAC/B,CAACC,MAAc,EAAEC,UAA4B,KAA8B;EACzE,IAAI,CAACA,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EAEA,MAAMC,GAAG,GAAGC,eAAI,CAACC,IAAI,CAACJ,MAAM,EAAEC,UAAU,CAACI,SAAS,IAAI,EAAE,CAAC;EACzD,MAAMC,OAAO,GAAG,IAAAC,wBAAe,EAACL,GAAG,EAAEH,YAAY,CAAC;;EAElD;AACJ;AACA;AACA;EACI,IAAI,CAACO,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EAEA,MAAMD,SAAS,GAAGF,eAAI,CAACK,OAAO,CAACF,OAAO,CAAC;EAEvC,MAAMG,YAAY,GAAG,IAAAC,yBAAgB,EAACC,aAAE,CAACC,WAAW,CAACP,SAAS,CAAC,CAAC;EAEhE,OAAO;IACLA,SAAS;IACTQ,sBAAsB,EAAEZ,UAAU,CAACY,sBAAsB;IACzDJ,YAAY;IACZK,yBAAyB,EAAEb,UAAU,CAACa,yBAAyB;IAC/DC,MAAM,EAAEd,UAAU,CAACc,MAAM,IAAI;EAC/B,CAAC;AACH,CAAC;;AAEH;AACA;AACA;AAFA;AAGO,MAAMC,mBAAmB,GAC9B,CAAC,CAAiB,CAAC,KACnB,CACEhB,MAAc,EACdC,UAAsC,GAAG,CAAC,CAAC,KACZ;EAC/B,IAAIA,UAAU,KAAK,IAAI,EAAE;IACvB,OAAO,IAAI;EACb;EAEA,MAAMgB,WAAW,GAAG,IAAAC,oBAAW,EAAClB,MAAM,CAAC;EAEvC,IAAI,CAACiB,WAAW,EAAE;IAChB,OAAO,IAAI;EACb;EAEA,IAAIE,OAAO,GAAG,YAAY;EAE1B,IAAI;IACF,MAAMC,WAAW,GAAGC,OAAO,CAAClB,eAAI,CAACC,IAAI,CAACJ,MAAM,EAAE,cAAc,CAAC,CAAC;IAE9D,IAAIoB,WAAW,CAACD,OAAO,EAAE;MACvBA,OAAO,GAAGC,WAAW,CAACD,OAAO;IAC/B;EACF,CAAC,CAAC,MAAM;IACN,MAAM,KAAIG,oBAAQ,EACf,2CAA0CC,gBAAK,CAACC,SAAS,CACxDxB,MAAM,CACN,4HAA2H,CAC9H;EACH;EAEA,OAAO;IACLiB,WAAW;IACXE,OAAO;IACPM,cAAc,EAAExB,UAAU,CAACwB,cAAc,IAAI,EAAE;IAC/CC,YAAY,EAAEzB,UAAU,CAACyB,YAAY,IAAI;EAC3C,CAAC;AACH,CAAC;AAAC;AAEG,MAAMC,gBAAgB,GAAGC,4BAAmB;AAAC"}