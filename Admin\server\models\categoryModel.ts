import mongoose from 'mongoose';

const categorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'active'
  },
  imageUrl: {
    type: String
  },
  products: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product'
  }],
  vendorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vendor'
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Add indexes for faster lookups
categorySchema.index({ name: 1 });
categorySchema.index({ vendorId: 1 });
categorySchema.index({ status: 1 });

export const Category = mongoose.model('Category', categorySchema);

export default Category;
