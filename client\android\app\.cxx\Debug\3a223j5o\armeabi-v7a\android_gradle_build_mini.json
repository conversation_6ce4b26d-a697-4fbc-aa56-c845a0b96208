{"buildFiles": ["D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\@react-native-community\\geolocation\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\lottie-react-native\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\android\\CMakeLists.txt", "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-mmkv\\MMKV\\Core\\CMakeLists.txt", "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-svg\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\client\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\android\\app\\.cxx\\Debug\\3a223j5o\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\android\\app\\.cxx\\Debug\\3a223j5o\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"artifactName": "react_codegen_RNVectorIconsSpec", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"artifactName": "react_codegen_rnreanimated", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "armeabi-v7a", "output": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\android\\app\\build\\intermediates\\cxx\\Debug\\3a223j5o\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9cd38d273aa80e0d66466b04adf6ea09\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495638402d0fc5d3374d8dd464a0b611\\transformed\\react-android-0.77.0-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495638402d0fc5d3374d8dd464a0b611\\transformed\\react-android-0.77.0-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}, "core::@1b9a7d546b295b7d0867": {"artifactName": "core", "abi": "armeabi-v7a", "output": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\android\\app\\.cxx\\Debug\\3a223j5o\\armeabi-v7a\\RNMmkvSpec_cxxmodule_autolinked_build\\core\\libcore.a", "runtimeFiles": []}, "react-native-mmkv::@4ae6a1e65d3e68ba0197": {"artifactName": "react-native-mmkv", "abi": "armeabi-v7a", "output": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\android\\app\\build\\intermediates\\cxx\\Debug\\3a223j5o\\obj\\armeabi-v7a\\libreact-native-mmkv.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9cd38d273aa80e0d66466b04adf6ea09\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495638402d0fc5d3374d8dd464a0b611\\transformed\\react-android-0.77.0-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495638402d0fc5d3374d8dd464a0b611\\transformed\\react-android-0.77.0-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"artifactName": "react_codegen_rngesturehandler_codegen", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "armeabi-v7a", "output": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\android\\app\\build\\intermediates\\cxx\\Debug\\3a223j5o\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495638402d0fc5d3374d8dd464a0b611\\transformed\\react-android-0.77.0-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495638402d0fc5d3374d8dd464a0b611\\transformed\\react-android-0.77.0-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9cd38d273aa80e0d66466b04adf6ea09\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so"]}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "armeabi-v7a", "output": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\android\\app\\build\\intermediates\\cxx\\Debug\\3a223j5o\\obj\\armeabi-v7a\\libappmodules.so", "runtimeFiles": ["D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\android\\app\\build\\intermediates\\cxx\\Debug\\3a223j5o\\obj\\armeabi-v7a\\libreact-native-mmkv.so", "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\android\\app\\build\\intermediates\\cxx\\Debug\\3a223j5o\\obj\\armeabi-v7a\\libreact_codegen_safeareacontext.so", "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\android\\app\\build\\intermediates\\cxx\\Debug\\3a223j5o\\obj\\armeabi-v7a\\libreact_codegen_rnscreens.so", "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\android\\app\\build\\intermediates\\cxx\\Debug\\3a223j5o\\obj\\armeabi-v7a\\libreact_codegen_rnsvg.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9cd38d273aa80e0d66466b04adf6ea09\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495638402d0fc5d3374d8dd464a0b611\\transformed\\react-android-0.77.0-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495638402d0fc5d3374d8dd464a0b611\\transformed\\react-android-0.77.0-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so"]}, "react_codegen_lottiereactnative::@0fa4dc904d7e359a99fb": {"artifactName": "react_codegen_lottiereactnative", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65": {"artifactName": "react_codegen_rnsvg", "abi": "armeabi-v7a", "output": "D:\\Zave\\aC5d55GwXZEbEbfnCDrKUR\\ZAVE\\29-05\\client\\android\\app\\build\\intermediates\\cxx\\Debug\\3a223j5o\\obj\\armeabi-v7a\\libreact_codegen_rnsvg.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495638402d0fc5d3374d8dd464a0b611\\transformed\\react-android-0.77.0-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\495638402d0fc5d3374d8dd464a0b611\\transformed\\react-android-0.77.0-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9cd38d273aa80e0d66466b04adf6ea09\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so"]}, "react_codegen_RNMmkvSpec::@7541eabbae598da31a69": {"artifactName": "react_codegen_RNMmkvSpec", "abi": "armeabi-v7a", "runtimeFiles": []}, "react_codegen_RNCGeolocationSpec::@1b959fcb56e23f7716ba": {"artifactName": "react_codegen_RNCGeolocationSpec", "abi": "armeabi-v7a", "runtimeFiles": []}}}