<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\res"><file name="rn_edit_text_material" path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\res\drawable\rn_edit_text_material.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#FDCC00</color></file><file path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">ZAVE</string></file><file path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
        <item name="android:windowDisablePreview">true</item>

    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\generated\res\resValues\debug"><file path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\generated\res\resValues\debug\values\gradleResValues.xml" qualifiers=""><integer name="react_native_dev_server_port">8081</integer></file></source></dataSet><mergedItems/></merger>