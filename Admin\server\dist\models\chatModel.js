"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Conversation = exports.Message = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const messageSchema = new mongoose_1.default.Schema({
    sender: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    content: {
        type: String,
        required: true
    },
    timestamp: {
        type: Date,
        default: Date.now
    },
    read: {
        type: Boolean,
        default: false
    }
});
const conversationSchema = new mongoose_1.default.Schema({
    participants: [{
            type: mongoose_1.default.Schema.Types.ObjectId,
            ref: 'User'
        }],
    messages: [messageSchema],
    lastUpdated: {
        type: Date,
        default: Date.now
    },
    title: {
        type: String
    },
    type: {
        type: String,
        enum: ['direct', 'group'],
        default: 'direct'
    }
});
exports.Message = mongoose_1.default.model('Message', messageSchema);
exports.Conversation = mongoose_1.default.model('Conversation', conversationSchema);
exports.default = { Message: exports.Message, Conversation: exports.Conversation };
