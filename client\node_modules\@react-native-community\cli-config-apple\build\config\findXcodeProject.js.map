{"version": 3, "names": ["findXcodeProject", "files", "sortedFiles", "sort", "i", "length", "fileName", "ext", "path", "extname", "projectPath", "dirname", "name", "isWorkspace"], "sources": ["../../src/config/findXcodeProject.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nimport path from 'path';\nimport {IOSProjectInfo} from '@react-native-community/cli-types';\n\nfunction findXcodeProject(files: Array<string>): IOSProjectInfo | null {\n  const sortedFiles = files.sort();\n\n  for (let i = sortedFiles.length - 1; i >= 0; i--) {\n    const fileName = files[i];\n    const ext = path.extname(fileName);\n    const projectPath = path.dirname(fileName);\n\n    if (ext === '.xcworkspace') {\n      return {\n        name: fileName,\n        path: projectPath,\n        isWorkspace: true,\n      };\n    }\n    if (ext === '.xcodeproj') {\n      return {\n        name: fileName,\n        path: projectPath,\n        isWorkspace: false,\n      };\n    }\n  }\n\n  return null;\n}\n\nexport default findXcodeProject;\n"], "mappings": ";;;;;;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AARxB;AACA;AACA;AACA;AACA;AACA;AACA;;AAKA,SAASA,gBAAgB,CAACC,KAAoB,EAAyB;EACrE,MAAMC,WAAW,GAAGD,KAAK,CAACE,IAAI,EAAE;EAEhC,KAAK,IAAIC,CAAC,GAAGF,WAAW,CAACG,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAChD,MAAME,QAAQ,GAAGL,KAAK,CAACG,CAAC,CAAC;IACzB,MAAMG,GAAG,GAAGC,eAAI,CAACC,OAAO,CAACH,QAAQ,CAAC;IAClC,MAAMI,WAAW,GAAGF,eAAI,CAACG,OAAO,CAACL,QAAQ,CAAC;IAE1C,IAAIC,GAAG,KAAK,cAAc,EAAE;MAC1B,OAAO;QACLK,IAAI,EAAEN,QAAQ;QACdE,IAAI,EAAEE,WAAW;QACjBG,WAAW,EAAE;MACf,CAAC;IACH;IACA,IAAIN,GAAG,KAAK,YAAY,EAAE;MACxB,OAAO;QACLK,IAAI,EAAEN,QAAQ;QACdE,IAAI,EAAEE,WAAW;QACjBG,WAAW,EAAE;MACf,CAAC;IACH;EACF;EAEA,OAAO,IAAI;AACb;AAAC,eAEcb,gBAAgB;AAAA"}