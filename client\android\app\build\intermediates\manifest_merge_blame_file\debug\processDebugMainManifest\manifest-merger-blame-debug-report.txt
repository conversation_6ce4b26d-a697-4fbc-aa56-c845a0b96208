1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.grocery_app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:3:5-67
11-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
12-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:4:5-79
12-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:4:22-76
13    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
13-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:5:5-81
13-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:5:22-78
14    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
14-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:6:5-85
14-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:6:22-82
15    <!--
16    This manifest file is used only by Gradle to configure debug-only capabilities
17    for React Native Apps.
18    -->
19    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /> <!-- Include required permissions for Google Maps API to run. -->
19-->[com.facebook.react:react-android:0.77.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\495638402d0fc5d3374d8dd464a0b611\transformed\react-android-0.77.0-debug\AndroidManifest.xml:16:5-78
19-->[com.facebook.react:react-android:0.77.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\495638402d0fc5d3374d8dd464a0b611\transformed\react-android-0.77.0-debug\AndroidManifest.xml:16:22-75
20    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
20-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
20-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:22-76
21
22    <uses-feature
22-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
23        android:glEsVersion="0x00020000"
23-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
24        android:required="true" />
24-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
25
26    <queries>
26-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
27
28        <!-- Needs to be explicitly declared on Android R+ -->
29        <package android:name="com.google.android.apps.maps" />
29-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
29-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
30    </queries>
31
32    <uses-permission android:name="android.permission.WAKE_LOCK" />
32-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
32-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:22-65
33    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
33-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
33-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:22-78
34    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
34-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
34-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
35
36    <permission
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
37        android:name="com.grocery_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
38        android:protectionLevel="signature" />
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
39
40    <uses-permission android:name="com.grocery_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
41
42    <application
42-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:9:5-37:19
43        android:name="com.grocery_app.MainApplication"
43-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:10:7-38
44        android:allowBackup="false"
44-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:14:7-34
45        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e41dd45e1a637e1ffaf746e59f946ed1\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
46        android:debuggable="true"
47        android:extractNativeLibs="false"
48        android:hardwareAccelerated="true"
48-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:17:7-41
49        android:icon="@mipmap/ic_launcher"
49-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:12:7-41
50        android:label="@string/app_name"
50-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:11:7-39
51        android:roundIcon="@mipmap/ic_launcher_round"
51-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:13:7-52
52        android:supportsRtl="true"
52-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:18:7-33
53        android:theme="@style/AppTheme"
53-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:16:7-38
54        android:usesCleartextTraffic="true" >
54-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:15:7-42
55        <meta-data
55-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:20:7-23:9
56            android:name="com.google.android.geo.API_KEY"
56-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:21:7-52
57            android:value="AIzaSyB3-FJ4nRfZz19GUndm70CKxH94-rZ9uKo" />
57-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:22:7-62
58
59        <activity
59-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:25:7-36:18
60            android:name="com.grocery_app.MainActivity"
60-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:26:9-37
61            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
61-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:28:9-118
62            android:exported="true"
62-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:31:9-32
63            android:label="@string/app_name"
63-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:27:9-41
64            android:launchMode="singleTask"
64-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:29:9-40
65            android:windowSoftInputMode="adjustNothing" >
65-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:30:9-52
66            <intent-filter>
66-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:32:9-35:25
67                <action android:name="android.intent.action.MAIN" />
67-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:33:13-65
67-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:33:21-62
68
69                <category android:name="android.intent.category.LAUNCHER" />
69-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:34:13-73
69-->D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\AndroidManifest.xml:34:23-70
70            </intent-filter>
71        </activity>
72        <activity
72-->[com.facebook.react:react-android:0.77.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\495638402d0fc5d3374d8dd464a0b611\transformed\react-android-0.77.0-debug\AndroidManifest.xml:19:9-21:40
73            android:name="com.facebook.react.devsupport.DevSettingsActivity"
73-->[com.facebook.react:react-android:0.77.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\495638402d0fc5d3374d8dd464a0b611\transformed\react-android-0.77.0-debug\AndroidManifest.xml:20:13-77
74            android:exported="false" />
74-->[com.facebook.react:react-android:0.77.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\495638402d0fc5d3374d8dd464a0b611\transformed\react-android-0.77.0-debug\AndroidManifest.xml:21:13-37
75
76        <meta-data
76-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e433cfd4abda164cbeb2e06c3c98800f\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
77            android:name="com.google.android.gms.version"
77-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e433cfd4abda164cbeb2e06c3c98800f\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
78            android:value="@integer/google_play_services_version" /> <!-- Needs to be explicitly declared on P+ -->
78-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e433cfd4abda164cbeb2e06c3c98800f\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
79        <uses-library
79-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
80            android:name="org.apache.http.legacy"
80-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
81            android:required="false" />
81-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276d863af8c9d2d1d1cdff2f09181a62\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
82
83        <activity
83-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c7fa47cfdf544d81d1c083c1fbfec41\transformed\play-services-base-18.2.0\AndroidManifest.xml:20:9-22:45
84            android:name="com.google.android.gms.common.api.GoogleApiActivity"
84-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c7fa47cfdf544d81d1c083c1fbfec41\transformed\play-services-base-18.2.0\AndroidManifest.xml:20:19-85
85            android:exported="false"
85-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c7fa47cfdf544d81d1c083c1fbfec41\transformed\play-services-base-18.2.0\AndroidManifest.xml:22:19-43
86            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
86-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1c7fa47cfdf544d81d1c083c1fbfec41\transformed\play-services-base-18.2.0\AndroidManifest.xml:21:19-78
87
88        <provider
88-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
89            android:name="androidx.startup.InitializationProvider"
89-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
90            android:authorities="com.grocery_app.androidx-startup"
90-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
91            android:exported="false" >
91-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
92            <meta-data
92-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
93                android:name="androidx.work.WorkManagerInitializer"
93-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
94                android:value="androidx.startup" />
94-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
95            <meta-data
95-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e261940f64ed4eac965b00d00d0dbd9\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
96                android:name="androidx.emoji2.text.EmojiCompatInitializer"
96-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e261940f64ed4eac965b00d00d0dbd9\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
97                android:value="androidx.startup" />
97-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e261940f64ed4eac965b00d00d0dbd9\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
98            <meta-data
98-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79c33e0b2a455bd8ac41666e964e403e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
99                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
99-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79c33e0b2a455bd8ac41666e964e403e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
100                android:value="androidx.startup" />
100-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\79c33e0b2a455bd8ac41666e964e403e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
101            <meta-data
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
103                android:value="androidx.startup" />
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
104        </provider>
105
106        <service
106-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
107            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
107-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
108            android:directBootAware="false"
108-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
109            android:enabled="@bool/enable_system_alarm_service_default"
109-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
110            android:exported="false" />
110-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
111        <service
111-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
112            android:name="androidx.work.impl.background.systemjob.SystemJobService"
112-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
113            android:directBootAware="false"
113-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
114            android:enabled="@bool/enable_system_job_service_default"
114-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
115            android:exported="true"
115-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
116            android:permission="android.permission.BIND_JOB_SERVICE" />
116-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
117        <service
117-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
118            android:name="androidx.work.impl.foreground.SystemForegroundService"
118-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
119            android:directBootAware="false"
119-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
120            android:enabled="@bool/enable_system_foreground_service_default"
120-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
121            android:exported="false" />
121-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
122
123        <receiver
123-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
124            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
124-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
125            android:directBootAware="false"
125-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
126            android:enabled="true"
126-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
127            android:exported="false" />
127-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
128        <receiver
128-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
129            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
129-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
130            android:directBootAware="false"
130-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
131            android:enabled="false"
131-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
132            android:exported="false" >
132-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
133            <intent-filter>
133-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
134                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
134-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
134-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
135                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
135-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
135-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
136            </intent-filter>
137        </receiver>
138        <receiver
138-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
139            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
139-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
140            android:directBootAware="false"
140-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
141            android:enabled="false"
141-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
142            android:exported="false" >
142-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
143            <intent-filter>
143-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
144                <action android:name="android.intent.action.BATTERY_OKAY" />
144-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
144-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
145                <action android:name="android.intent.action.BATTERY_LOW" />
145-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
145-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
146            </intent-filter>
147        </receiver>
148        <receiver
148-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
149            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
149-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
150            android:directBootAware="false"
150-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
151            android:enabled="false"
151-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
152            android:exported="false" >
152-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
153            <intent-filter>
153-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
154                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
154-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
154-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
155                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
155-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
155-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
156            </intent-filter>
157        </receiver>
158        <receiver
158-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
159            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
159-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
160            android:directBootAware="false"
160-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
161            android:enabled="false"
161-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
162            android:exported="false" >
162-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
163            <intent-filter>
163-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
164                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
164-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
164-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
165            </intent-filter>
166        </receiver>
167        <receiver
167-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
168            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
168-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
169            android:directBootAware="false"
169-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
170            android:enabled="false"
170-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
171            android:exported="false" >
171-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
172            <intent-filter>
172-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
173                <action android:name="android.intent.action.BOOT_COMPLETED" />
173-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:117:17-79
173-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:117:25-76
174                <action android:name="android.intent.action.TIME_SET" />
174-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
174-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
175                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
175-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
175-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
176            </intent-filter>
177        </receiver>
178        <receiver
178-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
179            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
179-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
180            android:directBootAware="false"
180-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
181            android:enabled="@bool/enable_system_alarm_service_default"
181-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
182            android:exported="false" >
182-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
183            <intent-filter>
183-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
184                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
184-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
184-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
185            </intent-filter>
186        </receiver>
187        <receiver
187-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
188            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
188-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
189            android:directBootAware="false"
189-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
190            android:enabled="true"
190-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
191            android:exported="true"
191-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
192            android:permission="android.permission.DUMP" >
192-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
193            <intent-filter>
193-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
194                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
194-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
194-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e19f6da748b218207b00fdf28ea30e88\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
195            </intent-filter>
196        </receiver>
197        <receiver
197-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
198            android:name="androidx.profileinstaller.ProfileInstallReceiver"
198-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
199            android:directBootAware="false"
199-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
200            android:enabled="true"
200-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
201            android:exported="true"
201-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
202            android:permission="android.permission.DUMP" >
202-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
203            <intent-filter>
203-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
204                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
204-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
204-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
205            </intent-filter>
206            <intent-filter>
206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
207                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
207-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
207-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
208            </intent-filter>
209            <intent-filter>
209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
210                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
210-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
210-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
211            </intent-filter>
212            <intent-filter>
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
213                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
213-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
213-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3ef42d9127548140693947f7b89cda39\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
214            </intent-filter>
215        </receiver>
216
217        <service
217-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d2c05ebeb2e9c00c30592bdef558d9c\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
218            android:name="androidx.room.MultiInstanceInvalidationService"
218-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d2c05ebeb2e9c00c30592bdef558d9c\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
219            android:directBootAware="true"
219-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d2c05ebeb2e9c00c30592bdef558d9c\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
220            android:exported="false" />
220-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6d2c05ebeb2e9c00c30592bdef558d9c\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
221
222        <meta-data
222-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b6ded0ceaee573029c2dcfb22b2cdb\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
223            android:name="com.facebook.soloader.enabled"
223-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b6ded0ceaee573029c2dcfb22b2cdb\transformed\soloader-0.12.1\AndroidManifest.xml:13:13-57
224            android:value="false" />
224-->[com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2b6ded0ceaee573029c2dcfb22b2cdb\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
225    </application>
226
227</manifest>
