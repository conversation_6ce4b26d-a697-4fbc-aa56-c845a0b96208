"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.EnsureSingleNavigator = EnsureSingleNavigator;
exports.SingleNavigatorContext = void 0;
var React = _interopRequireWildcard(require("react"));
var _jsxRuntime = require("react/jsx-runtime");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
const MULTIPLE_NAVIGATOR_ERROR = `Another navigator is already registered for this container. You likely have multiple navigators under a single "NavigationContainer" or "Screen". Make sure each navigator is under a separate "Screen" container. See https://reactnavigation.org/docs/nesting-navigators for a guide on nesting.`;
const SingleNavigatorContext = exports.SingleNavigatorContext = /*#__PURE__*/React.createContext(undefined);

/**
 * Component which ensures that there's only one navigator nested under it.
 */
function EnsureSingleNavigator({
  children
}) {
  const navigatorKeyRef = React.useRef();
  const value = React.useMemo(() => ({
    register(key) {
      const currentKey = navigatorKeyRef.current;
      if (currentKey !== undefined && key !== currentKey) {
        throw new Error(MULTIPLE_NAVIGATOR_ERROR);
      }
      navigatorKeyRef.current = key;
    },
    unregister(key) {
      const currentKey = navigatorKeyRef.current;
      if (key !== currentKey) {
        return;
      }
      navigatorKeyRef.current = undefined;
    }
  }), []);
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(SingleNavigatorContext.Provider, {
    value: value,
    children: children
  });
}
//# sourceMappingURL=EnsureSingleNavigator.js.map