{"name": "vendor-management-server", "version": "1.0.0", "description": "Backend server for vendor management system", "main": "index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon --exec ts-node index.ts", "build": "tsc"}, "dependencies": {"bcryptjs": "^2.4.3", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.3", "multer": "^1.4.5-lts.1", "socket.io": "^4.8.1", "uuid": "^9.0.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/cookie-parser": "^1.4.3", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jsonwebtoken": "^9.0.1", "@types/multer": "^1.4.7", "@types/node": "^18.15.11", "@types/uuid": "^9.0.2", "nodemon": "^2.0.22", "ts-node": "^10.9.1", "typescript": "^5.0.4"}}