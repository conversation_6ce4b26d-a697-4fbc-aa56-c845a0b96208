{"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\nimport { ConfigAPI, types as t, NodePath, template } from '@babel/core'\n\nexport interface Value {\n  value: string\n  newValue: string | boolean | number\n  literal?: boolean\n}\n\nexport interface Options {\n  values: Value[]\n}\n\nconst addJSXAttribute = (api: ConfigAPI, opts: Options) => {\n  const getAttributeValue = (\n    value: string | boolean | number,\n    literal?: boolean,\n  ) => {\n    if (typeof value === 'string' && literal) {\n      return t.jsxExpressionContainer(\n        (template.ast(value) as t.ExpressionStatement).expression,\n      )\n    }\n\n    if (typeof value === 'string') {\n      return t.stringLiteral(value)\n    }\n\n    if (typeof value === 'boolean') {\n      return t.jsxExpressionContainer(t.booleanLiteral(value))\n    }\n\n    if (typeof value === 'number') {\n      return t.jsxExpressionContainer(t.numericLiteral(value))\n    }\n\n    return null\n  }\n\n  return {\n    visitor: {\n      JSXAttribute(path: NodePath<t.JSXAttribute>) {\n        const valuePath = path.get('value')\n        if (!valuePath.isStringLiteral()) return\n\n        opts.values.forEach(({ value, newValue, literal }) => {\n          if (!valuePath.isStringLiteral({ value })) return\n          const attributeValue = getAttributeValue(newValue, literal)\n          if (attributeValue) {\n            valuePath.replaceWith(attributeValue)\n          }\n        })\n      },\n    },\n  }\n}\n\nexport default addJSXAttribute\n"], "names": ["t", "template"], "mappings": ";;;;AAaM,MAAA,eAAA,GAAkB,CAAC,GAAA,EAAgB,IAAkB,KAAA;AACzD,EAAM,MAAA,iBAAA,GAAoB,CACxB,KAAA,EACA,OACG,KAAA;AACH,IAAI,IAAA,OAAO,KAAU,KAAA,QAAA,IAAY,OAAS,EAAA;AACxC,MAAA,OAAOA,UAAE,CAAA,sBAAA;AAAA,QACNC,aAAA,CAAS,GAAI,CAAA,KAAK,CAA4B,CAAA,UAAA;AAAA,OACjD,CAAA;AAAA,KACF;AAEA,IAAI,IAAA,OAAO,UAAU,QAAU,EAAA;AAC7B,MAAO,OAAAD,UAAA,CAAE,cAAc,KAAK,CAAA,CAAA;AAAA,KAC9B;AAEA,IAAI,IAAA,OAAO,UAAU,SAAW,EAAA;AAC9B,MAAA,OAAOA,UAAE,CAAA,sBAAA,CAAuBA,UAAE,CAAA,cAAA,CAAe,KAAK,CAAC,CAAA,CAAA;AAAA,KACzD;AAEA,IAAI,IAAA,OAAO,UAAU,QAAU,EAAA;AAC7B,MAAA,OAAOA,UAAE,CAAA,sBAAA,CAAuBA,UAAE,CAAA,cAAA,CAAe,KAAK,CAAC,CAAA,CAAA;AAAA,KACzD;AAEA,IAAO,OAAA,IAAA,CAAA;AAAA,GACT,CAAA;AAEA,EAAO,OAAA;AAAA,IACL,OAAS,EAAA;AAAA,MACP,aAAa,IAAgC,EAAA;AAC3C,QAAM,MAAA,SAAA,GAAY,IAAK,CAAA,GAAA,CAAI,OAAO,CAAA,CAAA;AAClC,QAAI,IAAA,CAAC,UAAU,eAAgB,EAAA;AAAG,UAAA,OAAA;AAElC,QAAA,IAAA,CAAK,OAAO,OAAQ,CAAA,CAAC,EAAE,KAAO,EAAA,QAAA,EAAU,SAAc,KAAA;AACpD,UAAA,IAAI,CAAC,SAAA,CAAU,eAAgB,CAAA,EAAE,OAAO,CAAA;AAAG,YAAA,OAAA;AAC3C,UAAM,MAAA,cAAA,GAAiB,iBAAkB,CAAA,QAAA,EAAU,OAAO,CAAA,CAAA;AAC1D,UAAA,IAAI,cAAgB,EAAA;AAClB,YAAA,SAAA,CAAU,YAAY,cAAc,CAAA,CAAA;AAAA,WACtC;AAAA,SACD,CAAA,CAAA;AAAA,OACH;AAAA,KACF;AAAA,GACF,CAAA;AACF;;;;"}