import axios from "axios"
import { BASE_URL } from "./config"

export const getAllBranches = async () => {
    try {
        const response = await axios.get(`${BASE_URL}/branches`)
        return response.data
    } catch (error) {
        console.log("Error fetching branches", error)
        return { success: false, branches: [] }
    }
}

export const getDefaultBranch = async () => {
    try {
        const response = await getAllBranches()
        if (response.success && response.branches.length > 0) {
            // Return the first active branch as default
            return response.branches[0]
        }
        return null
    } catch (error) {
        console.log("Error getting default branch", error)
        return null
    }
}
