ninja: Entering directory `D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\node_modules\react-native-reanimated\android\.cxx\Debug\252f2v2v\armeabi-v7a'
[0/2] Re-checking globbed directories...
[1/37] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/c373fb0cb0205ac8156540e3132b2685/Registries/WorkletRuntimeRegistry.cpp.o
[2/37] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/7519ac08560a20b167be15fe9fbea7e4/cpp/worklets/Tools/ReanimatedJSIUtils.cpp.o
[3/37] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/7519ac08560a20b167be15fe9fbea7e4/cpp/worklets/Tools/JSISerializer.cpp.o
[4/37] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/8381e4f2241b07a247a8edec969abc0e/Common/cpp/worklets/Tools/JSScheduler.cpp.o
[5/37] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/8381e4f2241b07a247a8edec969abc0e/Common/cpp/worklets/Tools/AsyncQueue.cpp.o
[6/37] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/8381e4f2241b07a247a8edec969abc0e/Common/cpp/worklets/Tools/JSLogger.cpp.o
[7/37] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/5902132a011a07009ebc4c1979b47fca/worklets/Tools/WorkletEventHandler.cpp.o
[8/37] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/c373fb0cb0205ac8156540e3132b2685/Registries/EventHandlerRegistry.cpp.o
[9/37] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/7519ac08560a20b167be15fe9fbea7e4/cpp/worklets/SharedItems/Shareables.cpp.o
[10/37] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/7519ac08560a20b167be15fe9fbea7e4/cpp/worklets/Tools/ReanimatedVersion.cpp.o
[11/37] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/8381e4f2241b07a247a8edec969abc0e/Common/cpp/worklets/Tools/UIScheduler.cpp.o
[12/37] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/ff39682c307d56e3affbd2968b272f0a/ReanimatedHermesRuntime.cpp.o
[13/37] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/c373fb0cb0205ac8156540e3132b2685/WorkletRuntime/ReanimatedRuntime.cpp.o
[14/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/bc997d19da1574dd9ca452f37fd86245/AnimatedSensorModule.cpp.o
[15/37] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/c373fb0cb0205ac8156540e3132b2685/WorkletRuntime/WorkletRuntime.cpp.o
[16/37] Building CXX object src/main/cpp/worklets/CMakeFiles/worklets.dir/ff39682c307d56e3affbd2968b272f0a/WorkletRuntimeDecorator.cpp.o
[17/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/5902132a011a07009ebc4c1979b47fca/reanimated/Fabric/PropsRegistry.cpp.o
[18/37] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\252f2v2v\obj\armeabi-v7a\libworklets.so
[19/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/5384bd35cebe0339a8534868eb1525ec/LayoutAnimationsManager.cpp.o
[20/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/903cdc38e100b3e8f688e82b49cafa56/Fabric/ReanimatedMountHook.cpp.o
[21/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/5902132a011a07009ebc4c1979b47fca/reanimated/Tools/FeaturesConfig.cpp.o
[22/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/5384bd35cebe0339a8534868eb1525ec/LayoutAnimationsUtils.cpp.o
[23/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/903cdc38e100b3e8f688e82b49cafa56/Fabric/ShadowTreeCloner.cpp.o
[24/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/903cdc38e100b3e8f688e82b49cafa56/Fabric/ReanimatedCommitHook.cpp.o
[25/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/0ece905a2c2bf93f9c78f535a359f32b/ReanimatedWorkletRuntimeDecorator.cpp.o
[26/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/0ece905a2c2bf93f9c78f535a359f32b/UIRuntimeDecorator.cpp.o
[27/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/de26f8cead47227ca676f03d1d571aaf/NativeReanimatedModuleSpec.cpp.o
[28/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/0ece905a2c2bf93f9c78f535a359f32b/RNRuntimeDecorator.cpp.o
[29/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/PlatformLogger.cpp.o
[30/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/5384bd35cebe0339a8534868eb1525ec/LayoutAnimationsProxy.cpp.o
[31/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o
[32/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/AndroidUIScheduler.cpp.o
[33/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o
[34/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/de26f8cead47227ca676f03d1d571aaf/NativeReanimatedModule.cpp.o
[35/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o
[36/37] Building CXX object src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o
[37/37] Linking CXX shared library ..\..\..\..\build\intermediates\cxx\Debug\252f2v2v\obj\armeabi-v7a\libreanimated.so
