"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleUploadErrors = exports.uploadProductImages = exports.uploadCategoryImage = exports.uploadDeliveryPartnerPhoto = void 0;
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const uuid_1 = require("uuid");
const fs_1 = __importDefault(require("fs"));
// Ensure upload directories exist
const createDirIfNotExists = (dirPath) => {
    if (!fs_1.default.existsSync(dirPath)) {
        fs_1.default.mkdirSync(dirPath, { recursive: true });
    }
};
// Create upload directories
createDirIfNotExists(path_1.default.join(__dirname, '../uploads'));
createDirIfNotExists(path_1.default.join(__dirname, '../uploads/delivery-partners'));
createDirIfNotExists(path_1.default.join(__dirname, '../uploads/categories'));
createDirIfNotExists(path_1.default.join(__dirname, '../uploads/products'));
// Configure storage for delivery partner photos
const deliveryPartnerStorage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        cb(null, path_1.default.join(__dirname, '../uploads/delivery-partners'));
    },
    filename: (req, file, cb) => {
        const uniqueFilename = `${(0, uuid_1.v4)()}${path_1.default.extname(file.originalname)}`;
        cb(null, uniqueFilename);
    }
});
// Configure storage for category images
const categoryStorage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        cb(null, path_1.default.join(__dirname, '../uploads/categories'));
    },
    filename: (req, file, cb) => {
        const uniqueFilename = `${(0, uuid_1.v4)()}${path_1.default.extname(file.originalname)}`;
        cb(null, uniqueFilename);
    }
});
// Configure storage for product images
const productStorage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        cb(null, path_1.default.join(__dirname, '../uploads/products'));
    },
    filename: (req, file, cb) => {
        const uniqueFilename = `${(0, uuid_1.v4)()}${path_1.default.extname(file.originalname)}`;
        cb(null, uniqueFilename);
    }
});
// File filter to allow only images
const fileFilter = (req, file, cb) => {
    const allowedFileTypes = /jpeg|jpg|png|gif/;
    const extname = allowedFileTypes.test(path_1.default.extname(file.originalname).toLowerCase());
    const mimetype = allowedFileTypes.test(file.mimetype);
    if (extname && mimetype) {
        return cb(null, true);
    }
    else {
        cb(new Error('Only image files are allowed!'));
    }
};
// Create multer instances for different upload types
exports.uploadDeliveryPartnerPhoto = (0, multer_1.default)({
    storage: deliveryPartnerStorage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB max file size
    fileFilter
}).single('photo');
exports.uploadCategoryImage = (0, multer_1.default)({
    storage: categoryStorage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB max file size
    fileFilter
}).single('image');
exports.uploadProductImages = (0, multer_1.default)({
    storage: productStorage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB max file size
    fileFilter
}).array('images', 5); // Allow up to 5 images
// Middleware to handle file upload errors
const handleUploadErrors = (req, res, next) => {
    return (err) => {
        if (err instanceof multer_1.default.MulterError) {
            if (err.code === 'LIMIT_FILE_SIZE') {
                return res.status(400).json({
                    success: false,
                    message: 'File too large. Maximum size is 5MB.'
                });
            }
            return res.status(400).json({
                success: false,
                message: err.message
            });
        }
        else if (err) {
            return res.status(400).json({
                success: false,
                message: err.message
            });
        }
        next();
    };
};
exports.handleUploadErrors = handleUploadErrors;
