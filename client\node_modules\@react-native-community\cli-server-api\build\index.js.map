{"version": 3, "names": ["createDevServerMiddleware", "options", "messageSocketEndpoint", "createMessageSocketEndpoint", "broadcast", "eventsSocketEndpoint", "createEventsSocketEndpoint", "middleware", "connect", "use", "securityHeadersMiddleware", "compression", "nocache", "indexPageMiddleware", "openStackFrameMiddleware", "openURLMiddleware", "statusPageMiddleware", "rawBodyMiddleware", "systraceProfileMiddleware", "_req", "res", "end", "errorhandler", "watchFolders", "for<PERSON>ach", "folder", "serveStatic", "websocketEndpoints", "server"], "sources": ["../src/index.ts"], "sourcesContent": ["import http from 'http';\n\nimport compression from 'compression';\nimport connect from 'connect';\nimport errorhandler from 'errorhandler';\nimport nocache from 'nocache';\nimport serveStatic from 'serve-static';\n\nimport indexPageMiddleware from './indexPageMiddleware';\nimport openStackFrameMiddleware from './openStackFrameMiddleware';\nimport openURLMiddleware from './openURLMiddleware';\nimport rawBodyMiddleware from './rawBodyMiddleware';\nimport securityHeadersMiddleware from './securityHeadersMiddleware';\nimport statusPageMiddleware from './statusPageMiddleware';\nimport systraceProfileMiddleware from './systraceProfileMiddleware';\n\nimport createMessageSocketEndpoint from './websocket/createMessageSocketEndpoint';\nimport createEventsSocketEndpoint from './websocket/createEventsSocketEndpoint';\n\ntype MiddlewareOptions = {\n  host?: string;\n  watchFolders: ReadonlyArray<string>;\n  port: number;\n};\n\nexport function createDevServerMiddleware(options: MiddlewareOptions) {\n  const messageSocketEndpoint = createMessageSocketEndpoint();\n  const broadcast = messageSocketEndpoint.broadcast;\n\n  const eventsSocketEndpoint = createEventsSocketEndpoint(broadcast);\n\n  const middleware = connect()\n    .use(securityHeadersMiddleware(options))\n    // @ts-ignore compression and connect types mismatch\n    .use(compression())\n    .use(nocache())\n    .use('/', indexPageMiddleware)\n    .use('/open-stack-frame', openStackFrameMiddleware(options))\n    .use('/open-url', openURLMiddleware)\n    .use('/status', statusPageMiddleware)\n    // TODO: Remove. Requires standardized JSON body parsing support in Metro.\n    .use('/symbolicate', rawBodyMiddleware)\n    // @ts-ignore mismatch\n    .use('/systrace', systraceProfileMiddleware)\n    .use('/reload', (_req: http.IncomingMessage, res: http.ServerResponse) => {\n      broadcast('reload');\n      res.end('OK');\n    })\n    // @ts-ignore mismatch\n    .use(errorhandler());\n\n  options.watchFolders.forEach((folder) => {\n    // @ts-ignore mismatch between express and connect middleware types\n    middleware.use(serveStatic(folder));\n  });\n\n  return {\n    websocketEndpoints: {\n      '/message': messageSocketEndpoint.server,\n      '/events': eventsSocketEndpoint.server,\n    },\n    messageSocketEndpoint,\n    eventsSocketEndpoint,\n    middleware,\n  };\n}\n"], "mappings": ";;;;;;AAEA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAgF;AAQzE,SAASA,yBAAyB,CAACC,OAA0B,EAAE;EACpE,MAAMC,qBAAqB,GAAG,IAAAC,oCAA2B,GAAE;EAC3D,MAAMC,SAAS,GAAGF,qBAAqB,CAACE,SAAS;EAEjD,MAAMC,oBAAoB,GAAG,IAAAC,mCAA0B,EAACF,SAAS,CAAC;EAElE,MAAMG,UAAU,GAAG,IAAAC,kBAAO,GAAE,CACzBC,GAAG,CAAC,IAAAC,kCAAyB,EAACT,OAAO,CAAC;EACvC;EAAA,CACCQ,GAAG,CAAC,IAAAE,sBAAW,GAAE,CAAC,CAClBF,GAAG,CAAC,IAAAG,kBAAO,GAAE,CAAC,CACdH,GAAG,CAAC,GAAG,EAAEI,4BAAmB,CAAC,CAC7BJ,GAAG,CAAC,mBAAmB,EAAE,IAAAK,iCAAwB,EAACb,OAAO,CAAC,CAAC,CAC3DQ,GAAG,CAAC,WAAW,EAAEM,0BAAiB,CAAC,CACnCN,GAAG,CAAC,SAAS,EAAEO,6BAAoB;EACpC;EAAA,CACCP,GAAG,CAAC,cAAc,EAAEQ,0BAAiB;EACtC;EAAA,CACCR,GAAG,CAAC,WAAW,EAAES,kCAAyB,CAAC,CAC3CT,GAAG,CAAC,SAAS,EAAE,CAACU,IAA0B,EAAEC,GAAwB,KAAK;IACxEhB,SAAS,CAAC,QAAQ,CAAC;IACnBgB,GAAG,CAACC,GAAG,CAAC,IAAI,CAAC;EACf,CAAC;EACD;EAAA,CACCZ,GAAG,CAAC,IAAAa,uBAAY,GAAE,CAAC;EAEtBrB,OAAO,CAACsB,YAAY,CAACC,OAAO,CAAEC,MAAM,IAAK;IACvC;IACAlB,UAAU,CAACE,GAAG,CAAC,IAAAiB,sBAAW,EAACD,MAAM,CAAC,CAAC;EACrC,CAAC,CAAC;EAEF,OAAO;IACLE,kBAAkB,EAAE;MAClB,UAAU,EAAEzB,qBAAqB,CAAC0B,MAAM;MACxC,SAAS,EAAEvB,oBAAoB,CAACuB;IAClC,CAAC;IACD1B,qBAAqB;IACrBG,oBAAoB;IACpBE;EACF,CAAC;AACH"}