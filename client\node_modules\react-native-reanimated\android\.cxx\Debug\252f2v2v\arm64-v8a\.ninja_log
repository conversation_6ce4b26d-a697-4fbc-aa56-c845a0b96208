# ninja log v5
4	49	0	D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/.cxx/Debug/252f2v2v/arm64-v8a/CMakeFiles/cmake.verify_globs	78c736d6ce5b8180
3	3704	7702041293275323	src/main/cpp/worklets/CMakeFiles/worklets.dir/c373fb0cb0205ac8156540e3132b2685/Registries/WorkletRuntimeRegistry.cpp.o	314e6f044dccda33
23	3910	7702041295248708	src/main/cpp/worklets/CMakeFiles/worklets.dir/7519ac08560a20b167be15fe9fbea7e4/cpp/worklets/Tools/ReanimatedJSIUtils.cpp.o	914a3cc162587410
71	4892	7702041304739531	src/main/cpp/worklets/CMakeFiles/worklets.dir/8381e4f2241b07a247a8edec969abc0e/Common/cpp/worklets/Tools/JSLogger.cpp.o	4f04e9a98cdead1
15	5032	7702041306460564	src/main/cpp/worklets/CMakeFiles/worklets.dir/8381e4f2241b07a247a8edec969abc0e/Common/cpp/worklets/Tools/AsyncQueue.cpp.o	bc4e6e476828a203
19	5714	7702041313707767	src/main/cpp/worklets/CMakeFiles/worklets.dir/8381e4f2241b07a247a8edec969abc0e/Common/cpp/worklets/Tools/JSISerializer.cpp.o	a417fbc29b11c714
64	6229	7702041318579418	src/main/cpp/worklets/CMakeFiles/worklets.dir/8381e4f2241b07a247a8edec969abc0e/Common/cpp/worklets/Tools/JSScheduler.cpp.o	d0f3b06a7107d152
80	6373	7702041318738125	src/main/cpp/worklets/CMakeFiles/worklets.dir/c373fb0cb0205ac8156540e3132b2685/WorkletRuntime/ReanimatedHermesRuntime.cpp.o	1c41bc4ef6408912
11	8104	7702041337246634	src/main/cpp/worklets/CMakeFiles/worklets.dir/c373fb0cb0205ac8156540e3132b2685/Registries/EventHandlerRegistry.cpp.o	1105b6ce4930c738
7	8135	7702041336666137	src/main/cpp/worklets/CMakeFiles/worklets.dir/7519ac08560a20b167be15fe9fbea7e4/cpp/worklets/SharedItems/Shareables.cpp.o	cfa35cacb2f1ba2c
3911	8844	7702041342249822	src/main/cpp/worklets/CMakeFiles/worklets.dir/8381e4f2241b07a247a8edec969abc0e/Common/cpp/worklets/Tools/UIScheduler.cpp.o	ea0042ef0ba56e5b
3710	8931	7702041345334979	src/main/cpp/worklets/CMakeFiles/worklets.dir/7519ac08560a20b167be15fe9fbea7e4/cpp/worklets/Tools/WorkletEventHandler.cpp.o	b1291cea111f09e
4892	9736	7702041353480442	src/main/cpp/worklets/CMakeFiles/worklets.dir/c373fb0cb0205ac8156540e3132b2685/WorkletRuntime/ReanimatedRuntime.cpp.o	17a33855cacfdcf6
76	9801	7702041353929781	src/main/cpp/worklets/CMakeFiles/worklets.dir/7519ac08560a20b167be15fe9fbea7e4/cpp/worklets/Tools/ReanimatedVersion.cpp.o	1f44e99285e1e50e
5033	11733	7702041373183278	src/main/cpp/worklets/CMakeFiles/worklets.dir/5902132a011a07009ebc4c1979b47fca/worklets/WorkletRuntime/WorkletRuntime.cpp.o	a30128033d2bf5f4
6373	12269	7702041378677691	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/903cdc38e100b3e8f688e82b49cafa56/AnimatedSensor/AnimatedSensorModule.cpp.o	af83bed307f5d772
5715	15531	7702041410938527	src/main/cpp/worklets/CMakeFiles/worklets.dir/c373fb0cb0205ac8156540e3132b2685/WorkletRuntime/WorkletRuntimeDecorator.cpp.o	f2cb15389d8083d
15531	16655	7702041419382764	../../../../build/intermediates/cxx/Debug/252f2v2v/obj/arm64-v8a/libworklets.so	8cf608e246283a3b
8932	18403	7702041439680523	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/AndroidUIScheduler.cpp.o	fbec3835af59ec02
11734	19976	7702041454497369	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/5384bd35cebe0339a8534868eb1525ec/LayoutAnimationsManager.cpp.o	3a6162ef95b1285
6229	21522	7702041470642429	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/7519ac08560a20b167be15fe9fbea7e4/cpp/reanimated/Fabric/PropsRegistry.cpp.o	ed1ca000089d3aa4
8844	23878	7702041494503066	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/5384bd35cebe0339a8534868eb1525ec/LayoutAnimationsUtils.cpp.o	89878ade2bf1e307
8136	25099	7702041506502530	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/903cdc38e100b3e8f688e82b49cafa56/Fabric/ReanimatedCommitHook.cpp.o	63c6eb3074fd79ce
19976	25108	7702041505096348	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/de26f8cead47227ca676f03d1d571aaf/NativeReanimatedModuleSpec.cpp.o	6ad88f70a1e3a0c7
18404	25841	7702041514574454	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/0ece905a2c2bf93f9c78f535a359f32b/UIRuntimeDecorator.cpp.o	b07f2dfa1548b9f1
8104	25870	7702041514834208	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/903cdc38e100b3e8f688e82b49cafa56/Fabric/ReanimatedMountHook.cpp.o	ee504245dbdd45c
12269	26006	7702041515753234	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/5902132a011a07009ebc4c1979b47fca/reanimated/Fabric/ShadowTreeCloner.cpp.o	1545c9cd3ee7612c
23878	27449	7702041530543093	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/7519ac08560a20b167be15fe9fbea7e4/cpp/reanimated/Tools/FeaturesConfig.cpp.o	7f7728805d37d36
21523	27803	7702041533694533	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/0ece905a2c2bf93f9c78f535a359f32b/ReanimatedWorkletRuntimeDecorator.cpp.o	666bedb1f82839b3
16655	28874	7702041545172586	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/0ece905a2c2bf93f9c78f535a359f32b/RNRuntimeDecorator.cpp.o	e33e551be3d2d8e0
26006	29052	7702041547371436	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/PlatformLogger.cpp.o	fc2d4a1c530964d
9801	32871	7702041583525180	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/5384bd35cebe0339a8534868eb1525ec/LayoutAnimationsProxy.cpp.o	8a27f6547dd0cba2
25108	33164	7702041587727333	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o	1c4c6583c939b5b2
25100	33812	7702041595061656	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o	26714dd33d2854bf
25871	37837	7702041635142221	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o	eb1af24f39f926dd
9736	38196	7702041638278310	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/de26f8cead47227ca676f03d1d571aaf/NativeReanimatedModule.cpp.o	3207e88e71e2aad7
25841	43191	7702041688470801	src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o	5aac22245657cab8
43192	43505	7702041691708076	../../../../build/intermediates/cxx/Debug/252f2v2v/obj/arm64-v8a/libreanimated.so	8596f1ff3473ce5a
