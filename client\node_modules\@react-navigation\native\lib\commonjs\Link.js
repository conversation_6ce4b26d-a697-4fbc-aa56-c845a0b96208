"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.Link = Link;
var _core = require("@react-navigation/core");
var React = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _useLinkProps = require("./useLinkProps.js");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/**
 * Component to render link to another screen using a path.
 * Uses an anchor tag on the web.
 *
 * @param props.screen Name of the screen to navigate to (e.g. `'Feeds'`).
 * @param props.params Params to pass to the screen to navigate to (e.g. `{ sort: 'hot' }`).
 * @param props.href Optional absolute path to use for the href (e.g. `/feeds/hot`).
 * @param props.action Optional action to use for in-page navigation. By default, the path is parsed to an action based on linking config.
 * @param props.children Child elements to render the content.
 */
function Link({
  screen,
  params,
  action,
  href,
  style,
  ...rest
}) {
  const {
    colors,
    fonts
  } = (0, _core.useTheme)();
  // @ts-expect-error: This is already type-checked by the prop types
  const props = (0, _useLinkProps.useLinkProps)({
    screen,
    params,
    action,
    href
  });
  const onPress = e => {
    if ('onPress' in rest) {
      rest.onPress?.(e);
    }

    // Let user prevent default behavior
    if (!e.defaultPrevented) {
      props.onPress(e);
    }
  };
  return /*#__PURE__*/React.createElement(_reactNative.Text, {
    ...props,
    ...rest,
    ..._reactNative.Platform.select({
      web: {
        onClick: onPress
      },
      default: {
        onPress
      }
    }),
    style: [{
      color: colors.primary
    }, fonts.regular, style]
  });
}
//# sourceMappingURL=Link.js.map