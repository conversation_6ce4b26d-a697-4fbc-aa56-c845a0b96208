// This is a simple API fallback for Vercel deployment
// It will be used when the actual backend is not available

export default function handler(req, res) {
  // Get the requested endpoint
  const path = req.query.path || [];
  const endpoint = '/' + path.join('/');

  // Parse query parameters
  const url = new URL(req.url, `https://${req.headers.host}`);
  const page = parseInt(url.searchParams.get('page') || '0');
  const limit = parseInt(url.searchParams.get('limit') || '10');

  // Handle login requests
  if (endpoint === '/auth/login') {
    return res.status(200).json({
      success: true,
      token: 'mock-token-12345',
      user: {
        id: '1',
        name: 'Admin User',
        email: '<EMAIL>',
        userType: 'admin'
      }
    });
  }

  // Handle get current user requests
  if (endpoint === '/auth/me') {
    return res.status(200).json({
      success: true,
      user: {
        id: '1',
        name: 'Admin User',
        email: '<EMAIL>',
        userType: 'admin'
      }
    });
  }

  // Handle vendors endpoint
  if (endpoint === '/vendors') {
    console.log('API fallback: Handling /vendors endpoint');
    console.log('Query parameters:', { page, limit });

    const statuses = ["active", "inactive", "pending"];
    const vendors = Array.from({ length: limit }, (_, i) => ({
      id: `VEND-${page * limit + i + 1}`,
      name: `Vendor ${page * limit + i + 1}`,
      email: `vendor${page * limit + i + 1}@example.com`,
      phone: `+1 ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`,
      address: `${Math.floor(Math.random() * 9000) + 1000} Main St, City ${i + 1}`,
      status: statuses[Math.floor(Math.random() * statuses.length)],
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)).toISOString(),
      permissions: []
    }));

    const response = {
      success: true,
      vendors,
      totalPages: 10,
    };

    console.log('API fallback: Sending vendors response');
    return res.status(200).json(response);
  }

  // Handle customers endpoint
  if (endpoint === '/customers') {
    console.log('API fallback: Handling /customers endpoint');
    console.log('Query parameters:', { page, limit });

    const customers = Array.from({ length: limit }, (_, i) => ({
      id: `CUST-${page * limit + i + 1}`,
      name: `Customer ${page * limit + i + 1}`,
      email: `customer${page * limit + i + 1}@example.com`,
      totalOrders: Math.floor(Math.random() * 20) + 1,
      totalSpent: Math.floor(Math.random() * 10000) + 500,
      lastOrder: new Date(Date.now() - Math.floor(Math.random() * 10000000000)).toISOString(),
      status: "active",
      orders: [],
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)).toISOString(),
    }));

    const response = {
      success: true,
      customers,
      totalPages: 10,
    };

    console.log('API fallback: Sending customers response');
    return res.status(200).json(response);
  }

  // Handle categories endpoint
  if (endpoint === '/categories') {
    console.log('API fallback: Handling /categories endpoint');
    console.log('Query parameters:', { page, limit });

    const categories = Array.from({ length: limit }, (_, i) => ({
      id: `CAT-${page * limit + i + 1}`,
      name: `Category ${page * limit + i + 1}`,
      description: `Description for category ${page * limit + i + 1}`,
      status: Math.random() > 0.2 ? "active" : "inactive",
      imageUrl: Math.random() > 0.5 ? `https://picsum.photos/seed/cat${i}/200/200` : null,
      productCount: Math.floor(Math.random() * 50) + 1,
      vendorId: Math.random() > 0.5 ? `VEND-${Math.floor(Math.random() * 10) + 1}` : null,
      vendorName: Math.random() > 0.5 ? `Vendor ${Math.floor(Math.random() * 10) + 1}` : null,
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)).toISOString(),
    }));

    const response = {
      success: true,
      categories,
      totalPages: 5,
      currentPage: page,
      totalCategories: 50,
    };

    console.log('API fallback: Sending categories response');
    return res.status(200).json(response);
  }

  // Handle delivery partners endpoint
  if (endpoint === '/delivery-partners') {
    console.log('API fallback: Handling /delivery-partners endpoint');
    console.log('Query parameters:', { page, limit });

    const areas = ["North", "South", "East", "West", "Central"];
    const vehicleTypes = ["2-wheeler", "4-wheeler"];
    const deliveryPartners = Array.from({ length: limit }, (_, i) => ({
      id: `DEL-${page * limit + i + 1}`,
      name: `Delivery Partner ${page * limit + i + 1}`,
      email: `partner${page * limit + i + 1}@example.com`,
      phone: `+1 ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`,
      address: `${Math.floor(Math.random() * 9000) + 1000} Main St, City ${i + 1}`,
      governmentId: `GOV-ID-${Math.floor(Math.random() * 1000000)}`,
      drivingLicense: `DL-${Math.floor(Math.random() * 1000000)}`,
      vehicleNumber: `VN-${Math.floor(Math.random() * 10000)}`,
      vehicleType: vehicleTypes[Math.floor(Math.random() * vehicleTypes.length)],
      area: areas[Math.floor(Math.random() * areas.length)],
      status: Math.random() > 0.2 ? "active" : "inactive",
      photoUrl: Math.random() > 0.5 ? `https://randomuser.me/api/portraits/${Math.random() > 0.5 ? 'men' : 'women'}/${i + 1}.jpg` : null,
      deliveryCount: Math.floor(Math.random() * 500) + 10,
      branchId: Math.random() > 0.7 ? `BR-${Math.floor(Math.random() * 10) + 1}` : null,
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)).toISOString(),
    }));

    const response = {
      success: true,
      deliveryPartners,
      totalPages: 5,
      currentPage: page,
      totalDeliveryPartners: 50,
    };

    console.log('API fallback: Sending delivery partners response');
    return res.status(200).json(response);
  }

  // Handle branches endpoint
  if (endpoint === '/branches') {
    console.log('API fallback: Handling /branches endpoint');
    console.log('Query parameters:', { page, limit });

    const branches = Array.from({ length: limit }, (_, i) => ({
      id: `BR-${page * limit + i + 1}`,
      name: `Branch ${page * limit + i + 1}`,
      description: `Description for branch ${page * limit + i + 1}`,
      address: `${Math.floor(Math.random() * 9000) + 1000} Main St, City ${i + 1}`,
      city: `City ${Math.floor(Math.random() * 20) + 1}`,
      state: `State ${Math.floor(Math.random() * 10) + 1}`,
      pincode: `${Math.floor(Math.random() * 900000) + 100000}`,
      phone: `+1 ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`,
      location: {
        lat: 20.5937 + (Math.random() * 8 - 4),
        lng: 78.9629 + (Math.random() * 8 - 4)
      },
      status: Math.random() > 0.2 ? "active" : "inactive",
      vendorId: `VEND-${Math.floor(Math.random() * 10) + 1}`,
      deliveryPartners: Array.from({ length: Math.floor(Math.random() * 5) }, (_, j) => `DEL-${Math.floor(Math.random() * 50) + 1}`),
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)).toISOString(),
    }));

    const response = {
      success: true,
      branches,
      totalPages: 3,
      currentPage: page,
      totalBranches: 30,
    };

    console.log('API fallback: Sending branches response');
    return res.status(200).json(response);
  }

  // Handle coupons endpoint
  if (endpoint === '/coupons') {
    console.log('API fallback: Handling /coupons endpoint');
    console.log('Query parameters:', { page, limit });

    const coupons = Array.from({ length: limit }, (_, i) => ({
      id: `CPN-${page * limit + i + 1}`,
      code: `COUPON${page * limit + i + 1}`,
      description: `Discount coupon ${i + 1}`,
      discountType: Math.random() > 0.5 ? "percentage" : "fixed",
      discountValue: Math.random() > 0.5 ? Math.floor(Math.random() * 50) + 5 : Math.floor(Math.random() * 100) + 10,
      minimumPurchase: Math.floor(Math.random() * 500) + 100,
      maximumDiscount: Math.random() > 0.5 ? Math.floor(Math.random() * 200) + 50 : null,
      startDate: new Date(Date.now() - Math.floor(Math.random() * 10000000000)).toISOString(),
      expiryDate: new Date(Date.now() + Math.floor(Math.random() * 10000000000)).toISOString(),
      usageLimit: Math.random() > 0.5 ? Math.floor(Math.random() * 100) + 10 : null,
      usedCount: Math.floor(Math.random() * 50),
      isActive: Math.random() > 0.2,
      couponType: ["all", "product", "category", "vendor"][Math.floor(Math.random() * 4)],
      applicableProducts: [],
      applicableCategories: [],
      applicableVendors: [],
      createdBy: {
        id: '1',
        name: 'Admin User',
        email: '<EMAIL>'
      },
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)).toISOString(),
    }));

    const response = {
      success: true,
      coupons,
      totalPages: 5,
      currentPage: page,
      totalCoupons: 50
    };

    console.log('API fallback: Sending coupons response');
    return res.status(200).json(response);
  }

  // Handle coupon validation endpoint
  if (endpoint === '/coupons/validate') {
    console.log('API fallback: Handling /coupons/validate endpoint');

    const { code, amount } = req.body;

    if (!code || !amount) {
      return res.status(400).json({
        success: false,
        message: 'Coupon code and order amount are required'
      });
    }

    const discountType = Math.random() > 0.5 ? "percentage" : "fixed";
    const discountValue = discountType === "percentage" ?
      Math.floor(Math.random() * 50) + 5 :
      Math.floor(Math.random() * 100) + 10;

    let discount = 0;
    if (discountType === "percentage") {
      discount = (amount * discountValue) / 100;
      // Apply maximum discount if set
      const maximumDiscount = Math.floor(Math.random() * 200) + 50;
      if (discount > maximumDiscount) {
        discount = maximumDiscount;
      }
    } else {
      // Fixed discount
      discount = discountValue;
      // Ensure discount doesn't exceed order amount
      if (discount > amount) {
        discount = amount;
      }
    }

    // Round discount to 2 decimal places
    discount = Math.round(discount * 100) / 100;

    const response = {
      success: true,
      coupon: {
        id: `CPN-${Math.floor(Math.random() * 10000)}`,
        code: code.toUpperCase(),
        description: `Discount coupon for ${code}`,
        discountType,
        discountValue,
        discount,
        finalAmount: amount - discount,
        couponType: "all",
        applicableProducts: [],
        applicableCategories: [],
        applicableVendors: []
      }
    };

    console.log('API fallback: Sending coupon validation response');
    return res.status(200).json(response);
  }

  // Default response for unhandled endpoints
  console.log('API fallback: Unhandled endpoint', endpoint);
  return res.status(200).json({
    success: true,
    message: 'API fallback response',
    endpoint
  });
}
