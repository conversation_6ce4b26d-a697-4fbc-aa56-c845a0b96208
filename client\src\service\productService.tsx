import { appAxios } from "./apiInterceptors"

export const getAllCategories = async () => {
    try {
        console.log('Fetching categories from admin server...');
        const response = await appAxios.get(`/client/categories`)
        console.log('Categories response:', response.data);

        if (response.data && response.data.success && response.data.categories) {
            return response.data.categories;
        }
        return [];
    } catch (error) {
        console.log("Error fetching categories:", error)
        return []
    }
}

export const getProductsByCategoryId = async (id: string) => {
    try {
        console.log('Fetching products for category:', id);
        const response = await appAxios.get(`/client/products/${id}`)
        console.log('Products response:', response.data);

        if (response.data && response.data.success && response.data.products) {
            return response.data.products;
        }
        return [];
    } catch (error) {
        console.log("Error fetching products:", error)
        return []
    }
}

