"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.copyTemplate = copyTemplate;
exports.executePostInitScript = executePostInitScript;
exports.getTemplateConfig = getTemplateConfig;
exports.installTemplatePackage = installTemplatePackage;
function _execa() {
  const data = _interopRequireDefault(require("execa"));
  _execa = function () {
    return data;
  };
  return data;
}
function _path() {
  const data = _interopRequireDefault(require("path"));
  _path = function () {
    return data;
  };
  return data;
}
function _cliTools() {
  const data = require("@react-native-community/cli-tools");
  _cliTools = function () {
    return data;
  };
  return data;
}
var PackageManager = _interopRequireWildcard(require("../../tools/packageManager"));
var _copyFiles = _interopRequireDefault(require("../../tools/copyFiles"));
var _replacePathSepForRegex = _interopRequireDefault(require("../../tools/replacePathSepForRegex"));
function _fs() {
  const data = _interopRequireDefault(require("fs"));
  _fs = function () {
    return data;
  };
  return data;
}
function _chalk() {
  const data = _interopRequireDefault(require("chalk"));
  _chalk = function () {
    return data;
  };
  return data;
}
var _yarn = require("../../tools/yarn");
var _executeCommand = require("../../tools/executeCommand");
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
async function installTemplatePackage(templateName, root, packageManager, yarnConfigOptions) {
  _cliTools().logger.debug(`Installing template from ${templateName}`);
  await PackageManager.init({
    packageManager,
    silent: true,
    root
  });
  if (packageManager === 'yarn' && (0, _yarn.getYarnVersionIfAvailable)() !== null) {
    const options = {
      root,
      silent: true
    };

    // React Native doesn't support PnP, so we need to set nodeLinker to node-modules. Read more here: https://github.com/react-native-community/cli/issues/27#issuecomment-1772626767
    (0, _executeCommand.executeCommand)('yarn', ['config', 'set', 'nodeLinker', 'node-modules'], options);
    (0, _executeCommand.executeCommand)('yarn', ['config', 'set', 'nmHoistingLimits', 'workspaces'], options);
    for (let key in yarnConfigOptions) {
      if (yarnConfigOptions.hasOwnProperty(key)) {
        let value = yarnConfigOptions[key];
        (0, _executeCommand.executeCommand)('yarn', ['config', 'set', key, value], options);
      }
    }
  }
  return PackageManager.install([templateName], {
    packageManager,
    silent: true,
    root
  });
}
function getTemplateConfig(templateName, templateSourceDir) {
  const configFilePath = _path().default.resolve(templateSourceDir, 'node_modules', templateName, 'template.config.js');
  _cliTools().logger.debug(`Getting config from ${configFilePath}`);
  if (!_fs().default.existsSync(configFilePath)) {
    throw new (_cliTools().CLIError)(`Couldn't find the "${configFilePath} file inside "${templateName}" template. Please make sure the template is valid.
      Read more: ${_chalk().default.underline.dim('https://github.com/react-native-community/cli/blob/main/docs/init.md#creating-custom-template')}`);
  }
  return require(configFilePath);
}
async function copyTemplate(templateName, templateDir, templateSourceDir) {
  const templatePath = _path().default.resolve(templateSourceDir, 'node_modules', templateName, templateDir);
  _cliTools().logger.debug(`Copying template from ${templatePath}`);
  let regexStr = _path().default.resolve(templatePath, 'node_modules');
  await (0, _copyFiles.default)(templatePath, process.cwd(), {
    exclude: [new RegExp((0, _replacePathSepForRegex.default)(regexStr))]
  });
}
function executePostInitScript(templateName, postInitScript, templateSourceDir) {
  const scriptPath = _path().default.resolve(templateSourceDir, 'node_modules', templateName, postInitScript);
  _cliTools().logger.debug(`Executing post init script located ${scriptPath}`);
  return (0, _execa().default)(scriptPath, {
    stdio: 'inherit'
  });
}

//# sourceMappingURL=/Users/<USER>/Developer/oss/rncli/packages/cli/build/commands/init/template.js.map