<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":react-native-vector-icons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-vector-icons\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-svg" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-svg\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-reanimated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-reanimated\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-mmkv" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-mmkv\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-maps" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-maps\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-linear-gradient" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-linear-gradient\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-community_geolocation" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\@react-native-community\geolocation\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-screens\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\react-native-gesture-handler\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":lottie-react-native" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\client\node_modules\lottie-react-native\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\assets"><file name="fonts/Okra-Bold.ttf" path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\assets\fonts\Okra-Bold.ttf"/><file name="fonts/Okra-ExtraBold.ttf" path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\assets\fonts\Okra-ExtraBold.ttf"/><file name="fonts/Okra-Medium.ttf" path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\assets\fonts\Okra-Medium.ttf"/><file name="fonts/Okra-MediumLight.ttf" path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\assets\fonts\Okra-MediumLight.ttf"/><file name="fonts/Okra-Regular.ttf" path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\main\assets\fonts\Okra-Regular.ttf"/></source><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\ReactNativeVectorIcons"><file name="fonts/Ionicons.ttf" path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\ReactNativeVectorIcons\fonts\Ionicons.ttf"/><file name="fonts/MaterialCommunityIcons.ttf" path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\ReactNativeVectorIcons\fonts\MaterialCommunityIcons.ttf"/><file name="fonts/MaterialIcons.ttf" path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\ReactNativeVectorIcons\fonts\MaterialIcons.ttf"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>