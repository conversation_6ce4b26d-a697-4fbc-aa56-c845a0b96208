"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.useHeaderConfigProps = useHeaderConfigProps;
var _elements = require("@react-navigation/elements");
var _native = require("@react-navigation/native");
var _reactNative = require("react-native");
var _reactNativeScreens = require("react-native-screens");
var _FontProcessor = require("./FontProcessor");
var _jsxRuntime = require("react/jsx-runtime");
function useHeaderConfigProps({
  headerBackImageSource,
  headerBackButtonDisplayMode,
  headerBackButtonMenuEnabled,
  headerBackTitle,
  headerBackTitleStyle,
  headerBackVisible,
  headerShadowVisible,
  headerLargeStyle,
  headerLargeTitle,
  headerLargeTitleShadowVisible,
  headerLargeTitleStyle,
  headerBackground,
  headerLeft,
  headerRight,
  headerShown,
  headerStyle,
  headerBlurEffect,
  headerTintColor,
  headerTitle,
  headerTitleAlign,
  headerTitleStyle,
  headerTransparent,
  headerSearchBarOptions,
  headerTopInsetEnabled,
  headerBack,
  route,
  title
}) {
  const {
    direction
  } = (0, _native.useLocale)();
  const {
    colors,
    fonts
  } = (0, _native.useTheme)();
  const tintColor = headerTintColor ?? (_reactNative.Platform.OS === 'ios' ? colors.primary : colors.text);
  const headerBackTitleStyleFlattened = _reactNative.StyleSheet.flatten([fonts.regular, headerBackTitleStyle]) || {};
  const headerLargeTitleStyleFlattened = _reactNative.StyleSheet.flatten([_reactNative.Platform.select({
    ios: fonts.heavy,
    default: fonts.medium
  }), headerLargeTitleStyle]) || {};
  const headerTitleStyleFlattened = _reactNative.StyleSheet.flatten([_reactNative.Platform.select({
    ios: fonts.bold,
    default: fonts.medium
  }), headerTitleStyle]) || {};
  const headerStyleFlattened = _reactNative.StyleSheet.flatten(headerStyle) || {};
  const headerLargeStyleFlattened = _reactNative.StyleSheet.flatten(headerLargeStyle) || {};
  const [backTitleFontFamily, largeTitleFontFamily, titleFontFamily] = (0, _FontProcessor.processFonts)([headerBackTitleStyleFlattened.fontFamily, headerLargeTitleStyleFlattened.fontFamily, headerTitleStyleFlattened.fontFamily]);
  const backTitleFontSize = 'fontSize' in headerBackTitleStyleFlattened ? headerBackTitleStyleFlattened.fontSize : undefined;
  const titleText = (0, _elements.getHeaderTitle)({
    title,
    headerTitle
  }, route.name);
  const titleColor = 'color' in headerTitleStyleFlattened ? headerTitleStyleFlattened.color : headerTintColor ?? colors.text;
  const titleFontSize = 'fontSize' in headerTitleStyleFlattened ? headerTitleStyleFlattened.fontSize : undefined;
  const titleFontWeight = headerTitleStyleFlattened.fontWeight;
  const largeTitleBackgroundColor = headerLargeStyleFlattened.backgroundColor;
  const largeTitleColor = 'color' in headerLargeTitleStyleFlattened ? headerLargeTitleStyleFlattened.color : undefined;
  const largeTitleFontSize = 'fontSize' in headerLargeTitleStyleFlattened ? headerLargeTitleStyleFlattened.fontSize : undefined;
  const largeTitleFontWeight = headerLargeTitleStyleFlattened.fontWeight;
  const headerTitleStyleSupported = {
    color: titleColor
  };
  if (headerTitleStyleFlattened.fontFamily != null) {
    headerTitleStyleSupported.fontFamily = headerTitleStyleFlattened.fontFamily;
  }
  if (titleFontSize != null) {
    headerTitleStyleSupported.fontSize = titleFontSize;
  }
  if (titleFontWeight != null) {
    headerTitleStyleSupported.fontWeight = titleFontWeight;
  }
  const headerBackgroundColor = headerStyleFlattened.backgroundColor ?? (headerBackground != null || headerTransparent ? 'transparent' : colors.card);
  const canGoBack = headerBack != null;
  const headerLeftElement = headerLeft?.({
    tintColor,
    canGoBack,
    label: headerBackTitle ?? headerBack?.title,
    // `href` is only applicable to web
    href: undefined
  });
  const headerRightElement = headerRight?.({
    tintColor,
    canGoBack
  });
  const headerTitleElement = typeof headerTitle === 'function' ? headerTitle({
    tintColor,
    children: titleText
  }) : null;
  const supportsHeaderSearchBar = typeof _reactNativeScreens.isSearchBarAvailableForCurrentPlatform === 'boolean' ? _reactNativeScreens.isSearchBarAvailableForCurrentPlatform :
  // Fallback for older versions of react-native-screens
  _reactNative.Platform.OS === 'ios' && _reactNativeScreens.SearchBar != null;
  const hasHeaderSearchBar = supportsHeaderSearchBar && headerSearchBarOptions != null;
  if (headerSearchBarOptions != null && !supportsHeaderSearchBar) {
    throw new Error(`The current version of 'react-native-screens' doesn't support SearchBar in the header. Please update to the latest version to use this option.`);
  }

  /**
   * We need to set this in if:
   * - Back button should stay visible when `headerLeft` is specified
   * - If `headerTitle` for Android is specified, so we only need to remove the title and keep the back button
   */
  const backButtonInCustomView = headerBackVisible || _reactNative.Platform.OS === 'android' && headerTitleElement != null && headerLeftElement == null;
  const translucent = headerBackground != null || headerTransparent ||
  // When using a SearchBar or large title, the header needs to be translucent for it to work on iOS
  (hasHeaderSearchBar || headerLargeTitle) && _reactNative.Platform.OS === 'ios' && headerTransparent !== false;
  const isBackButtonDisplayModeAvailable =
  // On iOS 14+
  _reactNative.Platform.OS === 'ios' && parseInt(_reactNative.Platform.Version, 10) >= 14 &&
  // Doesn't have custom back title
  headerBackTitle == null &&
  // Doesn't have custom styling
  backTitleFontFamily == null && backTitleFontSize == null &&
  // Back button menu is not disabled
  headerBackButtonMenuEnabled !== false;
  const isCenterViewRenderedAndroid = headerTitleAlign === 'center';
  const children = /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
    children: [_reactNative.Platform.OS === 'ios' ? /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [headerLeftElement != null ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeScreens.ScreenStackHeaderLeftView, {
        children: headerLeftElement
      }) : null, headerTitleElement != null ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeScreens.ScreenStackHeaderCenterView, {
        children: headerTitleElement
      }) : null]
    }) : /*#__PURE__*/(0, _jsxRuntime.jsxs)(_jsxRuntime.Fragment, {
      children: [headerLeftElement != null || typeof headerTitle === 'function' ?
      /*#__PURE__*/
      // The style passed to header left, together with title element being wrapped
      // in flex view is reqruied for proper header layout, in particular,
      // for the text truncation to work.
      (0, _jsxRuntime.jsxs)(_reactNativeScreens.ScreenStackHeaderLeftView, {
        style: !isCenterViewRenderedAndroid ? {
          flex: 1
        } : null,
        children: [headerLeftElement, headerTitleAlign !== 'center' ? typeof headerTitle === 'function' ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: {
            flex: 1
          },
          children: headerTitleElement
        }) : /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
          style: {
            flex: 1
          },
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.HeaderTitle, {
            tintColor: tintColor,
            style: headerTitleStyleSupported,
            children: titleText
          })
        }) : null]
      }) : null, isCenterViewRenderedAndroid ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeScreens.ScreenStackHeaderCenterView, {
        children: typeof headerTitle === 'function' ? headerTitleElement : /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.HeaderTitle, {
          tintColor: tintColor,
          style: headerTitleStyleSupported,
          children: titleText
        })
      }) : null]
    }), headerBackImageSource !== undefined ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeScreens.ScreenStackHeaderBackButtonImage, {
      source: headerBackImageSource
    }) : null, headerRightElement != null ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeScreens.ScreenStackHeaderRightView, {
      children: headerRightElement
    }) : null, hasHeaderSearchBar ? /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeScreens.ScreenStackHeaderSearchBarView, {
      children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNativeScreens.SearchBar, {
        ...headerSearchBarOptions
      })
    }) : null]
  });
  return {
    backButtonInCustomView,
    backgroundColor: headerBackgroundColor,
    backTitle: headerBackTitle,
    backTitleVisible: isBackButtonDisplayModeAvailable ? undefined : headerBackButtonDisplayMode !== 'minimal',
    backButtonDisplayMode: isBackButtonDisplayModeAvailable ? headerBackButtonDisplayMode : undefined,
    backTitleFontFamily,
    backTitleFontSize,
    blurEffect: headerBlurEffect,
    color: tintColor,
    direction,
    disableBackButtonMenu: headerBackButtonMenuEnabled === false,
    hidden: headerShown === false,
    hideBackButton: headerBackVisible === false,
    hideShadow: headerShadowVisible === false || headerBackground != null || headerTransparent && headerShadowVisible !== true,
    largeTitle: headerLargeTitle,
    largeTitleBackgroundColor,
    largeTitleColor,
    largeTitleFontFamily,
    largeTitleFontSize,
    largeTitleFontWeight,
    largeTitleHideShadow: headerLargeTitleShadowVisible === false,
    title: titleText,
    titleColor,
    titleFontFamily,
    titleFontSize,
    titleFontWeight: String(titleFontWeight),
    topInsetEnabled: headerTopInsetEnabled,
    translucent: translucent === true,
    children
  };
}
//# sourceMappingURL=useHeaderConfigProps.js.map