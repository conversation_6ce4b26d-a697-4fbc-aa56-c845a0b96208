const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Admin Server for Client App Integration...');
console.log('📁 Working directory:', process.cwd());

// Change to Admin/server directory
const serverPath = path.join(__dirname, 'Admin', 'server');
console.log('📂 Server path:', serverPath);

// Start the server using ts-node
const serverProcess = spawn('npx', ['ts-node', 'index.ts'], {
  cwd: serverPath,
  stdio: 'inherit',
  shell: true
});

serverProcess.on('error', (error) => {
  console.error('❌ Failed to start server:', error);
});

serverProcess.on('close', (code) => {
  console.log(`🔴 Server process exited with code ${code}`);
});

console.log('✅ Server startup initiated...');
console.log('🌐 Server should be available at: http://localhost:3000');
console.log('📱 Client app can now connect to the admin backend');
