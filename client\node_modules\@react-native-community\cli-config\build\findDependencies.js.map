{"version": 3, "names": ["findDependencies", "root", "pjson", "content", "fs", "readFileSync", "path", "join", "JSON", "parse", "e", "deps", "Set", "Object", "keys", "dependencies", "peerDependencies", "devDependencies", "Array", "from"], "sources": ["../src/findDependencies.ts"], "sourcesContent": ["import path from 'path';\nimport fs from 'fs';\n\n/**\n * Returns an array of dependencies from project's package.json\n */\nexport default function findDependencies(root: string): Array<string> {\n  let pjson;\n\n  try {\n    const content = fs.readFileSync(path.join(root, 'package.json'), 'utf8');\n    pjson = JSON.parse(content);\n  } catch (e) {\n    return [];\n  }\n\n  const deps = new Set([\n    ...Object.keys(pjson.dependencies || {}),\n    ...Object.keys(pjson.peerDependencies || {}),\n    ...Object.keys(pjson.devDependencies || {}),\n  ]);\n\n  return Array.from(deps);\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAoB;AAEpB;AACA;AACA;AACe,SAASA,gBAAgB,CAACC,IAAY,EAAiB;EACpE,IAAIC,KAAK;EAET,IAAI;IACF,MAAMC,OAAO,GAAGC,aAAE,CAACC,YAAY,CAACC,eAAI,CAACC,IAAI,CAACN,IAAI,EAAE,cAAc,CAAC,EAAE,MAAM,CAAC;IACxEC,KAAK,GAAGM,IAAI,CAACC,KAAK,CAACN,OAAO,CAAC;EAC7B,CAAC,CAAC,OAAOO,CAAC,EAAE;IACV,OAAO,EAAE;EACX;EAEA,MAAMC,IAAI,GAAG,IAAIC,GAAG,CAAC,CACnB,GAAGC,MAAM,CAACC,IAAI,CAACZ,KAAK,CAACa,YAAY,IAAI,CAAC,CAAC,CAAC,EACxC,GAAGF,MAAM,CAACC,IAAI,CAACZ,KAAK,CAACc,gBAAgB,IAAI,CAAC,CAAC,CAAC,EAC5C,GAAGH,MAAM,CAACC,IAAI,CAACZ,KAAK,CAACe,eAAe,IAAI,CAAC,CAAC,CAAC,CAC5C,CAAC;EAEF,OAAOC,KAAK,CAACC,IAAI,CAACR,IAAI,CAAC;AACzB"}