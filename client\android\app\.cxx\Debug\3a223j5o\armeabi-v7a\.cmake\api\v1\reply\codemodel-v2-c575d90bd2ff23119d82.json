{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 7, 8, 9, 10, 11], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "RNCGeolocationSpec_autolinked_build", "jsonFile": "directory-RNCGeolocationSpec_autolinked_build-Debug-c6340eec4c0e48eb2e75.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "lottiereactnative_autolinked_build", "jsonFile": "directory-lottiereactnative_autolinked_build-Debug-aca4c3e0b87b98d8be7c.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni", "targetIndexes": [6]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-4958aac8d05dc61afe3d.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [7]}, {"build": "RNMmkvSpec_autolinked_build", "jsonFile": "directory-RNMmkvSpec_autolinked_build-Debug-61ab8d770ee3991134a2.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "RNMmkvSpec_cxxmodule_autolinked_build", "childIndexes": [6], "jsonFile": "directory-RNMmkvSpec_cxxmodule_autolinked_build-Debug-aab980eb312f31a79042.json", "minimumCMakeVersion": {"string": "3.9.0"}, "parentIndex": 0, "projectIndex": 1, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android", "targetIndexes": [2]}, {"build": "RNMmkvSpec_cxxmodule_autolinked_build/core", "jsonFile": "directory-RNMmkvSpec_cxxmodule_autolinked_build.core-Debug-2c0f84a7b02e837c5a67.json", "minimumCMakeVersion": {"string": "3.10.0"}, "parentIndex": 5, "projectIndex": 2, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core", "targetIndexes": [1]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-Debug-1abf843057c55d07906d.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [8]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-70bc1e87d2def13333aa.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [11]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-1ff6da03be16ef9dd239.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [9]}, {"build": "rnsvg_autolinked_build", "jsonFile": "directory-rnsvg_autolinked_build-Debug-ff9e867020bdb39f4d5a.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni", "targetIndexes": [10]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-00618633da6ae7a0b49e.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [5]}], "name": "Debug", "projects": [{"childIndexes": [1], "directoryIndexes": [0, 1, 2, 3, 4, 7, 8, 9, 10, 11], "name": "appmodules", "targetIndexes": [0, 3, 4, 5, 6, 7, 8, 9, 10, 11]}, {"childIndexes": [2], "directoryIndexes": [5], "name": "ReactNativeMmkv", "parentIndex": 0, "targetIndexes": [2]}, {"directoryIndexes": [6], "name": "core", "parentIndex": 1, "targetIndexes": [1]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-0d27f46c17ce0383cf88.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 6, "id": "core::@1b9a7d546b295b7d0867", "jsonFile": "target-core-Debug-d8d2699525e05b81a62c.json", "name": "core", "projectIndex": 2}, {"directoryIndex": 5, "id": "react-native-mmkv::@4ae6a1e65d3e68ba0197", "jsonFile": "target-react-native-mmkv-Debug-df0302e46677050d4f34.json", "name": "react-native-mmkv", "projectIndex": 1}, {"directoryIndex": 1, "id": "react_codegen_RNCGeolocationSpec::@1b959fcb56e23f7716ba", "jsonFile": "target-react_codegen_RNCGeolocationSpec-Debug-335f009d59fa0d93e2b7.json", "name": "react_codegen_RNCGeolocationSpec", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_RNMmkvSpec::@7541eabbae598da31a69", "jsonFile": "target-react_codegen_RNMmkvSpec-Debug-59924e598d8eb19254d7.json", "name": "react_codegen_RNMmkvSpec", "projectIndex": 0}, {"directoryIndex": 11, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-6b2f1331d132c0cd6f87.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_lottiereactnative::@0fa4dc904d7e359a99fb", "jsonFile": "target-react_codegen_lottiereactnative-Debug-820f9d8720107f798614.json", "name": "react_codegen_lottiereactnative", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-d7107a03e5718ccb31a4.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-Debug-84f2cfa523d17efbd763.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 9, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-fc808bb589317920e7bc.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 10, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65", "jsonFile": "target-react_codegen_rnsvg-Debug-28483db4dbc378adbd54.json", "name": "react_codegen_rnsvg", "projectIndex": 0}, {"directoryIndex": 8, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-4c07390af6149f4d12a0.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/armeabi-v7a", "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}