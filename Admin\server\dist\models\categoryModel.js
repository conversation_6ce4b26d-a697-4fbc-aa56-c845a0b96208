"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Category = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const categorySchema = new mongoose_1.default.Schema({
    name: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    status: {
        type: String,
        enum: ['active', 'inactive'],
        default: 'active'
    },
    imageUrl: {
        type: String
    },
    products: [{
            type: mongoose_1.default.Schema.Types.ObjectId,
            ref: 'Product'
        }],
    vendorId: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: 'Vendor'
    },
    createdAt: {
        type: Date,
        default: Date.now
    }
});
// Add indexes for faster lookups
categorySchema.index({ name: 1 });
categorySchema.index({ vendorId: 1 });
categorySchema.index({ status: 1 });
exports.Category = mongoose_1.default.model('Category', categorySchema);
exports.default = exports.Category;
