{"version": 3, "file": "spawnAsync.js", "sourceRoot": "", "sources": ["../src/spawnAsync.ts"], "names": [], "mappings": ";;;;AACA,8DAAgC;AAqBhC,SAAS,UAAU,CACjB,OAAe,EACf,IAA4B,EAC5B,UAAmC,EAAE;IAErC,MAAM,SAAS,GAAG,IAAI,KAAK,EAAE,CAAC;IAC9B,MAAM,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAEvF,IAAI,KAAmB,CAAC;IACxB,IAAI,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC5C,IAAI,EAAE,WAAW,EAAE,GAAG,WAAW,EAAE,GAAG,OAAO,CAAC;QAC9C,2FAA2F;QAC3F,KAAK,GAAG,IAAA,qBAAK,EAAC,OAAO,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;QAC1C,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,IAAI,CAAC,WAAW,EAAE;YAChB,IAAI,KAAK,CAAC,MAAM,EAAE;gBAChB,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBAC/B,MAAM,IAAI,IAAI,CAAC;gBACjB,CAAC,CAAC,CAAC;aACJ;YAED,IAAI,KAAK,CAAC,MAAM,EAAE;gBAChB,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBAC/B,MAAM,IAAI,IAAI,CAAC;gBACjB,CAAC,CAAC,CAAC;aACJ;SACF;QAED,IAAI,kBAAkB,GAAG,CAAC,IAAmB,EAAE,MAAqB,EAAE,EAAE;YACtE,KAAK,CAAC,cAAc,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YAC7C,IAAI,MAAM,GAA2B;gBACnC,GAAG,EAAE,KAAK,CAAC,GAAG;gBACd,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;gBACxB,MAAM;gBACN,MAAM;gBACN,MAAM,EAAE,IAAI;gBACZ,MAAM;aACP,CAAC;YACF,IAAI,IAAI,KAAK,CAAC,EAAE;gBACd,IAAI,cAAc,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACzE,IAAI,KAAK,GAAG,MAAM;oBAChB,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,OAAO,GAAG,cAAc,wBAAwB,MAAM,EAAE,CAAC;oBACxE,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,OAAO,GAAG,cAAc,+BAA+B,IAAI,EAAE,CAAC,CAAC;gBAChF,IAAI,KAAK,CAAC,KAAK,IAAI,WAAW,EAAE;oBAC9B,KAAK,CAAC,KAAK,IAAI,KAAK,WAAW,EAAE,CAAC;iBACnC;gBACD,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBAC7B,MAAM,CAAC,KAAK,CAAC,CAAC;aACf;iBAAM;gBACL,OAAO,CAAC,MAAM,CAAC,CAAC;aACjB;QACH,CAAC,CAAC;QAEF,IAAI,aAAa,GAAG,CAAC,KAAY,EAAE,EAAE;YACnC,IAAI,WAAW,EAAE;gBACf,KAAK,CAAC,cAAc,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;aAClD;iBAAM;gBACL,KAAK,CAAC,cAAc,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;aACnD;YACD,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE;gBACnB,GAAG,EAAE,KAAK,CAAC,GAAG;gBACd,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;gBACxB,MAAM;gBACN,MAAM;gBACN,MAAM,EAAE,IAAI;gBACZ,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;YACH,MAAM,CAAC,KAAK,CAAC,CAAC;QAChB,CAAC,CAAC;QAEF,IAAI,WAAW,EAAE;YACf,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;SACxC;aAAM;YACL,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;SACzC;QACD,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;IACrC,CAAC,CAAoD,CAAC;IACtD,6FAA6F;IAC7F,oCAAoC;IACpC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;IACtB,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,iBAAS,UAAU,CAAC", "sourcesContent": ["import { ChildProcess, SpawnOptions as NodeSpawnOptions } from 'child_process';\nimport spawn from 'cross-spawn';\n\nnamespace spawnAsync {\n  export interface SpawnOptions extends NodeSpawnOptions {\n    ignoreStdio?: boolean;\n  }\n\n  export interface SpawnPromise<T> extends Promise<T> {\n    child: ChildProcess;\n  }\n\n  export interface SpawnResult {\n    pid?: number;\n    output: string[];\n    stdout: string;\n    stderr: string;\n    status: number | null;\n    signal: string | null;\n  }\n}\n\nfunction spawnAsync(\n  command: string,\n  args?: ReadonlyArray<string>,\n  options: spawnAsync.SpawnOptions = {}\n): spawnAsync.SpawnPromise<spawnAsync.SpawnResult> {\n  const stubError = new Error();\n  const callerStack = stubError.stack ? stubError.stack.replace(/^.*/, '    ...') : null;\n\n  let child: ChildProcess;\n  let promise = new Promise((resolve, reject) => {\n    let { ignoreStdio, ...nodeOptions } = options;\n    // @ts-ignore: cross-spawn declares \"args\" to be a regular array instead of a read-only one\n    child = spawn(command, args, nodeOptions);\n    let stdout = '';\n    let stderr = '';\n\n    if (!ignoreStdio) {\n      if (child.stdout) {\n        child.stdout.on('data', (data) => {\n          stdout += data;\n        });\n      }\n\n      if (child.stderr) {\n        child.stderr.on('data', (data) => {\n          stderr += data;\n        });\n      }\n    }\n\n    let completionListener = (code: number | null, signal: string | null) => {\n      child.removeListener('error', errorListener);\n      let result: spawnAsync.SpawnResult = {\n        pid: child.pid,\n        output: [stdout, stderr],\n        stdout,\n        stderr,\n        status: code,\n        signal,\n      };\n      if (code !== 0) {\n        let argumentString = args && args.length > 0 ? ` ${args.join(' ')}` : '';\n        let error = signal\n          ? new Error(`${command}${argumentString} exited with signal: ${signal}`)\n          : new Error(`${command}${argumentString} exited with non-zero code: ${code}`);\n        if (error.stack && callerStack) {\n          error.stack += `\\n${callerStack}`;\n        }\n        Object.assign(error, result);\n        reject(error);\n      } else {\n        resolve(result);\n      }\n    };\n\n    let errorListener = (error: Error) => {\n      if (ignoreStdio) {\n        child.removeListener('exit', completionListener);\n      } else {\n        child.removeListener('close', completionListener);\n      }\n      Object.assign(error, {\n        pid: child.pid,\n        output: [stdout, stderr],\n        stdout,\n        stderr,\n        status: null,\n        signal: null,\n      });\n      reject(error);\n    };\n\n    if (ignoreStdio) {\n      child.once('exit', completionListener);\n    } else {\n      child.once('close', completionListener);\n    }\n    child.once('error', errorListener);\n  }) as spawnAsync.SpawnPromise<spawnAsync.SpawnResult>;\n  // @ts-ignore: TypeScript isn't aware the Promise constructor argument runs synchronously and\n  // thinks `child` is not yet defined\n  promise.child = child;\n  return promise;\n}\n\nexport = spawnAsync;\n"]}