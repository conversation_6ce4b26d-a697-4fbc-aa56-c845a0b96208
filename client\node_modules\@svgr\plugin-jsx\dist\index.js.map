{"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "sourcesContent": ["import { parse } from 'svg-parser'\nimport hastToBabelAst from '@svgr/hast-util-to-babel-ast'\nimport { transformFromAstSync, createConfigItem } from '@babel/core'\nimport svgrBabelPreset, {\n  Options as SvgrPresetOptions,\n} from '@svgr/babel-preset'\nimport type { Plugin, Config } from '@svgr/core'\n\nconst getJsxRuntimeOptions = (config: Config): Partial<SvgrPresetOptions> => {\n  if (config.jsxRuntimeImport) {\n    return {\n      importSource: config.jsxRuntimeImport.source,\n      jsxRuntimeImport: config.jsxRuntimeImport,\n    }\n  }\n  switch (config.jsxRuntime) {\n    case null:\n    case undefined:\n    case 'classic':\n      return {\n        jsxRuntime: 'classic',\n        importSource: 'react',\n        jsxRuntimeImport: { namespace: 'React', source: 'react' },\n      }\n    case 'classic-preact':\n      return {\n        jsxRuntime: 'classic',\n        importSource: 'preact/compat',\n        jsxRuntimeImport: { specifiers: ['h'], source: 'preact' },\n      }\n    case 'automatic':\n      return { jsxRuntime: 'automatic' }\n    default:\n      throw new Error(`Unsupported \"jsxRuntime\" \"${config.jsxRuntime}\"`)\n  }\n}\n\nconst jsxPlugin: Plugin = (code, config, state) => {\n  const filePath = state.filePath || 'unknown'\n  const hastTree = parse(code)\n\n  const babelTree = hastToBabelAst(hastTree)\n\n  const svgPresetOptions: SvgrPresetOptions = {\n    ref: config.ref,\n    titleProp: config.titleProp,\n    descProp: config.descProp,\n    expandProps: config.expandProps,\n    dimensions: config.dimensions,\n    icon: config.icon,\n    native: config.native,\n    svgProps: config.svgProps,\n    replaceAttrValues: config.replaceAttrValues,\n    typescript: config.typescript,\n    template: config.template,\n    memo: config.memo,\n    exportType: config.exportType,\n    namedExport: config.namedExport,\n    ...getJsxRuntimeOptions(config),\n    state,\n  }\n\n  const result = transformFromAstSync(babelTree, code, {\n    caller: {\n      name: 'svgr',\n    },\n    presets: [\n      createConfigItem([svgrBabelPreset, svgPresetOptions], {\n        type: 'preset',\n      }),\n    ],\n    filename: filePath,\n    babelrc: false,\n    configFile: false,\n    code: true,\n    ast: false,\n    // @ts-ignore\n    inputSourceMap: false,\n    ...(config.jsx && config.jsx.babelConfig),\n  })\n\n  if (!result?.code) {\n    throw new Error(`Unable to generate SVG file`)\n  }\n\n  return result.code\n}\n\nexport default jsxPlugin\n"], "names": ["parse", "transformFromAstSync", "createConfigItem"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,MAAM,oBAAA,GAAuB,CAAC,MAA+C,KAAA;AAC3E,EAAA,IAAI,OAAO,gBAAkB,EAAA;AAC3B,IAAO,OAAA;AAAA,MACL,YAAA,EAAc,OAAO,gBAAiB,CAAA,MAAA;AAAA,MACtC,kBAAkB,MAAO,CAAA,gBAAA;AAAA,KAC3B,CAAA;AAAA,GACF;AACA,EAAA,QAAQ,OAAO,UAAY;AAAA,IACzB,KAAK,IAAA,CAAA;AAAA,IACL,KAAK,KAAA,CAAA,CAAA;AAAA,IACL,KAAK,SAAA;AACH,MAAO,OAAA;AAAA,QACL,UAAY,EAAA,SAAA;AAAA,QACZ,YAAc,EAAA,OAAA;AAAA,QACd,gBAAkB,EAAA,EAAE,SAAW,EAAA,OAAA,EAAS,QAAQ,OAAQ,EAAA;AAAA,OAC1D,CAAA;AAAA,IACF,KAAK,gBAAA;AACH,MAAO,OAAA;AAAA,QACL,UAAY,EAAA,SAAA;AAAA,QACZ,YAAc,EAAA,eAAA;AAAA,QACd,kBAAkB,EAAE,UAAA,EAAY,CAAC,GAAG,CAAA,EAAG,QAAQ,QAAS,EAAA;AAAA,OAC1D,CAAA;AAAA,IACF,KAAK,WAAA;AACH,MAAO,OAAA,EAAE,YAAY,WAAY,EAAA,CAAA;AAAA,IACnC;AACE,MAAA,MAAM,IAAI,KAAA,CAAM,CAA6B,0BAAA,EAAA,MAAA,CAAO,UAAa,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,GACrE;AACF,CAAA,CAAA;AAEA,MAAM,SAAoB,GAAA,CAAC,IAAM,EAAA,MAAA,EAAQ,KAAU,KAAA;AACjD,EAAM,MAAA,QAAA,GAAW,MAAM,QAAY,IAAA,SAAA,CAAA;AACnC,EAAM,MAAA,QAAA,GAAWA,gBAAM,IAAI,CAAA,CAAA;AAE3B,EAAM,MAAA,SAAA,GAAY,eAAe,QAAQ,CAAA,CAAA;AAEzC,EAAA,MAAM,gBAAsC,GAAA,aAAA,CAAA,cAAA,CAAA;AAAA,IAC1C,KAAK,MAAO,CAAA,GAAA;AAAA,IACZ,WAAW,MAAO,CAAA,SAAA;AAAA,IAClB,UAAU,MAAO,CAAA,QAAA;AAAA,IACjB,aAAa,MAAO,CAAA,WAAA;AAAA,IACpB,YAAY,MAAO,CAAA,UAAA;AAAA,IACnB,MAAM,MAAO,CAAA,IAAA;AAAA,IACb,QAAQ,MAAO,CAAA,MAAA;AAAA,IACf,UAAU,MAAO,CAAA,QAAA;AAAA,IACjB,mBAAmB,MAAO,CAAA,iBAAA;AAAA,IAC1B,YAAY,MAAO,CAAA,UAAA;AAAA,IACnB,UAAU,MAAO,CAAA,QAAA;AAAA,IACjB,MAAM,MAAO,CAAA,IAAA;AAAA,IACb,YAAY,MAAO,CAAA,UAAA;AAAA,IACnB,aAAa,MAAO,CAAA,WAAA;AAAA,GACjB,EAAA,oBAAA,CAAqB,MAAM,CAfY,CAAA,EAAA;AAAA,IAgB1C,KAAA;AAAA,GACF,CAAA,CAAA;AAEA,EAAM,MAAA,MAAA,GAASC,yBAAqB,CAAA,SAAA,EAAW,IAAM,EAAA,cAAA,CAAA;AAAA,IACnD,MAAQ,EAAA;AAAA,MACN,IAAM,EAAA,MAAA;AAAA,KACR;AAAA,IACA,OAAS,EAAA;AAAA,MACPC,qBAAiB,CAAA,CAAC,eAAiB,EAAA,gBAAgB,CAAG,EAAA;AAAA,QACpD,IAAM,EAAA,QAAA;AAAA,OACP,CAAA;AAAA,KACH;AAAA,IACA,QAAU,EAAA,QAAA;AAAA,IACV,OAAS,EAAA,KAAA;AAAA,IACT,UAAY,EAAA,KAAA;AAAA,IACZ,IAAM,EAAA,IAAA;AAAA,IACN,GAAK,EAAA,KAAA;AAAA;AAAA,IAEL,cAAgB,EAAA,KAAA;AAAA,GAAA,EACZ,MAAO,CAAA,GAAA,IAAO,MAAO,CAAA,GAAA,CAAI,WAC9B,CAAA,CAAA,CAAA;AAED,EAAI,IAAA,EAAC,iCAAQ,IAAM,CAAA,EAAA;AACjB,IAAM,MAAA,IAAI,MAAM,CAA6B,2BAAA,CAAA,CAAA,CAAA;AAAA,GAC/C;AAEA,EAAA,OAAO,MAAO,CAAA,IAAA,CAAA;AAChB;;;;"}