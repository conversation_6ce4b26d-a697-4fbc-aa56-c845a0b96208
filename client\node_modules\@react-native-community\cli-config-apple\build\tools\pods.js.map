{"version": 3, "names": ["getPackageJson", "root", "require", "path", "join", "CLIError", "getPlatformDependencies", "dependencies", "platformName", "Object", "keys", "filter", "dependency", "platforms", "map", "version", "sort", "dependenciesToString", "generateMd5Hash", "text", "createHash", "update", "digest", "compareMd5Hashes", "hash1", "hash2", "get<PERSON><PERSON><PERSON><PERSON>", "podfileLockPath", "file", "fs", "readFileSync", "checksumLine", "split", "find", "line", "includes", "undefined", "install", "packageJson", "cachedDependenciesHash", "currentDependenciesHash", "iosFolderPath", "loader", "<PERSON><PERSON><PERSON><PERSON>", "installPods", "skipBundleInstall", "cacheManager", "set", "name", "succeed", "error", "fail", "chalk", "bold", "resolvePods", "sourceDir", "nativeDependencies", "options", "podfilePath", "platformFolderPath", "slice", "lastIndexOf", "podsPath", "arePodsInstalled", "existsSync", "platformDependencies", "dependenciesString", "currentPodfileHash", "currentPodfileLockChecksum", "cachedPodfileHash", "get", "cachedPodfileLockChecksum", "forceInstall", "newArchEnabled"], "sources": ["../../src/tools/pods.ts"], "sourcesContent": ["import path from 'path';\nimport fs from 'fs-extra';\nimport {createHash} from 'crypto';\nimport chalk from 'chalk';\nimport {\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  cache<PERSON>anager,\n  getLoader,\n} from '@react-native-community/cli-tools';\nimport installPods from './installPods';\nimport {\n  DependencyConfig,\n  IOSDependencyConfig,\n} from '@react-native-community/cli-types';\nimport {ApplePlatform} from '../types';\n\ninterface ResolvePodsOptions {\n  forceInstall?: boolean;\n  newArchEnabled?: boolean;\n}\n\ninterface NativeDependencies {\n  [key: string]: DependencyConfig;\n}\n\nfunction getPackageJson(root: string) {\n  try {\n    return require(path.join(root, 'package.json'));\n  } catch {\n    throw new CLIError(\n      'No package.json found. Please make sure the file exists in the current folder.',\n    );\n  }\n}\n\nexport function getPlatformDependencies(\n  dependencies: NativeDependencies,\n  platformName: ApplePlatform,\n) {\n  return Object.keys(dependencies)\n    .filter((dependency) => dependencies[dependency].platforms?.[platformName])\n    .map(\n      (dependency) =>\n        `${dependency}@${\n          (\n            dependencies[dependency].platforms?.[\n              platformName\n            ] as IOSDependencyConfig\n          ).version\n        }`,\n    )\n    .sort();\n}\n\nexport function dependenciesToString(dependencies: string[]) {\n  return dependencies.join('\\n');\n}\n\nexport function generateMd5Hash(text: string) {\n  return createHash('md5').update(text).digest('hex');\n}\n\nexport function compareMd5Hashes(hash1?: string, hash2?: string) {\n  return hash1 === hash2;\n}\n\nasync function getChecksum(\n  podfileLockPath: string,\n): Promise<string | undefined> {\n  try {\n    const file = fs.readFileSync(podfileLockPath, 'utf8');\n\n    const checksumLine = file\n      .split('\\n')\n      .find((line) => line.includes('PODFILE CHECKSUM'));\n\n    if (checksumLine) {\n      return checksumLine.split(': ')[1];\n    }\n\n    return undefined;\n  } catch {\n    return undefined;\n  }\n}\n\nasync function install(\n  packageJson: Record<string, any>,\n  cachedDependenciesHash: string | undefined,\n  currentDependenciesHash: string,\n  iosFolderPath: string,\n) {\n  const loader = getLoader('Installing CocoaPods...');\n  try {\n    await installPods(loader, {\n      skipBundleInstall: !!cachedDependenciesHash,\n      iosFolderPath,\n    });\n    cacheManager.set(packageJson.name, 'dependencies', currentDependenciesHash);\n    loader.succeed();\n  } catch (error) {\n    loader.fail();\n    throw new CLIError(\n      `Something when wrong while installing CocoaPods. Please run ${chalk.bold(\n        'pod install',\n      )} manually`,\n      error as Error,\n    );\n  }\n}\n\nexport default async function resolvePods(\n  root: string,\n  sourceDir: string,\n  nativeDependencies: NativeDependencies,\n  platformName: ApplePlatform,\n  options?: ResolvePodsOptions,\n) {\n  const packageJson = getPackageJson(root);\n  const podfilePath = path.join(sourceDir, 'Podfile'); // sourceDir is calculated based on Podfile location, see getProjectConfig()\n\n  const podfileLockPath = path.join(sourceDir, 'Podfile.lock');\n  const platformFolderPath = podfilePath\n    ? podfilePath.slice(0, podfilePath.lastIndexOf('/'))\n    : path.join(root, platformName);\n  const podsPath = path.join(platformFolderPath, 'Pods');\n  const arePodsInstalled = fs.existsSync(podsPath);\n  const platformDependencies = getPlatformDependencies(\n    nativeDependencies,\n    platformName,\n  );\n  const dependenciesString = dependenciesToString(platformDependencies);\n  const currentDependenciesHash = generateMd5Hash(dependenciesString);\n  // Users can manually add dependencies to Podfile, so we can't entirely rely on `dependencies` from `config`'s output.\n  const currentPodfileHash = generateMd5Hash(\n    fs.readFileSync(podfilePath, 'utf8'),\n  );\n  let currentPodfileLockChecksum = await getChecksum(podfileLockPath);\n\n  const cachedPodfileHash = cacheManager.get(packageJson.name, 'podfile');\n  const cachedPodfileLockChecksum = cacheManager.get(\n    packageJson.name,\n    'podfileLock',\n  );\n\n  const cachedDependenciesHash = cacheManager.get(\n    packageJson.name,\n    'dependencies',\n  );\n\n  if (options?.forceInstall) {\n    await install(\n      packageJson,\n      cachedDependenciesHash,\n      currentDependenciesHash,\n      platformFolderPath,\n    );\n  } else if (\n    arePodsInstalled &&\n    compareMd5Hashes(currentDependenciesHash, cachedDependenciesHash) &&\n    compareMd5Hashes(currentPodfileHash, cachedPodfileHash) &&\n    compareMd5Hashes(currentPodfileLockChecksum, cachedPodfileLockChecksum)\n  ) {\n    cacheManager.set(packageJson.name, 'dependencies', currentDependenciesHash);\n    cacheManager.set(packageJson.name, 'podfile', currentPodfileHash);\n    cacheManager.set(\n      packageJson.name,\n      'podfileLock',\n      currentPodfileLockChecksum ?? '',\n    );\n  } else {\n    const loader = getLoader('Installing CocoaPods...');\n    try {\n      await installPods(loader, {\n        skipBundleInstall: !!cachedDependenciesHash,\n        newArchEnabled: options?.newArchEnabled,\n        iosFolderPath: platformFolderPath,\n      });\n      cacheManager.set(\n        packageJson.name,\n        'dependencies',\n        currentDependenciesHash,\n      );\n      cacheManager.set(packageJson.name, 'podfile', currentPodfileHash);\n      // We need to read again the checksum because value changed after running `pod install`\n      currentPodfileLockChecksum = await getChecksum(podfileLockPath);\n      cacheManager.set(\n        packageJson.name,\n        'podfileLock',\n        currentPodfileLockChecksum ?? '',\n      );\n      loader.succeed();\n    } catch (error) {\n      loader.fail();\n      throw new CLIError(\n        `Something when wrong while installing CocoaPods. Please run ${chalk.bold(\n          'pod install',\n        )} manually`,\n        error as Error,\n      );\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAKA;AAAwC;AAgBxC,SAASA,cAAc,CAACC,IAAY,EAAE;EACpC,IAAI;IACF,OAAOC,OAAO,CAACC,eAAI,CAACC,IAAI,CAACH,IAAI,EAAE,cAAc,CAAC,CAAC;EACjD,CAAC,CAAC,MAAM;IACN,MAAM,KAAII,oBAAQ,EAChB,gFAAgF,CACjF;EACH;AACF;AAEO,SAASC,uBAAuB,CACrCC,YAAgC,EAChCC,YAA2B,EAC3B;EACA,OAAOC,MAAM,CAACC,IAAI,CAACH,YAAY,CAAC,CAC7BI,MAAM,CAAEC,UAAU;IAAA;IAAA,gCAAKL,YAAY,CAACK,UAAU,CAAC,CAACC,SAAS,0DAAlC,sBAAqCL,YAAY,CAAC;EAAA,EAAC,CAC1EM,GAAG,CACDF,UAAU;IAAA;IAAA,OACR,GAAEA,UAAW,IACZ,2BACEL,YAAY,CAACK,UAAU,CAAC,CAACC,SAAS,2DAAlC,uBACEL,YAAY,CACb,EACDO,OACH,EAAC;EAAA,EACL,CACAC,IAAI,EAAE;AACX;AAEO,SAASC,oBAAoB,CAACV,YAAsB,EAAE;EAC3D,OAAOA,YAAY,CAACH,IAAI,CAAC,IAAI,CAAC;AAChC;AAEO,SAASc,eAAe,CAACC,IAAY,EAAE;EAC5C,OAAO,IAAAC,oBAAU,EAAC,KAAK,CAAC,CAACC,MAAM,CAACF,IAAI,CAAC,CAACG,MAAM,CAAC,KAAK,CAAC;AACrD;AAEO,SAASC,gBAAgB,CAACC,KAAc,EAAEC,KAAc,EAAE;EAC/D,OAAOD,KAAK,KAAKC,KAAK;AACxB;AAEA,eAAeC,WAAW,CACxBC,eAAuB,EACM;EAC7B,IAAI;IACF,MAAMC,IAAI,GAAGC,kBAAE,CAACC,YAAY,CAACH,eAAe,EAAE,MAAM,CAAC;IAErD,MAAMI,YAAY,GAAGH,IAAI,CACtBI,KAAK,CAAC,IAAI,CAAC,CACXC,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;IAEpD,IAAIJ,YAAY,EAAE;MAChB,OAAOA,YAAY,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpC;IAEA,OAAOI,SAAS;EAClB,CAAC,CAAC,MAAM;IACN,OAAOA,SAAS;EAClB;AACF;AAEA,eAAeC,OAAO,CACpBC,WAAgC,EAChCC,sBAA0C,EAC1CC,uBAA+B,EAC/BC,aAAqB,EACrB;EACA,MAAMC,MAAM,GAAG,IAAAC,qBAAS,EAAC,yBAAyB,CAAC;EACnD,IAAI;IACF,MAAM,IAAAC,oBAAW,EAACF,MAAM,EAAE;MACxBG,iBAAiB,EAAE,CAAC,CAACN,sBAAsB;MAC3CE;IACF,CAAC,CAAC;IACFK,wBAAY,CAACC,GAAG,CAACT,WAAW,CAACU,IAAI,EAAE,cAAc,EAAER,uBAAuB,CAAC;IAC3EE,MAAM,CAACO,OAAO,EAAE;EAClB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdR,MAAM,CAACS,IAAI,EAAE;IACb,MAAM,KAAI9C,oBAAQ,EACf,+DAA8D+C,gBAAK,CAACC,IAAI,CACvE,aAAa,CACb,WAAU,EACZH,KAAK,CACN;EACH;AACF;AAEe,eAAeI,WAAW,CACvCrD,IAAY,EACZsD,SAAiB,EACjBC,kBAAsC,EACtChD,YAA2B,EAC3BiD,OAA4B,EAC5B;EACA,MAAMnB,WAAW,GAAGtC,cAAc,CAACC,IAAI,CAAC;EACxC,MAAMyD,WAAW,GAAGvD,eAAI,CAACC,IAAI,CAACmD,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;;EAErD,MAAM5B,eAAe,GAAGxB,eAAI,CAACC,IAAI,CAACmD,SAAS,EAAE,cAAc,CAAC;EAC5D,MAAMI,kBAAkB,GAAGD,WAAW,GAClCA,WAAW,CAACE,KAAK,CAAC,CAAC,EAAEF,WAAW,CAACG,WAAW,CAAC,GAAG,CAAC,CAAC,GAClD1D,eAAI,CAACC,IAAI,CAACH,IAAI,EAAEO,YAAY,CAAC;EACjC,MAAMsD,QAAQ,GAAG3D,eAAI,CAACC,IAAI,CAACuD,kBAAkB,EAAE,MAAM,CAAC;EACtD,MAAMI,gBAAgB,GAAGlC,kBAAE,CAACmC,UAAU,CAACF,QAAQ,CAAC;EAChD,MAAMG,oBAAoB,GAAG3D,uBAAuB,CAClDkD,kBAAkB,EAClBhD,YAAY,CACb;EACD,MAAM0D,kBAAkB,GAAGjD,oBAAoB,CAACgD,oBAAoB,CAAC;EACrE,MAAMzB,uBAAuB,GAAGtB,eAAe,CAACgD,kBAAkB,CAAC;EACnE;EACA,MAAMC,kBAAkB,GAAGjD,eAAe,CACxCW,kBAAE,CAACC,YAAY,CAAC4B,WAAW,EAAE,MAAM,CAAC,CACrC;EACD,IAAIU,0BAA0B,GAAG,MAAM1C,WAAW,CAACC,eAAe,CAAC;EAEnE,MAAM0C,iBAAiB,GAAGvB,wBAAY,CAACwB,GAAG,CAAChC,WAAW,CAACU,IAAI,EAAE,SAAS,CAAC;EACvE,MAAMuB,yBAAyB,GAAGzB,wBAAY,CAACwB,GAAG,CAChDhC,WAAW,CAACU,IAAI,EAChB,aAAa,CACd;EAED,MAAMT,sBAAsB,GAAGO,wBAAY,CAACwB,GAAG,CAC7ChC,WAAW,CAACU,IAAI,EAChB,cAAc,CACf;EAED,IAAIS,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEe,YAAY,EAAE;IACzB,MAAMnC,OAAO,CACXC,WAAW,EACXC,sBAAsB,EACtBC,uBAAuB,EACvBmB,kBAAkB,CACnB;EACH,CAAC,MAAM,IACLI,gBAAgB,IAChBxC,gBAAgB,CAACiB,uBAAuB,EAAED,sBAAsB,CAAC,IACjEhB,gBAAgB,CAAC4C,kBAAkB,EAAEE,iBAAiB,CAAC,IACvD9C,gBAAgB,CAAC6C,0BAA0B,EAAEG,yBAAyB,CAAC,EACvE;IACAzB,wBAAY,CAACC,GAAG,CAACT,WAAW,CAACU,IAAI,EAAE,cAAc,EAAER,uBAAuB,CAAC;IAC3EM,wBAAY,CAACC,GAAG,CAACT,WAAW,CAACU,IAAI,EAAE,SAAS,EAAEmB,kBAAkB,CAAC;IACjErB,wBAAY,CAACC,GAAG,CACdT,WAAW,CAACU,IAAI,EAChB,aAAa,EACboB,0BAA0B,IAAI,EAAE,CACjC;EACH,CAAC,MAAM;IACL,MAAM1B,MAAM,GAAG,IAAAC,qBAAS,EAAC,yBAAyB,CAAC;IACnD,IAAI;MACF,MAAM,IAAAC,oBAAW,EAACF,MAAM,EAAE;QACxBG,iBAAiB,EAAE,CAAC,CAACN,sBAAsB;QAC3CkC,cAAc,EAAEhB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEgB,cAAc;QACvChC,aAAa,EAAEkB;MACjB,CAAC,CAAC;MACFb,wBAAY,CAACC,GAAG,CACdT,WAAW,CAACU,IAAI,EAChB,cAAc,EACdR,uBAAuB,CACxB;MACDM,wBAAY,CAACC,GAAG,CAACT,WAAW,CAACU,IAAI,EAAE,SAAS,EAAEmB,kBAAkB,CAAC;MACjE;MACAC,0BAA0B,GAAG,MAAM1C,WAAW,CAACC,eAAe,CAAC;MAC/DmB,wBAAY,CAACC,GAAG,CACdT,WAAW,CAACU,IAAI,EAChB,aAAa,EACboB,0BAA0B,IAAI,EAAE,CACjC;MACD1B,MAAM,CAACO,OAAO,EAAE;IAClB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdR,MAAM,CAACS,IAAI,EAAE;MACb,MAAM,KAAI9C,oBAAQ,EACf,+DAA8D+C,gBAAK,CAACC,IAAI,CACvE,aAAa,CACb,WAAU,EACZH,KAAK,CACN;IACH;EACF;AACF"}