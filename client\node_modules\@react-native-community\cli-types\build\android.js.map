{"version": 3, "names": [], "sources": ["../src/android.ts"], "sourcesContent": ["export interface AndroidProjectConfig {\n  sourceDir: string;\n  appName: string;\n  packageName: string;\n  applicationId: string;\n  mainActivity: string;\n  dependencyConfiguration?: string;\n  watchModeCommandParams?: string[];\n  assets: string[];\n}\n\nexport type AndroidProjectParams = {\n  sourceDir?: string;\n  appName?: string;\n  manifestPath?: string;\n  packageName?: string;\n  dependencyConfiguration?: string;\n  watchModeCommandParams?: string[];\n  assets?: string[];\n};\n\nexport type AndroidDependencyConfig = {\n  sourceDir: string;\n  packageImportPath: string | null;\n  packageInstance: string | null;\n  dependencyConfiguration?: string;\n  buildTypes: string[];\n  libraryName?: string | null;\n  componentDescriptors?: string[] | null;\n  cmakeListsPath?: string | null;\n  cxxModuleCMakeListsModuleName?: string | null;\n  cxxModuleCMakeListsPath?: string | null;\n  cxxModuleHeaderName?: string | null;\n  isPureCxxDependency?: boolean;\n};\n\nexport type AndroidDependencyParams = {\n  sourceDir?: string;\n  manifestPath?: string;\n  packageName?: string;\n  dependencyConfiguration?: string;\n  packageImportPath?: string;\n  packageInstance?: string;\n  buildTypes?: string[];\n  libraryName?: string | null;\n  componentDescriptors?: string[] | null;\n  cmakeListsPath?: string | null;\n  cxxModuleCMakeListsModuleName?: string | null;\n  cxxModuleCMakeListsPath?: string | null;\n  cxxModuleHeaderName?: string | null;\n};\n"], "mappings": ""}