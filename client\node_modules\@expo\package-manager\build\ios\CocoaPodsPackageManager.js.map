{"version": 3, "file": "CocoaPodsPackageManager.js", "sourceRoot": "", "sources": ["../../src/ios/CocoaPodsPackageManager.ts"], "names": [], "mappings": ";;;;;;AAAA,oEAA0E;AAC1E,kDAA0B;AAC1B,2BAAgC;AAEhC,4CAAoB;AACpB,gDAAwB;AAExB,0CAAgD;AAIhD,MAAa,cAAe,SAAQ,KAAK;IAM9B;IACA;IANA,IAAI,GAAG,gBAAgB,CAAC;IACxB,qBAAqB,GAAG,IAAI,CAAC;IAEtC,YACE,OAAe,EACR,IAAwB,EACxB,KAAa;QAEpB,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,eAAe,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAH3D,SAAI,GAAJ,IAAI,CAAoB;QACxB,UAAK,GAAL,KAAK,CAAQ;IAGtB,CAAC;CACF;AAXD,wCAWC;AAED,SAAgB,6BAA6B,CAAC,WAAmB;IAC/D,wGAAwG;IACxG,MAAM,OAAO,GAAG,WAAW,CAAC,KAAK,CAC/B,qGAAqG,CACtG,CAAC;IACF,IAAI,OAAO,EAAE;QACX,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;KACjC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AATD,sEASC;AAED,MAAa,uBAAuB;IAClC,OAAO,CAAe;IAEd,MAAM,CAAU;IAExB,MAAM,CAAC,iBAAiB,CAAC,WAAmB;QAC1C,IAAI,uBAAuB,CAAC,WAAW,CAAC,WAAW,CAAC;YAAE,OAAO,WAAW,CAAC;QACzE,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QACjD,IAAI,uBAAuB,CAAC,WAAW,CAAC,UAAU,CAAC;YAAE,OAAO,UAAU,CAAC;QACvE,MAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;QACrD,IAAI,uBAAuB,CAAC,WAAW,CAAC,YAAY,CAAC;YAAE,OAAO,YAAY,CAAC;QAC3E,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,WAAmB;QACpC,OAAO,IAAA,eAAU,EAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;IACvD,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAC7B,iBAA0B,KAAK,EAC/B,eAA6B,EAAE,KAAK,EAAE,SAAS,EAAE;QAEjD,MAAM,OAAO,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,eAAe,CAAC,CAAC;QAE1D,IAAI;YACF,mIAAmI;YACnI,MAAM,IAAA,qBAAU,EAAC,KAAK,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;SAChD;QAAC,OAAO,KAAU,EAAE;YACnB,IAAI,cAAc,EAAE;gBAClB,MAAM,IAAI,cAAc,CACtB,wDAAwD,EACxD,gBAAgB,EAChB,KAAK,CACN,CAAC;aACH;YACD,2EAA2E;YAC3E,MAAM,IAAA,sBAAc,EAAC,CAAC,KAAK,EAAE,GAAG,OAAO,CAAC,EAAE,YAAY,CAAC,CAAC;SACzD;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,eAA6B,EAAE,KAAK,EAAE,SAAS,EAAE;QAC7E,MAAM,IAAA,qBAAU,EAAC,MAAM,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,YAAY,CAAC,CAAC;IAChE,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAC9B,eAA6B,EAAE,KAAK,EAAE,SAAS,EAAE;QAEjD,MAAM,IAAA,qBAAU,EAAC,MAAM,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,YAAY,CAAC,CAAC;IACnE,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,EAC3B,cAAc,GAAG,KAAK,EACtB,YAAY,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,GAIpC;QACC,IAAI,CAAC,YAAY,EAAE;YACjB,YAAY,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;SACrC;QACD,MAAM,MAAM,GAAG,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC;QAE1C,IAAI;YACF,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;YAC9E,MAAM,uBAAuB,CAAC,kBAAkB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;YAC/E,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;YAC/E,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,KAAU,EAAE;YACnB,IAAI,CAAC,MAAM,EAAE;gBACX,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,iDAAiD,CAAC,CAAC,CAAC;gBAC7E,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;gBACtD,OAAO,CAAC,GAAG,CAAC,0DAA0D,CAAC,CAAC;aACzE;YACD,IAAI;gBACF,MAAM,uBAAuB,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;gBAChE,IAAI,CAAC,CAAC,MAAM,uBAAuB,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,EAAE;oBACtE,IAAI;wBACF,MAAM,uBAAuB,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;wBAC7D,8CAA8C;wBAC9C,IAAI,CAAC,CAAC,MAAM,uBAAuB,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,EAAE;4BACtE,MAAM,IAAI,cAAc,CACtB,gHAAgH,EAChH,QAAQ,EACR,KAAK,CACN,CAAC;yBACH;qBACF;oBAAC,OAAO,KAAU,EAAE;wBACnB,MAAM,IAAI,cAAc,CACtB,mGAAmG,EACnG,QAAQ,EACR,KAAK,CACN,CAAC;qBACH;iBACF;gBAED,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;gBACpF,OAAO,IAAI,CAAC;aACb;YAAC,OAAO,KAAU,EAAE;gBACnB,CAAC,MAAM;oBACL,OAAO,CAAC,IAAI,CACV,eAAK,CAAC,MAAM,CACV,wGAAwG,CACzG,CACF,CAAC;gBACJ,MAAM,IAAI,cAAc,CACtB,iGAAiG,EACjG,QAAQ,EACR,KAAK,CACN,CAAC;aACH;SACF;IACH,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,WAAmB,EAAE,MAAe;QACrD,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE;YACjC,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC,CAAC;YACnF,OAAO,KAAK,CAAC;SACd;QACD,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,WAAW,CAAC,EAAE;YACrD,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,4CAA4C,CAAC,CAAC,CAAC;YACnF,OAAO,KAAK,CAAC;SACd;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAC9B,eAA6B,EAAE,KAAK,EAAE,SAAS,EAAE;QAEjD,IAAI;YACF,MAAM,IAAA,qBAAU,EAAC,KAAK,EAAE,CAAC,WAAW,CAAC,EAAE,YAAY,CAAC,CAAC;YACrD,OAAO,IAAI,CAAC;SACb;QAAC,MAAM;YACN,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED,YAAY,EAAE,GAAG,EAAE,MAAM,EAAqC;QAC5D,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG;YACb,GAAG;YACH,4GAA4G;YAC5G,gFAAgF;YAChF,KAAK,EAAE,MAAM;SACd,CAAC;IACJ,CAAC;IAED,IAAI,IAAI;QACN,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,sGAAsG;IACtG,KAAK,CAAC,YAAY,CAAC,EAAE,OAAO,KAAwB,EAAE;QACpD,MAAM,IAAI,CAAC,aAAa,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IACxC,CAAC;IAEM,mBAAmB;QACxB,OAAO,uBAAuB,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACnE,CAAC;IAEM,eAAe;QACpB,OAAO,uBAAuB,CAAC,eAAe,CAAC;YAC7C,cAAc,EAAE,IAAI;YACpB,YAAY,EAAE,IAAI,CAAC,OAAO;SAC3B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,EAC5B,KAAK,EACL,YAAY,GAAG,IAAI,EACnB,eAAe,GAAG,EAAE,EACpB,OAAO,GAMR;QACC,+BAA+B;QAC/B,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACjB,MAAM,KAAK,CAAC;SACb;QAED,8IAA8I;QAC9I,4DAA4D;QAC5D,IAAI,CAAC,YAAY,EAAE;YACjB,uGAAuG;YACvG,MAAM,0BAA0B,CAAC,KAAK,EAAE;gBACtC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG;aACtB,CAAC,CAAC;SACJ;QAED,iCAAiC;QACjC,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QAErD,gFAAgF;QAChF,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC;QAE7E,IAAI,CAAC,aAAa,IAAI,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;YAC7D,iCAAiC;YACjC,6EAA6E;YAC7E,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC;gBAC9B,OAAO;gBACP,gBAAgB,EAAE,IAAI;gBACtB,0HAA0H;gBAC1H,YAAY,EAAE,KAAK;gBACnB,eAAe;aAChB,CAAC,CAAC;SACJ;QACD,wDAAwD;QACxD,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAEpC,yDAAyD;QACzD,kFAAkF;QAElF,wEAAwE;QACxE,OAAO,MAAM,IAAI,CAAC,0BAA0B,CAC1C,CAAC,QAAQ,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,EACrF;YACE,aAAa;gBACX,MAAM,aAAa,GAAG,oBAAoB,eAAK,CAAC,IAAI,CAClD,aAAa,CACd,0CAA0C,CAAC;gBAC5C,OAAO,aAAa,CAAC;YACvB,CAAC;YACD,OAAO;YACP,eAAe;SAChB,CACF,CAAC;QACF,4FAA4F;QAC5F,oCAAoC;QACpC,aAAa;QACb,yBAAyB;QACzB,qBAAqB;QACrB,MAAM;IACR,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,EAC1B,gBAAgB,EAChB,GAAG,KAAK,KAMN,EAAE;QACJ,OAAO,MAAM,IAAI,CAAC,0BAA0B,CAC1C,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,EACpE;YACE,aAAa,CAAC,KAAU;gBACtB,gFAAgF;gBAChF,OAAO,uBAAuB,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC;YAC3E,CAAC;YACD,GAAG,KAAK;SACT,CACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,0BAA0B,CACtC,OAAiB,EACjB,EACE,aAAa,EACb,GAAG,KAAK,KAMN,EAAE;QAEN,IAAI;YACF,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;SACtC;QAAC,OAAO,KAAU,EAAE;YACnB,IAAI,aAAa,EAAE;gBACjB,MAAM,OAAO,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;gBACrC,IAAI,KAAK,CAAC,OAAO,EAAE;oBACjB,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBAC1C;gBACD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBAChB,OAAO,CAAC,IAAI,CAAC,eAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;iBACrC;aACF;YAED,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,EAAE,KAAK,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC;SAChE;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,KAAe,EAAE,UAAoB;QAChE,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAED,QAAQ,CAAC,QAAkB,EAAE;QAC3B,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAED,WAAW,CAAC,QAAkB,EAAE;QAC9B,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAED,cAAc,CAAC,QAAkB,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAED,WAAW,CAAC,QAAkB,EAAE;QAC9B,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAED,cAAc,CAAC,QAAkB,EAAE;QACjC,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAED,iBAAiB,CAAC,QAAkB,EAAE;QACpC,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,qBAAU,EAAC,KAAK,EAAE,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QACxE,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,GAAW;QAC3B,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;IACnC,CAAC;IAED,UAAU;IACF,KAAK,CAAC,kBAAkB;QAC9B,IAAI;YACF,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;SAC1C;QAAC,OAAO,KAAU,EAAE;YACnB,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;YAEhE,MAAM,IAAI,cAAc,CACtB,gDAAgD,EAChD,gBAAgB,EAChB,KAAK,CACN,CAAC;SACH;IACH,CAAC;IAED,sBAAsB;IACtB,KAAK,CAAC,SAAS,CAAC,IAAc;QAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;SACxC;QACD,MAAM,OAAO,GAAG,IAAA,qBAAU,EACxB,KAAK,EACL;YACE,GAAG,IAAI;YACP,0CAA0C;YAC1C,QAAQ;SACT,EACD;YACE,sDAAsD;YACtD,GAAG,IAAI,CAAC,OAAO;YACf,4GAA4G;YAC5G,2EAA2E;YAE3E,gFAAgF;YAChF,yGAAyG;YACzG,KAAK,EAAE,MAAM;SACd,CACF,CAAC;QAEF,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,yDAAyD;YACzD,gIAAgI;YAChI,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE;gBACxB,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;aAC3C;SACF;QAED,OAAO,MAAM,OAAO,CAAC;IACvB,CAAC;CACF;AA5XD,0DA4XC;AAED,sGAAsG;AACtG,SAAS,mBAAmB,CAAC,WAAmB;IAC9C,MAAM,MAAM,GAAG,WAAW,CAAC;IAC3B,MAAM,oBAAoB,GACxB,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;IAC5E,OAAO,oBAAoB,CAAC;AAC9B,CAAC;AAED,SAAgB,mBAAmB,CAAC,MAAc;IAChD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CACxB,4EAA4E,CAC7E,CAAC;IAEF,OAAO;QACL,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,IAAI;QACjC,gBAAgB,EAAE,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;KAC9B,CAAC;AACJ,CAAC;AATD,kDASC;AAED,SAAgB,uBAAuB,CAAC,WAAmB;IACzD,MAAM,WAAW,GAAG,6BAA6B,CAAC,WAAW,CAAC,CAAC;IAC/D,MAAM,aAAa,GAAG,mBAAmB,CAAC,WAAW,CAAC,CAAC;IAEvD,IAAI,OAAe,CAAC;IACpB,IAAI,WAAW,EAAE;QACf,OAAO,GAAG,qBAAqB,WAAW,CAAC,CAAC,CAAC,MAAM,eAAK,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;KACvF;SAAM,IAAI,aAAa,EAAE,aAAa,EAAE;QACvC,OAAO,GAAG,qBAAqB,aAAa,EAAE,aAAa,GAAG,CAAC;KAChE;SAAM;QACL,OAAO,GAAG,wBAAwB,CAAC;KACpC;IACD,OAAO,IAAI,gDAAgD,CAAC;IAC5D,OAAO,EAAE,OAAO,EAAE,GAAG,aAAa,EAAE,CAAC;AACvC,CAAC;AAdD,0DAcC;AAED;;;;;GAKG;AACH,SAAgB,0BAA0B,CACxC,KAA0B,EAC1B,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,EAA6B;IAElD,iCAAiC;IACjC,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,YAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;IAErD,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,qDAAqD,CAAC,EAAE;QAC7E,4CAA4C;QAC5C,KAAK,CAAC,OAAO,GAAG,kCAAkC,GAAG,4CAA4C,CAAC;KACnG;SAAM,IAAI,mBAAmB,CAAC,WAAW,CAAC,EAAE;QAC3C,6DAA6D;QAC7D,MAAM,WAAW,GAAG,6BAA6B,CAAC,WAAW,CAAC,CAAC;QAC/D,IAAI,MAAc,CAAC;QACnB,IAAI,WAAW,EAAE;YACf,MAAM,GAAG,qBAAqB,WAAW,CAAC,CAAC,CAAC,MAAM,eAAK,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;SACrF;aAAM;YACL,MAAM,GAAG,0DAA0D,CAAC;SACrE;QAED,mHAAmH;QACnH,kGAAkG;QAClG,IAAI,QAAgB,CAAC;QACrB,IAAI,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE;YACpB,uHAAuH;YACvH,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,2BAA2B,CAAC,EAAE;gBACrD,QAAQ,GAAG,2BAA2B,WAAW,CAAC,CAAC,CAAC,0EAA0E,CAAC;aAChI;iBAAM;gBACL,QAAQ,GAAG,wBAAwB,WAAW,CAAC,CAAC,CAAC,0EAA0E,CAAC;aAC7H;SACF;aAAM;YACL,cAAc;YACd,QAAQ,GAAG,6GAA6G,CAAC;SAC1H;QACD,KAAK,CAAC,OAAO,GAAG,GAAG,MAAM,KAAK,QAAQ,EAAE,CAAC;QAEzC,qGAAqG;QACrG,IAAI,KAAK,CAAC,MAAM,EAAE;YAChB,MAAM,kBAAkB,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,YAAE,CAAC,GAAG,CAAC,CAAC;YACtD,6EAA6E;YAC7E,MAAM,YAAY,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9E,IAAI,YAAY,KAAK,CAAC,CAAC,EAAE;gBACvB,MAAM,OAAO,GAAG,kBAAkB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,YAAE,CAAC,GAAG,CAAC,CAAC;gBACpE,KAAK,CAAC,OAAO,IAAI,OAAO,eAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;aAC/C;SACF;QACD,OAAO,IAAI,cAAc,CACvB,6CAA6C,EAC7C,gBAAgB,EAChB,KAAK,CACN,CAAC;KACH;SAAM;QACL,IAAI,MAAM,GAAkB,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QAEhD,qDAAqD;QACrD,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAElE,sEAAsE;QACtE,IAAI,WAAW,EAAE;YACf,gDAAgD;YAChD,IAAI,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,kCAAkC,CAAC,EAAE;gBAC5D,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC;aACpB;YACD,MAAM,GAAG,IAAI,CAAC;SACf;QAED,KAAK,CAAC,OAAO,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACjF;IAED,OAAO,IAAI,cAAc,CAAC,+BAA+B,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;AACtF,CAAC;AAtED,gEAsEC"}