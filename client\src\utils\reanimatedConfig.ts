// React Native Reanimated configuration to prevent precision issues

import { Extrapolation } from 'react-native-reanimated';

/**
 * Default interpolation configuration that prevents precision loss
 */
export const defaultInterpolationConfig = {
  extrapolateLeft: Extrapolation.CLAMP,
  extrapolateRight: Extrapolation.CLAMP,
};

/**
 * Safe animation configuration for opacity values
 */
export const opacityAnimationConfig = {
  inputRange: [0, 1],
  outputRange: [0, 1],
  ...defaultInterpolationConfig,
};

/**
 * Safe animation configuration for transform values
 */
export const transformAnimationConfig = {
  ...defaultInterpolationConfig,
};

/**
 * Worklet function to safely clamp values
 */
export const clampWorklet = (value: number, min: number = 0, max: number = 1): number => {
  'worklet';
  return Math.max(min, Math.min(max, value));
};

/**
 * Worklet function to safely round values
 */
export const roundWorklet = (value: number, decimals: number = 3): number => {
  'worklet';
  const factor = Math.pow(10, decimals);
  return Math.round(value * factor) / factor;
};

/**
 * Worklet function for safe opacity values
 */
export const safeOpacityWorklet = (value: number): number => {
  'worklet';
  return clampWorklet(roundWorklet(value), 0, 1);
};
