
import express from 'express';
import mongoose from 'mongoose';
import cors from 'cors';
import dotenv from 'dotenv';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { Request, Response } from 'express';
import cookieParser from 'cookie-parser';
import http from 'http';
import { Server } from 'socket.io';
import path from 'path';
import fs from 'fs';
import { DeliveryPartner } from './models/deliveryPartnerModel';
import { Branch } from './models/branchModel';
import { Coupon } from './models/couponModel';
import { Category } from './models/categoryModel';
import { Product } from './models/productModel';
import { Order } from './models/orderModel';
import {
  uploadDeliveryPartnerPhoto,
  uploadCategoryImage,
  uploadProductImages,
  handleUploadErrors
} from './middleware/uploadMiddleware';

// Load environment variables
dotenv.config();

// MongoDB connection
const MONGO_URI = process.env.MONGO_URI || 'mongodb://localhost:27017/vendor-management';
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const CORS_ORIGIN = process.env.CORS_ORIGIN || 'http://localhost:5173';
const PORT = process.env.PORT || 3000; // Changed to 3000 to match client app expectations

// Define user schema
const userSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  userType: { type: String, enum: ['admin', 'vendor'], required: true },
  vendorId: { type: mongoose.Schema.Types.ObjectId, ref: 'Vendor' },
  createdAt: { type: Date, default: Date.now },
});

// Define vendor schema
const vendorSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true },
  phone: { type: String },
  address: { type: String },
  status: { type: String, enum: ['active', 'inactive', 'pending'], default: 'pending' },
  permissions: [
    {
      name: { type: String, required: true },
      description: { type: String },
      enabled: { type: Boolean, default: false },
    },
  ],
  createdAt: { type: Date, default: Date.now },
});

// Define customer schema
const customerSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true },
  phone: { type: String, required: true, unique: true },
  status: { type: String, enum: ['active', 'inactive'], default: 'active' },
  totalOrders: { type: Number, default: 0 },
  totalSpent: { type: Number, default: 0 },
  lastOrder: { type: Date },
  orders: [{ type: mongoose.Schema.Types.ObjectId, ref: 'Order' }],
  address: {
    street: String,
    city: String,
    state: String,
    zipCode: String
  },
  location: {
    latitude: Number,
    longitude: Number
  },
  createdAt: { type: Date, default: Date.now },
});

// Define chat schemas
const messageSchema = new mongoose.Schema({
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  content: {
    type: String,
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  read: {
    type: Boolean,
    default: false
  }
});

const conversationSchema = new mongoose.Schema({
  participants: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  messages: [messageSchema],
  lastUpdated: {
    type: Date,
    default: Date.now
  },
  title: {
    type: String
  },
  type: {
    type: String,
    enum: ['direct', 'group'],
    default: 'direct'
  }
});

// Create models
const User = mongoose.model('User', userSchema);
const Vendor = mongoose.model('Vendor', vendorSchema);
const Customer = mongoose.model('Customer', customerSchema);
const Message = mongoose.model('Message', messageSchema);
const Conversation = mongoose.model('Conversation', conversationSchema);

// Create Express app and HTTP server
const app = express();
const server = http.createServer(app);

// Create socket.io server
const io = new Server(server, {
  cors: {
    origin: CORS_ORIGIN,
    methods: ["GET", "POST"],
    credentials: true
  }
});

app.use(express.json());
app.use(cookieParser());
app.use(cors({
  origin: CORS_ORIGIN,
  credentials: true,
}));

// Serve static files from uploads directory
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Connect to MongoDB
mongoose.connect(MONGO_URI)
  .then(() => console.log('Connected to MongoDB'))
  .catch(err => console.error('MongoDB connection error:', err));

// Authentication middleware
const authenticateToken = (req: Request & { user?: any }, res: Response, next: Function) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) return res.status(401).json({ success: false, message: 'Access denied' });

  jwt.verify(token, JWT_SECRET, (err: any, user: any) => {
    if (err) return res.status(403).json({ success: false, message: 'Invalid or expired token' });
    req.user = user;
    next();
  });
};

// Login route
app.post('/api/auth/login', async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;
    console.log('Login attempt:', { email, password });

    // Find user by email
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(400).json({ success: false, message: 'Invalid email or password' });
    }

    // Check password
    const validPassword = await bcrypt.compare(password, user.password);
    if (!validPassword) {
      return res.status(400).json({ success: false, message: 'Invalid email or password' });
    }

    // Create token
    const token = jwt.sign(
      { id: user._id, email: user.email, userType: user.userType },
      JWT_SECRET,
      { expiresIn: '1d' }
    );

    // Return user data and token
    res.json({
      success: true,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        userType: user.userType,
        vendorId: user.vendorId || null,
      },
      token,
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get current user
app.get('/api/auth/me', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const user = await User.findById(req.user.id).select('-password');
    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    res.json({
      success: true,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        userType: user.userType,
        vendorId: user.vendorId || null,
      },
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get customers
app.get('/api/customers', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Only admin can view all customers
    if (req.user.userType !== 'admin') {
      return res.status(403).json({ success: false, message: 'Not authorized to access this resource' });
    }

    const customers = await Customer.find()
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const totalCustomers = await Customer.countDocuments();
    const totalPages = Math.ceil(totalCustomers / limit);

    res.json({
      success: true,
      customers: customers.map(customer => ({
        id: customer._id,
        name: customer.name,
        email: customer.email,
        status: customer.status,
        totalOrders: customer.totalOrders,
        totalSpent: customer.totalSpent,
        lastOrder: customer.lastOrder,
        orders: customer.orders,
        createdAt: customer.createdAt,
      })),
      totalPages,
      currentPage: page,
      totalCustomers,
    });
  } catch (error) {
    console.error('Get customers error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get vendors
app.get('/api/vendors', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Only admin can view all vendors
    if (req.user.userType !== 'admin') {
      return res.status(403).json({ success: false, message: 'Not authorized to access this resource' });
    }

    const vendors = await Vendor.find()
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const totalVendors = await Vendor.countDocuments();
    const totalPages = Math.ceil(totalVendors / limit);

    res.json({
      success: true,
      vendors: vendors.map(vendor => ({
        id: vendor._id,
        name: vendor.name,
        email: vendor.email,
        phone: vendor.phone,
        address: vendor.address,
        status: vendor.status,
        permissions: vendor.permissions,
        createdAt: vendor.createdAt,
      })),
      totalPages,
      currentPage: page,
      totalVendors,
    });
  } catch (error) {
    console.error('Get vendors error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// CHAT ROUTES

// Get conversations for current user
app.get('/api/chat/conversations', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const userId = req.user.id;

    const conversations = await Conversation.find({
      participants: userId
    }).populate('participants', 'name email')
      .sort({ lastUpdated: -1 });

    res.json({
      success: true,
      conversations: conversations.map(conv => {
        // Get other participants (exclude current user)
        const otherParticipants = conv.participants.filter(
          (p: any) => p._id.toString() !== userId
        );

        // For direct chats, use the other user's name as title
        let title = conv.title;
        if (conv.type === 'direct' && otherParticipants.length > 0) {
          title = (otherParticipants[0] as any).name;
        }

        // Get the last message if any
        let lastMessage = null;
        if (conv.messages && conv.messages.length > 0) {
          const message = conv.messages[conv.messages.length - 1];
          const sender = conv.participants.find(
            (p: any) => p._id.toString() === message.sender.toString()
          );

          lastMessage = {
            id: message._id,
            content: message.content,
            senderId: message.sender,
            senderName: sender ? (sender as any).name : 'Unknown',
            timestamp: message.timestamp,
            read: message.read
          };
        }

        // Count unread messages
        const unreadCount = conv.messages.filter(
          (msg: any) => !msg.read && msg.sender.toString() !== userId
        ).length;

        return {
          id: conv._id,
          title,
          participants: conv.participants.map((p: any) => ({
            id: p._id,
            name: p.name,
            email: p.email
          })),
          type: conv.type,
          lastMessage,
          unreadCount,
          lastUpdated: conv.lastUpdated
        };
      })
    });
  } catch (error) {
    console.error('Get conversations error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get messages for a conversation
app.get('/api/chat/conversations/:id/messages', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const userId = req.user.id;
    const conversationId = req.params.id;

    const conversation = await Conversation.findOne({
      _id: conversationId,
      participants: userId
    }).populate('participants', 'name email')
      .populate({
        path: 'messages.sender',
        select: 'name email'
      });

    if (!conversation) {
      return res.status(404).json({ success: false, message: 'Conversation not found' });
    }

    // Mark messages as read
    if (conversation.messages && conversation.messages.length > 0) {
      for (const message of conversation.messages) {
        if (!message.read && message.sender._id.toString() !== userId) {
          message.read = true;
        }
      }
      await conversation.save();
    }

    res.json({
      success: true,
      messages: conversation.messages.map((msg: any) => ({
        id: msg._id,
        content: msg.content,
        senderId: msg.sender._id,
        senderName: msg.sender.name,
        timestamp: msg.timestamp,
        read: msg.read
      }))
    });
  } catch (error) {
    console.error('Get messages error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Send a message
app.post('/api/chat/conversations/:id/messages', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const userId = req.user.id;
    const conversationId = req.params.id;
    const { content } = req.body;

    if (!content || content.trim() === '') {
      return res.status(400).json({ success: false, message: 'Message content is required' });
    }

    const conversation = await Conversation.findOne({
      _id: conversationId,
      participants: userId
    }).populate('participants', 'name email');

    if (!conversation) {
      return res.status(404).json({ success: false, message: 'Conversation not found' });
    }

    const newMessage = {
      sender: userId,
      content,
      timestamp: new Date(),
      read: false
    };

    conversation.messages.push(newMessage);
    conversation.lastUpdated = new Date();
    await conversation.save();

    // Get the sender info
    const sender = await User.findById(userId);

    // Emit the message to all participants via socket.io
    conversation.participants.forEach((participant: any) => {
      io.to(participant._id.toString()).emit('new_message', {
        conversationId,
        message: {
          id: conversation.messages[conversation.messages.length - 1]._id,
          content,
          senderId: userId,
          senderName: sender ? sender.name : 'Unknown',
          timestamp: new Date(),
          read: false
        }
      });
    });

    res.json({
      success: true,
      message: {
        id: conversation.messages[conversation.messages.length - 1]._id,
        content,
        senderId: userId,
        senderName: sender ? sender.name : 'Unknown',
        timestamp: new Date(),
        read: false
      }
    });
  } catch (error) {
    console.error('Send message error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get delivery partners
app.get('/api/delivery-partners', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Only admin can view all delivery partners
    if (req.user.userType !== 'admin') {
      return res.status(403).json({ success: false, message: 'Not authorized to access this resource' });
    }

    const deliveryPartners = await DeliveryPartner.find()
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const totalDeliveryPartners = await DeliveryPartner.countDocuments();
    const totalPages = Math.ceil(totalDeliveryPartners / limit);

    res.json({
      success: true,
      deliveryPartners: deliveryPartners.map(partner => ({
        id: partner._id,
        name: partner.name,
        email: partner.email,
        phone: partner.phone,
        address: partner.address,
        governmentId: partner.governmentId,
        drivingLicense: partner.drivingLicense,
        vehicleNumber: partner.vehicleNumber,
        vehicleType: partner.vehicleType,
        area: partner.area,
        status: partner.status,
        photoUrl: partner.photoUrl,
        deliveryCount: partner.deliveryCount,
        createdAt: partner.createdAt,
      })),
      totalPages,
      currentPage: page,
      totalDeliveryPartners,
    });
  } catch (error) {
    console.error('Get delivery partners error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get vendor-specific delivery partners
app.get('/api/vendors/:vendorId/delivery-partners', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const vendorId = req.params.vendorId;

    // Check if user is admin or the vendor
    if (req.user.userType !== 'admin' && req.user.vendorId.toString() !== vendorId) {
      return res.status(403).json({ success: false, message: 'Not authorized to access this resource' });
    }

    const deliveryPartners = await DeliveryPartner.find({ vendorId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const totalDeliveryPartners = await DeliveryPartner.countDocuments({ vendorId });
    const totalPages = Math.ceil(totalDeliveryPartners / limit);

    res.json({
      success: true,
      deliveryPartners: deliveryPartners.map(partner => ({
        id: partner._id,
        name: partner.name,
        email: partner.email,
        phone: partner.phone,
        address: partner.address,
        governmentId: partner.governmentId,
        drivingLicense: partner.drivingLicense,
        vehicleNumber: partner.vehicleNumber,
        vehicleType: partner.vehicleType,
        area: partner.area,
        status: partner.status,
        photoUrl: partner.photoUrl,
        deliveryCount: partner.deliveryCount,
        createdAt: partner.createdAt,
      })),
      totalPages,
      currentPage: page,
      totalDeliveryPartners,
    });
  } catch (error) {
    console.error('Get vendor delivery partners error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Create a new delivery partner
app.post('/api/delivery-partners', authenticateToken, (req: Request & { user?: any }, res: Response) => {
  uploadDeliveryPartnerPhoto(req, res, handleUploadErrors(req, res, async () => {
    try {
      // Only admin can create delivery partners
      if (req.user.userType !== 'admin') {
        return res.status(403).json({ success: false, message: 'Not authorized to create delivery partners' });
      }

      const {
        name,
        email,
        phone,
        address,
        governmentId,
        drivingLicense,
        vehicleNumber,
        vehicleType,
        area,
        status
      } = req.body;

      // Validate required fields
      if (!name || !email || !phone || !address || !governmentId || !drivingLicense || !vehicleNumber || !vehicleType || !area) {
        return res.status(400).json({ success: false, message: 'All required fields must be provided' });
      }

      // Create new delivery partner
      const deliveryPartner = new DeliveryPartner({
        name,
        email,
        phone,
        address,
        governmentId,
        drivingLicense,
        vehicleNumber,
        vehicleType,
        area,
        status: status || 'active',
        photoUrl: req.file ? req.file.path : null,
        deliveryCount: 0
      });

      await deliveryPartner.save();

      res.status(201).json({
        success: true,
        deliveryPartner: {
          id: deliveryPartner._id,
          name: deliveryPartner.name,
          email: deliveryPartner.email,
          phone: deliveryPartner.phone,
          address: deliveryPartner.address,
          governmentId: deliveryPartner.governmentId,
          drivingLicense: deliveryPartner.drivingLicense,
          vehicleNumber: deliveryPartner.vehicleNumber,
          vehicleType: deliveryPartner.vehicleType,
          area: deliveryPartner.area,
          status: deliveryPartner.status,
          photoUrl: deliveryPartner.photoUrl ? `${process.env.UPLOAD_BASE_URL}/delivery-partners/${path.basename(deliveryPartner.photoUrl)}` : null,
          deliveryCount: deliveryPartner.deliveryCount,
          createdAt: deliveryPartner.createdAt,
        }
      });
    } catch (error) {
      console.error('Create delivery partner error:', error);
      res.status(500).json({ success: false, message: 'Server error' });
    }
  }));
});

// Get all branches/connections (admin only)
app.get('/api/branches', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    // Only admin can view all branches
    if (req.user.userType !== 'admin') {
      return res.status(403).json({ success: false, message: 'Not authorized to access this resource' });
    }

    const branches = await Branch.find()
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('vendorId', 'name email')
      .populate('deliveryPartners', 'name email phone');

    const totalBranches = await Branch.countDocuments();
    const totalPages = Math.ceil(totalBranches / limit);

    res.json({
      success: true,
      branches: branches.map(branch => ({
        id: branch._id,
        name: branch.name,
        description: branch.description,
        status: branch.status,
        vendor: branch.vendorId ? {
          id: branch.vendorId._id,
          name: branch.vendorId.name,
          email: branch.vendorId.email
        } : null,
        deliveryPartners: branch.deliveryPartners ? branch.deliveryPartners.map(partner => ({
          id: partner._id,
          name: partner.name,
          email: partner.email,
          phone: partner.phone
        })) : [],
        createdAt: branch.createdAt,
      })),
      totalPages,
      currentPage: page,
      totalBranches,
    });
  } catch (error) {
    console.error('Get branches error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get vendor-specific branches/connections
app.get('/api/vendors/:vendorId/branches', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const vendorId = req.params.vendorId;

    // Check if user is admin or the vendor
    if (req.user.userType !== 'admin' && req.user.vendorId.toString() !== vendorId) {
      return res.status(403).json({ success: false, message: 'Not authorized to access this resource' });
    }

    const branches = await Branch.find({ vendorId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('deliveryPartners', 'name email phone');

    const totalBranches = await Branch.countDocuments({ vendorId });
    const totalPages = Math.ceil(totalBranches / limit);

    res.json({
      success: true,
      branches: branches.map(branch => ({
        id: branch._id,
        name: branch.name,
        description: branch.description,
        status: branch.status,
        deliveryPartners: branch.deliveryPartners ? branch.deliveryPartners.map(partner => ({
          id: partner._id,
          name: partner.name,
          email: partner.email,
          phone: partner.phone
        })) : [],
        createdAt: branch.createdAt,
      })),
      totalPages,
      currentPage: page,
      totalBranches,
    });
  } catch (error) {
    console.error('Get vendor branches error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Create a new branch (connection between vendor and delivery partners)
app.post('/api/branches', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    // Only admin can create branches
    if (req.user.userType !== 'admin') {
      return res.status(403).json({ success: false, message: 'Not authorized to create connections' });
    }

    const {
      name,
      description,
      address,
      city,
      state,
      pincode,
      phone,
      location,
      status,
      vendorId
    } = req.body;

    // Validate required fields
    if (!name || !vendorId) {
      return res.status(400).json({ success: false, message: 'Name and vendor ID are required' });
    }

    // Check if vendor exists
    const vendor = await Vendor.findById(vendorId);
    if (!vendor) {
      return res.status(404).json({ success: false, message: 'Vendor not found' });
    }

    // Parse location if provided as string
    let locationData = location;
    if (typeof location === 'string') {
      try {
        locationData = JSON.parse(location);
      } catch (err) {
        console.error('Error parsing location data:', err);
        locationData = null;
      }
    }

    // Create new branch (connection)
    const branch = new Branch({
      name,
      description,
      address,
      city,
      state,
      pincode,
      phone,
      location: locationData,
      status: status || 'active',
      vendorId,
      deliveryPartners: []
    });

    await branch.save();

    res.status(201).json({
      success: true,
      branch: {
        id: branch._id,
        name: branch.name,
        description: branch.description,
        address: branch.address,
        city: branch.city,
        state: branch.state,
        pincode: branch.pincode,
        phone: branch.phone,
        location: branch.location,
        status: branch.status,
        vendorId: branch.vendorId,
        deliveryPartners: [],
        createdAt: branch.createdAt,
      }
    });
  } catch (error) {
    console.error('Create branch error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Create a vendor-specific branch (connection between vendor and delivery partners)
app.post('/api/vendors/:vendorId/branches', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const vendorId = req.params.vendorId;

    // Check if user is admin or the vendor
    if (req.user.userType !== 'admin' && req.user.vendorId.toString() !== vendorId) {
      return res.status(403).json({ success: false, message: 'Not authorized to create connections for this vendor' });
    }

    const {
      name,
      description,
      address,
      city,
      state,
      pincode,
      phone,
      location,
      status
    } = req.body;

    // Validate required fields
    if (!name) {
      return res.status(400).json({ success: false, message: 'Name is required' });
    }

    // Check if vendor exists
    const vendor = await Vendor.findById(vendorId);
    if (!vendor) {
      return res.status(404).json({ success: false, message: 'Vendor not found' });
    }

    // Parse location if provided as string
    let locationData = location;
    if (typeof location === 'string') {
      try {
        locationData = JSON.parse(location);
      } catch (err) {
        console.error('Error parsing location data:', err);
        locationData = null;
      }
    }

    // Create new branch (connection)
    const branch = new Branch({
      name,
      description,
      address,
      city,
      state,
      pincode,
      phone,
      location: locationData,
      status: status || 'active',
      vendorId,
      deliveryPartners: []
    });

    await branch.save();

    res.status(201).json({
      success: true,
      branch: {
        id: branch._id,
        name: branch.name,
        description: branch.description,
        address: branch.address,
        city: branch.city,
        state: branch.state,
        pincode: branch.pincode,
        phone: branch.phone,
        location: branch.location,
        status: branch.status,
        vendorId: branch.vendorId,
        deliveryPartners: [],
        createdAt: branch.createdAt,
      }
    });
  } catch (error) {
    console.error('Create vendor branch error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Assign delivery partner to branch
app.post('/api/branches/:branchId/delivery-partners/:deliveryPartnerId', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const branchId = req.params.branchId;
    const deliveryPartnerId = req.params.deliveryPartnerId;

    // Find the branch
    const branch = await Branch.findById(branchId);
    if (!branch) {
      return res.status(404).json({ success: false, message: 'Branch not found' });
    }

    // Check if user is admin or the vendor of the branch
    if (req.user.userType !== 'admin' && req.user.vendorId.toString() !== branch.vendorId.toString()) {
      return res.status(403).json({ success: false, message: 'Not authorized to modify this branch' });
    }

    // Find the delivery partner
    const deliveryPartner = await DeliveryPartner.findById(deliveryPartnerId);
    if (!deliveryPartner) {
      return res.status(404).json({ success: false, message: 'Delivery partner not found' });
    }

    // Check if delivery partner is already assigned to this branch
    if (branch.deliveryPartners.includes(deliveryPartnerId)) {
      return res.status(400).json({ success: false, message: 'Delivery partner already assigned to this branch' });
    }

    // Add delivery partner to branch
    branch.deliveryPartners.push(deliveryPartnerId);
    await branch.save();

    // Update delivery partner with branch ID
    deliveryPartner.branchId = branchId;
    await deliveryPartner.save();

    res.json({
      success: true,
      message: 'Delivery partner assigned to branch successfully',
      branch: {
        id: branch._id,
        name: branch.name,
        deliveryPartners: branch.deliveryPartners
      },
      deliveryPartner: {
        id: deliveryPartner._id,
        name: deliveryPartner.name,
        branchId: deliveryPartner.branchId
      }
    });
  } catch (error) {
    console.error('Assign delivery partner error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Remove delivery partner from branch
app.delete('/api/branches/:branchId/delivery-partners/:deliveryPartnerId', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const branchId = req.params.branchId;
    const deliveryPartnerId = req.params.deliveryPartnerId;

    // Find the branch
    const branch = await Branch.findById(branchId);
    if (!branch) {
      return res.status(404).json({ success: false, message: 'Branch not found' });
    }

    // Check if user is admin or the vendor of the branch
    if (req.user.userType !== 'admin' && req.user.vendorId.toString() !== branch.vendorId.toString()) {
      return res.status(403).json({ success: false, message: 'Not authorized to modify this branch' });
    }

    // Find the delivery partner
    const deliveryPartner = await DeliveryPartner.findById(deliveryPartnerId);
    if (!deliveryPartner) {
      return res.status(404).json({ success: false, message: 'Delivery partner not found' });
    }

    // Check if delivery partner is assigned to this branch
    if (!branch.deliveryPartners.includes(deliveryPartnerId)) {
      return res.status(400).json({ success: false, message: 'Delivery partner not assigned to this branch' });
    }

    // Remove delivery partner from branch
    branch.deliveryPartners = branch.deliveryPartners.filter(id => id.toString() !== deliveryPartnerId);
    await branch.save();

    // Update delivery partner to remove branch ID
    deliveryPartner.branchId = null;
    await deliveryPartner.save();

    res.json({
      success: true,
      message: 'Delivery partner removed from branch successfully',
      branch: {
        id: branch._id,
        name: branch.name,
        deliveryPartners: branch.deliveryPartners
      },
      deliveryPartner: {
        id: deliveryPartner._id,
        name: deliveryPartner.name,
        branchId: deliveryPartner.branchId
      }
    });
  } catch (error) {
    console.error('Remove delivery partner error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Create a vendor-specific delivery partner
app.post('/api/vendors/:vendorId/delivery-partners', authenticateToken, (req: Request & { user?: any }, res: Response) => {
  uploadDeliveryPartnerPhoto(req, res, handleUploadErrors(req, res, async () => {
    try {
      const vendorId = req.params.vendorId;

      // Check if user is admin or the vendor
      if (req.user.userType !== 'admin' && req.user.vendorId.toString() !== vendorId) {
        return res.status(403).json({ success: false, message: 'Not authorized to create delivery partners for this vendor' });
      }

      const {
        name,
        email,
        phone,
        address,
        governmentId,
        drivingLicense,
        vehicleNumber,
        vehicleType,
        area,
        status
      } = req.body;

      // Validate required fields
      if (!name || !email || !phone || !address || !governmentId || !drivingLicense || !vehicleNumber || !vehicleType || !area) {
        return res.status(400).json({ success: false, message: 'All required fields must be provided' });
      }

      // Create new delivery partner
      const deliveryPartner = new DeliveryPartner({
        name,
        email,
        phone,
        address,
        governmentId,
        drivingLicense,
        vehicleNumber,
        vehicleType,
        area,
        status: status || 'active',
        photoUrl: req.file ? req.file.path : null,
        deliveryCount: 0,
        vendorId
      });

      await deliveryPartner.save();

      res.status(201).json({
        success: true,
        deliveryPartner: {
          id: deliveryPartner._id,
          name: deliveryPartner.name,
          email: deliveryPartner.email,
          phone: deliveryPartner.phone,
          address: deliveryPartner.address,
          governmentId: deliveryPartner.governmentId,
          drivingLicense: deliveryPartner.drivingLicense,
          vehicleNumber: deliveryPartner.vehicleNumber,
          vehicleType: deliveryPartner.vehicleType,
          area: deliveryPartner.area,
          status: deliveryPartner.status,
          photoUrl: deliveryPartner.photoUrl ? `${process.env.UPLOAD_BASE_URL}/delivery-partners/${path.basename(deliveryPartner.photoUrl)}` : null,
          deliveryCount: deliveryPartner.deliveryCount,
          createdAt: deliveryPartner.createdAt,
        }
      });
    } catch (error) {
      console.error('Create vendor delivery partner error:', error);
      res.status(500).json({ success: false, message: 'Server error' });
    }
  }));
});

// Create a new conversation
app.post('/api/chat/conversations', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const userId = req.user.id;
    const { participants, title, type = 'direct' } = req.body;

    if (!participants || !Array.isArray(participants) || participants.length === 0) {
      return res.status(400).json({ success: false, message: 'Participants are required' });
    }

    // Add current user to participants if not already included
    const allParticipants = [...new Set([userId, ...participants])];

    // For direct chats, check if a conversation already exists
    if (type === 'direct' && allParticipants.length === 2) {
      const existingConversation = await Conversation.findOne({
        type: 'direct',
        participants: { $all: allParticipants, $size: 2 }
      });

      if (existingConversation) {
        return res.json({
          success: true,
          conversation: {
            id: existingConversation._id,
            participants: allParticipants,
            title: title || 'Direct Chat',
            type: 'direct',
            messages: [],
            lastUpdated: new Date()
          }
        });
      }
    }

    // Create a new conversation
    const newConversation = new Conversation({
      participants: allParticipants,
      title: title || (type === 'direct' ? 'Direct Chat' : 'Group Chat'),
      type,
      messages: [],
      lastUpdated: new Date()
    });

    await newConversation.save();

    res.status(201).json({
      success: true,
      conversation: {
        id: newConversation._id,
        participants: allParticipants,
        title: newConversation.title,
        type: newConversation.type,
        messages: [],
        lastUpdated: newConversation.lastUpdated
      }
    });
  } catch (error) {
    console.error('Create conversation error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get all coupons
app.get('/api/coupons', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const search = req.query.search as string || '';

    // Only admin can view all coupons
    if (req.user.userType !== 'admin') {
      return res.status(403).json({ success: false, message: 'Not authorized to access this resource' });
    }

    // Build query
    let query: any = {};
    if (search) {
      query = {
        $or: [
          { code: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ]
      };
    }

    const coupons = await Coupon.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('createdBy', 'name email');

    const totalCoupons = await Coupon.countDocuments(query);
    const totalPages = Math.ceil(totalCoupons / limit);

    res.json({
      success: true,
      coupons: coupons.map(coupon => ({
        id: coupon._id,
        code: coupon.code,
        description: coupon.description,
        discountType: coupon.discountType,
        discountValue: coupon.discountValue,
        minimumPurchase: coupon.minimumPurchase,
        maximumDiscount: coupon.maximumDiscount,
        startDate: coupon.startDate,
        expiryDate: coupon.expiryDate,
        usageLimit: coupon.usageLimit,
        usedCount: coupon.usedCount,
        isActive: coupon.isActive,
        couponType: coupon.couponType,
        applicableProducts: coupon.applicableProducts,
        applicableCategories: coupon.applicableCategories,
        applicableVendors: coupon.applicableVendors,
        createdBy: coupon.createdBy ? {
          id: coupon.createdBy._id,
          name: coupon.createdBy.name,
          email: coupon.createdBy.email
        } : null,
        createdAt: coupon.createdAt
      })),
      totalPages,
      currentPage: page,
      totalCoupons
    });
  } catch (error) {
    console.error('Get coupons error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get coupon by ID
app.get('/api/coupons/:id', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const couponId = req.params.id;

    // Only admin can view coupon details
    if (req.user.userType !== 'admin') {
      return res.status(403).json({ success: false, message: 'Not authorized to access this resource' });
    }

    const coupon = await Coupon.findById(couponId)
      .populate('createdBy', 'name email')
      .populate('applicableProducts', 'name price')
      .populate('applicableCategories', 'name')
      .populate('applicableVendors', 'name email');

    if (!coupon) {
      return res.status(404).json({ success: false, message: 'Coupon not found' });
    }

    res.json({
      success: true,
      coupon: {
        id: coupon._id,
        code: coupon.code,
        description: coupon.description,
        discountType: coupon.discountType,
        discountValue: coupon.discountValue,
        minimumPurchase: coupon.minimumPurchase,
        maximumDiscount: coupon.maximumDiscount,
        startDate: coupon.startDate,
        expiryDate: coupon.expiryDate,
        usageLimit: coupon.usageLimit,
        usedCount: coupon.usedCount,
        isActive: coupon.isActive,
        couponType: coupon.couponType,
        applicableProducts: coupon.applicableProducts,
        applicableCategories: coupon.applicableCategories,
        applicableVendors: coupon.applicableVendors,
        createdBy: coupon.createdBy ? {
          id: coupon.createdBy._id,
          name: coupon.createdBy.name,
          email: coupon.createdBy.email
        } : null,
        createdAt: coupon.createdAt
      }
    });
  } catch (error) {
    console.error('Get coupon error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Create a new coupon
app.post('/api/coupons', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    // Only admin can create coupons
    if (req.user.userType !== 'admin') {
      return res.status(403).json({ success: false, message: 'Not authorized to create coupons' });
    }

    const {
      code,
      description,
      discountType,
      discountValue,
      minimumPurchase,
      maximumDiscount,
      startDate,
      expiryDate,
      usageLimit,
      isActive,
      couponType,
      applicableProducts,
      applicableCategories,
      applicableVendors
    } = req.body;

    // Validate required fields
    if (!code || !discountType || !discountValue || !expiryDate) {
      return res.status(400).json({
        success: false,
        message: 'Code, discount type, discount value, and expiry date are required'
      });
    }

    // Check if coupon code already exists
    const existingCoupon = await Coupon.findOne({ code: code.toUpperCase() });
    if (existingCoupon) {
      return res.status(400).json({ success: false, message: 'Coupon code already exists' });
    }

    // Create new coupon
    const coupon = new Coupon({
      code: code.toUpperCase(),
      description,
      discountType,
      discountValue,
      minimumPurchase: minimumPurchase || 0,
      maximumDiscount: maximumDiscount || null,
      startDate: startDate || new Date(),
      expiryDate,
      usageLimit: usageLimit || null,
      isActive: isActive !== undefined ? isActive : true,
      couponType: couponType || 'all',
      applicableProducts: applicableProducts || [],
      applicableCategories: applicableCategories || [],
      applicableVendors: applicableVendors || [],
      createdBy: req.user.id,
      usedCount: 0
    });

    await coupon.save();

    res.status(201).json({
      success: true,
      coupon: {
        id: coupon._id,
        code: coupon.code,
        description: coupon.description,
        discountType: coupon.discountType,
        discountValue: coupon.discountValue,
        minimumPurchase: coupon.minimumPurchase,
        maximumDiscount: coupon.maximumDiscount,
        startDate: coupon.startDate,
        expiryDate: coupon.expiryDate,
        usageLimit: coupon.usageLimit,
        usedCount: coupon.usedCount,
        isActive: coupon.isActive,
        couponType: coupon.couponType,
        applicableProducts: coupon.applicableProducts,
        applicableCategories: coupon.applicableCategories,
        applicableVendors: coupon.applicableVendors,
        createdBy: req.user.id,
        createdAt: coupon.createdAt
      }
    });
  } catch (error) {
    console.error('Create coupon error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Update a coupon
app.put('/api/coupons/:id', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const couponId = req.params.id;

    // Only admin can update coupons
    if (req.user.userType !== 'admin') {
      return res.status(403).json({ success: false, message: 'Not authorized to update coupons' });
    }

    const {
      code,
      description,
      discountType,
      discountValue,
      minimumPurchase,
      maximumDiscount,
      startDate,
      expiryDate,
      usageLimit,
      isActive,
      couponType,
      applicableProducts,
      applicableCategories,
      applicableVendors
    } = req.body;

    // Validate required fields
    if (!code || !discountType || !discountValue || !expiryDate) {
      return res.status(400).json({
        success: false,
        message: 'Code, discount type, discount value, and expiry date are required'
      });
    }

    // Check if coupon exists
    const coupon = await Coupon.findById(couponId);
    if (!coupon) {
      return res.status(404).json({ success: false, message: 'Coupon not found' });
    }

    // Check if updated code already exists (if code is changed)
    if (code.toUpperCase() !== coupon.code) {
      const existingCoupon = await Coupon.findOne({ code: code.toUpperCase() });
      if (existingCoupon) {
        return res.status(400).json({ success: false, message: 'Coupon code already exists' });
      }
    }

    // Update coupon
    coupon.code = code.toUpperCase();
    coupon.description = description;
    coupon.discountType = discountType;
    coupon.discountValue = discountValue;
    coupon.minimumPurchase = minimumPurchase || 0;
    coupon.maximumDiscount = maximumDiscount || null;
    coupon.startDate = startDate || coupon.startDate;
    coupon.expiryDate = expiryDate;
    coupon.usageLimit = usageLimit || null;
    coupon.isActive = isActive !== undefined ? isActive : coupon.isActive;
    coupon.couponType = couponType || coupon.couponType;
    coupon.applicableProducts = applicableProducts || coupon.applicableProducts;
    coupon.applicableCategories = applicableCategories || coupon.applicableCategories;
    coupon.applicableVendors = applicableVendors || coupon.applicableVendors;

    await coupon.save();

    res.json({
      success: true,
      coupon: {
        id: coupon._id,
        code: coupon.code,
        description: coupon.description,
        discountType: coupon.discountType,
        discountValue: coupon.discountValue,
        minimumPurchase: coupon.minimumPurchase,
        maximumDiscount: coupon.maximumDiscount,
        startDate: coupon.startDate,
        expiryDate: coupon.expiryDate,
        usageLimit: coupon.usageLimit,
        usedCount: coupon.usedCount,
        isActive: coupon.isActive,
        couponType: coupon.couponType,
        applicableProducts: coupon.applicableProducts,
        applicableCategories: coupon.applicableCategories,
        applicableVendors: coupon.applicableVendors,
        createdAt: coupon.createdAt
      }
    });
  } catch (error) {
    console.error('Update coupon error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Delete a coupon
app.delete('/api/coupons/:id', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const couponId = req.params.id;

    // Only admin can delete coupons
    if (req.user.userType !== 'admin') {
      return res.status(403).json({ success: false, message: 'Not authorized to delete coupons' });
    }

    // Check if coupon exists
    const coupon = await Coupon.findById(couponId);
    if (!coupon) {
      return res.status(404).json({ success: false, message: 'Coupon not found' });
    }

    // Delete coupon
    await Coupon.findByIdAndDelete(couponId);

    res.json({
      success: true,
      message: 'Coupon deleted successfully'
    });
  } catch (error) {
    console.error('Delete coupon error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Validate a coupon
app.post('/api/coupons/validate', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const {
      code,
      amount,
      productIds = [],
      categoryIds = [],
      vendorId
    } = req.body;

    if (!code || !amount) {
      return res.status(400).json({ success: false, message: 'Coupon code and order amount are required' });
    }

    // Find coupon
    const coupon = await Coupon.findOne({
      code: code.toUpperCase(),
      isActive: true,
      expiryDate: { $gt: new Date() },
      startDate: { $lte: new Date() }
    }).populate('applicableProducts')
      .populate('applicableCategories')
      .populate('applicableVendors');

    if (!coupon) {
      return res.status(404).json({ success: false, message: 'Invalid or expired coupon' });
    }

    // Check usage limit
    if (coupon.usageLimit !== null && coupon.usedCount >= coupon.usageLimit) {
      return res.status(400).json({ success: false, message: 'Coupon usage limit reached' });
    }

    // Check minimum purchase
    if (amount < coupon.minimumPurchase) {
      return res.status(400).json({
        success: false,
        message: `Minimum purchase amount of ${coupon.minimumPurchase} required`
      });
    }

    // Check if coupon is applicable based on coupon type
    if (coupon.couponType !== 'all') {
      // Check for product-specific coupon
      if (coupon.couponType === 'product' && coupon.applicableProducts && coupon.applicableProducts.length > 0) {
        const applicableProductIds = coupon.applicableProducts.map((product: any) =>
          product._id ? product._id.toString() : product.toString()
        );

        const hasApplicableProduct = productIds.some((id: string) =>
          applicableProductIds.includes(id)
        );

        if (!hasApplicableProduct) {
          return res.status(400).json({
            success: false,
            message: 'This coupon is not applicable to the selected products'
          });
        }
      }

      // Check for category-specific coupon
      if (coupon.couponType === 'category' && coupon.applicableCategories && coupon.applicableCategories.length > 0) {
        const applicableCategoryIds = coupon.applicableCategories.map((category: any) =>
          category._id ? category._id.toString() : category.toString()
        );

        const hasApplicableCategory = categoryIds.some((id: string) =>
          applicableCategoryIds.includes(id)
        );

        if (!hasApplicableCategory) {
          return res.status(400).json({
            success: false,
            message: 'This coupon is not applicable to the selected product categories'
          });
        }
      }

      // Check for vendor-specific coupon
      if (coupon.couponType === 'vendor' && coupon.applicableVendors && coupon.applicableVendors.length > 0) {
        const applicableVendorIds = coupon.applicableVendors.map((vendor: any) =>
          vendor._id ? vendor._id.toString() : vendor.toString()
        );

        if (!vendorId || !applicableVendorIds.includes(vendorId)) {
          return res.status(400).json({
            success: false,
            message: 'This coupon is not applicable to the selected vendor'
          });
        }
      }
    }

    // Calculate discount
    let discount = 0;
    if (coupon.discountType === 'percentage') {
      discount = (amount * coupon.discountValue) / 100;
      // Apply maximum discount if set
      if (coupon.maximumDiscount !== null && discount > coupon.maximumDiscount) {
        discount = coupon.maximumDiscount;
      }
    } else {
      // Fixed discount
      discount = coupon.discountValue;
      // Ensure discount doesn't exceed order amount
      if (discount > amount) {
        discount = amount;
      }
    }

    // Round discount to 2 decimal places
    discount = Math.round(discount * 100) / 100;

    res.json({
      success: true,
      coupon: {
        id: coupon._id,
        code: coupon.code,
        description: coupon.description,
        discountType: coupon.discountType,
        discountValue: coupon.discountValue,
        discount: discount,
        finalAmount: amount - discount,
        couponType: coupon.couponType,
        applicableProducts: coupon.applicableProducts && coupon.applicableProducts.length > 0 ?
          coupon.applicableProducts.map((product: any) => ({
            id: product._id || product,
            name: product.name || null
          })) : [],
        applicableCategories: coupon.applicableCategories && coupon.applicableCategories.length > 0 ?
          coupon.applicableCategories.map((category: any) => ({
            id: category._id || category,
            name: category.name || null
          })) : [],
        applicableVendors: coupon.applicableVendors && coupon.applicableVendors.length > 0 ?
          coupon.applicableVendors.map((vendor: any) => ({
            id: vendor._id || vendor,
            name: vendor.name || null
          })) : []
      }
    });
  } catch (error) {
    console.error('Validate coupon error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Socket.io connection handler
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  // Authenticate socket connection
  socket.on('authenticate', (token) => {
    try {
      const decoded = jwt.verify(token, JWT_SECRET);
      const userId = (decoded as any).id;

      // Join a room with the user's ID for direct messaging
      socket.join(userId);

      console.log(`User ${userId} authenticated on socket ${socket.id}`);

      // Notify the user that they're connected
      socket.emit('authenticated', { success: true, userId });

      // Mark user as online
      socket.broadcast.emit('user_status', { userId, status: 'online' });
    } catch (error) {
      console.error('Socket authentication error:', error);
      socket.emit('authenticated', { success: false, error: 'Invalid token' });
    }
  });

  // Handle read receipts
  socket.on('message_read', async ({ conversationId, messageId, userId }) => {
    try {
      const conversation = await Conversation.findById(conversationId);
      if (conversation) {
        const message = conversation.messages.id(messageId);
        if (message && !message.read) {
          message.read = true;
          await conversation.save();

          // Emit to all participants that this message was read
          conversation.participants.forEach((participantId) => {
            if (participantId.toString() !== userId) {
              io.to(participantId.toString()).emit('message_status', {
                conversationId,
                messageId,
                status: 'read'
              });
            }
          });
        }
      }
    } catch (error) {
      console.error('Message read error:', error);
    }
  });

  // Handle typing indicators
  socket.on('typing', ({ conversationId, userId, isTyping }) => {
    try {
      Conversation.findById(conversationId).then(conversation => {
        if (conversation) {
          conversation.participants.forEach((participantId) => {
            if (participantId.toString() !== userId) {
              io.to(participantId.toString()).emit('user_typing', {
                conversationId,
                userId,
                isTyping
              });
            }
          });
        }
      });
    } catch (error) {
      console.error('Typing indicator error:', error);
    }
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);

    // We would mark the user as offline here
    // but we need their userId which we don't have easily available
    // A better solution would be to maintain a mapping of socket.id to userId
  });
});

// Create a default admin user if none exists
const createDefaultAdmin = async () => {
  try {
    const adminExists = await User.findOne({ userType: 'admin' });

    if (!adminExists) {
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('admin123', salt);

      await User.create({
        name: 'Admin User',
        email: '<EMAIL>',
        password: hashedPassword,
        userType: 'admin',
      });

      console.log('Default admin user created');
    }
  } catch (error) {
    console.error('Error creating default admin:', error);
  }
};

// Create demo customers
const createDemoCustomers = async () => {
  try {
    const customersExist = await Customer.countDocuments();

    if (customersExist === 0) {
      const customers = Array.from({ length: 20 }, (_, i) => ({
        name: `Customer ${i + 1}`,
        email: `customer${i + 1}@example.com`,
        status: Math.random() > 0.2 ? 'active' : 'inactive',
        totalOrders: Math.floor(Math.random() * 20) + 1,
        totalSpent: Math.floor(Math.random() * 10000) + 500,
        lastOrder: new Date(Date.now() - Math.floor(Math.random() * 10000000000)),
        createdAt: new Date(Date.now() - Math.floor(Math.random() * 10000000000)),
      }));

      await Customer.insertMany(customers);
      console.log('Demo customers created');
    }
  } catch (error) {
    console.error('Error creating demo customers:', error);
  }
};

// Create demo conversations
const createDemoConversations = async () => {
  try {
    const conversationsExist = await Conversation.countDocuments();

    if (conversationsExist === 0) {
      // Get admin user
      const adminUser = await User.findOne({ userType: 'admin' });
      if (!adminUser) return;

      // Create a demo vendor user if not exists
      let vendorUser = await User.findOne({ userType: 'vendor' });
      if (!vendorUser) {
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash('vendor123', salt);

        const vendor = await Vendor.create({
          name: 'Demo Vendor',
          email: '<EMAIL>',
          phone: '************',
          status: 'active'
        });

        vendorUser = await User.create({
          name: 'Vendor User',
          email: '<EMAIL>',
          password: hashedPassword,
          userType: 'vendor',
          vendorId: vendor._id
        });

        console.log('Demo vendor user created');
      }

      // Create a conversation between admin and vendor
      const conversation = new Conversation({
        participants: [adminUser._id, vendorUser._id],
        title: 'Admin-Vendor Chat',
        type: 'direct',
        messages: [
          {
            sender: adminUser._id,
            content: 'Hello vendor, welcome to our platform!',
            timestamp: new Date(Date.now() - 3600000),
            read: true
          },
          {
            sender: vendorUser._id,
            content: 'Thank you! I\'m excited to start working with you.',
            timestamp: new Date(Date.now() - 3000000),
            read: true
          },
          {
            sender: adminUser._id,
            content: 'Let me know if you have any questions about our system.',
            timestamp: new Date(Date.now() - 2400000),
            read: true
          }
        ],
        lastUpdated: new Date()
      });

      await conversation.save();
      console.log('Demo conversation created');
    }
  } catch (error) {
    console.error('Error creating demo conversations:', error);
  }
};

// Create demo categories and products for client app
const createDemoData = async () => {
  try {
    // Check if categories already exist
    const categoriesExist = await Category.countDocuments();
    if (categoriesExist > 0) return;

    // Get or create a demo vendor
    let vendor = await Vendor.findOne({ email: '<EMAIL>' });
    if (!vendor) {
      vendor = await Vendor.create({
        name: 'Demo Vendor',
        email: '<EMAIL>',
        phone: '************',
        status: 'active'
      });
    }

    // Create demo branch
    let branch = await Branch.findOne({ vendorId: vendor._id });
    if (!branch) {
      branch = await Branch.create({
        name: 'Main Branch',
        description: 'Main store branch',
        address: '123 Main Street, City Center',
        city: 'Demo City',
        state: 'Demo State',
        pincode: '12345',
        phone: '************',
        location: {
          lat: 40.7128,
          lng: -74.0060
        },
        vendorId: vendor._id,
        status: 'active'
      });
    }

    // Demo categories data
    const categoriesData = [
      {
        name: 'Fruits & Vegetables',
        description: 'Fresh fruits and vegetables',
        products: [
          { name: 'Fresh Apples', price: 150, description: 'Red fresh apples', stock: 100 },
          { name: 'Bananas', price: 60, description: 'Yellow ripe bananas', stock: 80 },
          { name: 'Carrots', price: 40, description: 'Fresh orange carrots', stock: 50 },
          { name: 'Tomatoes', price: 30, description: 'Red juicy tomatoes', stock: 70 }
        ]
      },
      {
        name: 'Dairy Products',
        description: 'Fresh dairy products',
        products: [
          { name: 'Fresh Milk', price: 60, description: 'Pure cow milk', stock: 30 },
          { name: 'Cheese', price: 200, description: 'Processed cheese', stock: 25 },
          { name: 'Yogurt', price: 80, description: 'Greek yogurt', stock: 40 },
          { name: 'Butter', price: 120, description: 'Fresh butter', stock: 20 }
        ]
      },
      {
        name: 'Bakery',
        description: 'Fresh baked goods',
        products: [
          { name: 'White Bread', price: 40, description: 'Fresh white bread', stock: 15 },
          { name: 'Croissants', price: 80, description: 'Buttery croissants', stock: 12 },
          { name: 'Muffins', price: 60, description: 'Chocolate muffins', stock: 18 },
          { name: 'Bagels', price: 50, description: 'Sesame bagels', stock: 20 }
        ]
      },
      {
        name: 'Beverages',
        description: 'Refreshing drinks',
        products: [
          { name: 'Orange Juice', price: 100, description: 'Fresh orange juice', stock: 25 },
          { name: 'Coffee', price: 150, description: 'Premium coffee beans', stock: 30 },
          { name: 'Green Tea', price: 120, description: 'Organic green tea', stock: 40 },
          { name: 'Sparkling Water', price: 80, description: 'Natural sparkling water', stock: 50 }
        ]
      }
    ];

    // Create categories and products
    for (const categoryData of categoriesData) {
      const category = await Category.create({
        name: categoryData.name,
        description: categoryData.description,
        status: 'active',
        vendorId: vendor._id
      });

      // Create products for this category
      const products = [];
      for (const productData of categoryData.products) {
        const product = await Product.create({
          name: productData.name,
          description: productData.description,
          price: productData.price,
          discountPrice: productData.price * 0.9, // 10% discount
          images: [], // Empty for now, can be populated later
          category: category._id,
          vendorId: vendor._id,
          status: 'active',
          stock: productData.stock
        });
        products.push(product._id);
      }

      // Update category with product references
      category.products = products;
      await category.save();
    }

    console.log('Demo categories and products created successfully');
  } catch (error) {
    console.error('Error creating demo data:', error);
  }
};

// CATEGORY API ROUTES

// Get all categories (admin only)
app.get('/api/categories', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const search = req.query.search as string || '';
    const status = req.query.status as string || '';

    // Only admin can view all categories
    if (req.user.userType !== 'admin') {
      return res.status(403).json({ success: false, message: 'Not authorized to access this resource' });
    }

    // Build query
    let query: any = {};
    if (search) {
      query = {
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ]
      };
    }
    if (status && status !== 'all') {
      query.status = status;
    }

    const categories = await Category.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('vendorId', 'name email');

    const totalCategories = await Category.countDocuments(query);
    const totalPages = Math.ceil(totalCategories / limit);

    res.json({
      success: true,
      categories: categories.map(category => ({
        id: category._id,
        name: category.name,
        description: category.description,
        status: category.status,
        imageUrl: category.imageUrl ? `${process.env.UPLOAD_BASE_URL}/categories/${path.basename(category.imageUrl)}` : null,
        productCount: category.products ? category.products.length : 0,
        vendorId: category.vendorId ? category.vendorId._id : null,
        vendorName: category.vendorId ? (category.vendorId as any).name : null,
        createdAt: category.createdAt,
      })),
      totalPages,
      currentPage: page,
      totalCategories,
    });
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get category by ID
app.get('/api/categories/:id', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const categoryId = req.params.id;

    const category = await Category.findById(categoryId)
      .populate('vendorId', 'name email')
      .populate('products', 'name price');

    if (!category) {
      return res.status(404).json({ success: false, message: 'Category not found' });
    }

    // Check if user is admin or the vendor
    if (req.user.userType !== 'admin' &&
        (!category.vendorId || req.user.vendorId.toString() !== category.vendorId.toString())) {
      return res.status(403).json({ success: false, message: 'Not authorized to access this resource' });
    }

    res.json({
      success: true,
      category: {
        id: category._id,
        name: category.name,
        description: category.description,
        status: category.status,
        imageUrl: category.imageUrl ? `${process.env.UPLOAD_BASE_URL}/categories/${path.basename(category.imageUrl)}` : null,
        products: category.products ? category.products.map((product: any) => ({
          id: product._id,
          name: product.name,
          price: product.price
        })) : [],
        vendorId: category.vendorId ? category.vendorId._id : null,
        vendorName: category.vendorId ? (category.vendorId as any).name : null,
        createdAt: category.createdAt,
      }
    });
  } catch (error) {
    console.error('Get category error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Create a new category
app.post('/api/categories', authenticateToken, (req: Request & { user?: any }, res: Response) => {
  uploadCategoryImage(req, res, handleUploadErrors(req, res, async () => {
    try {
      // Only admin can create categories
      if (req.user.userType !== 'admin') {
        return res.status(403).json({ success: false, message: 'Not authorized to create categories' });
      }

      const {
        name,
        description,
        status,
        vendorId,
        products
      } = req.body;

      // Validate required fields
      if (!name) {
        return res.status(400).json({ success: false, message: 'Name is required' });
      }

      // Create new category
      const category = new Category({
        name,
        description,
        status: status || 'active',
        imageUrl: req.file ? req.file.path : null,
        products: products ? JSON.parse(products) : [],
        vendorId: vendorId || null,
      });

      await category.save();

      res.status(201).json({
        success: true,
        category: {
          id: category._id,
          name: category.name,
          description: category.description,
          status: category.status,
          imageUrl: category.imageUrl ? `${process.env.UPLOAD_BASE_URL}/categories/${path.basename(category.imageUrl)}` : null,
          productCount: category.products ? category.products.length : 0,
          vendorId: category.vendorId,
          createdAt: category.createdAt,
        }
      });
    } catch (error) {
      console.error('Create category error:', error);
      res.status(500).json({ success: false, message: 'Server error' });
    }
  }));
});

// Update a category
app.put('/api/categories/:id', authenticateToken, (req: Request & { user?: any }, res: Response) => {
  uploadCategoryImage(req, res, handleUploadErrors(req, res, async () => {
    try {
      const categoryId = req.params.id;

      // Check if category exists
      const category = await Category.findById(categoryId);
      if (!category) {
        return res.status(404).json({ success: false, message: 'Category not found' });
      }

      // Check if user is admin or the vendor
      if (req.user.userType !== 'admin' &&
          (!category.vendorId || req.user.vendorId.toString() !== category.vendorId.toString())) {
        return res.status(403).json({ success: false, message: 'Not authorized to update this category' });
      }

      const {
        name,
        description,
        status,
        products
      } = req.body;

      // Update category fields
      if (name) category.name = name;
      if (description !== undefined) category.description = description;
      if (status) category.status = status;
      if (products) category.products = JSON.parse(products);
      if (req.file) {
        // Delete old image if exists
        if (category.imageUrl) {
          try {
            fs.unlinkSync(category.imageUrl);
          } catch (err) {
            console.error('Error deleting old image:', err);
          }
        }
        category.imageUrl = req.file.path;
      }

      await category.save();

      res.json({
        success: true,
        category: {
          id: category._id,
          name: category.name,
          description: category.description,
          status: category.status,
          imageUrl: category.imageUrl ? `${process.env.UPLOAD_BASE_URL}/categories/${path.basename(category.imageUrl)}` : null,
          productCount: category.products ? category.products.length : 0,
          vendorId: category.vendorId,
          createdAt: category.createdAt,
        }
      });
    } catch (error) {
      console.error('Update category error:', error);
      res.status(500).json({ success: false, message: 'Server error' });
    }
  }));
});

// Delete a category
app.delete('/api/categories/:id', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const categoryId = req.params.id;

    // Check if category exists
    const category = await Category.findById(categoryId);
    if (!category) {
      return res.status(404).json({ success: false, message: 'Category not found' });
    }

    // Only admin can delete categories
    if (req.user.userType !== 'admin') {
      return res.status(403).json({ success: false, message: 'Not authorized to delete categories' });
    }

    // Delete image if exists
    if (category.imageUrl) {
      try {
        fs.unlinkSync(category.imageUrl);
      } catch (err) {
        console.error('Error deleting image:', err);
      }
    }

    // Delete category
    await Category.findByIdAndDelete(categoryId);

    res.json({
      success: true,
      message: 'Category deleted successfully'
    });
  } catch (error) {
    console.error('Delete category error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get vendor-specific categories
app.get('/api/vendors/:vendorId/categories', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const vendorId = req.params.vendorId;

    // Check if user is admin or the vendor
    if (req.user.userType !== 'admin' && req.user.vendorId.toString() !== vendorId) {
      return res.status(403).json({ success: false, message: 'Not authorized to access this resource' });
    }

    const categories = await Category.find({ vendorId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const totalCategories = await Category.countDocuments({ vendorId });
    const totalPages = Math.ceil(totalCategories / limit);

    res.json({
      success: true,
      categories: categories.map(category => ({
        id: category._id,
        name: category.name,
        description: category.description,
        status: category.status,
        imageUrl: category.imageUrl ? `${process.env.UPLOAD_BASE_URL}/categories/${path.basename(category.imageUrl)}` : null,
        productCount: category.products ? category.products.length : 0,
        createdAt: category.createdAt,
      })),
      totalPages,
      currentPage: page,
      totalCategories,
    });
  } catch (error) {
    console.error('Get vendor categories error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Create a vendor-specific category
app.post('/api/vendors/:vendorId/categories', authenticateToken, (req: Request & { user?: any }, res: Response) => {
  uploadCategoryImage(req, res, handleUploadErrors(req, res, async () => {
    try {
      const vendorId = req.params.vendorId;

      // Check if user is admin or the vendor
      if (req.user.userType !== 'admin' && req.user.vendorId.toString() !== vendorId) {
        return res.status(403).json({ success: false, message: 'Not authorized to create categories for this vendor' });
      }

      const {
        name,
        description,
        status,
        products
      } = req.body;

      // Validate required fields
      if (!name) {
        return res.status(400).json({ success: false, message: 'Name is required' });
      }

      // Create new category
      const category = new Category({
        name,
        description,
        status: status || 'active',
        imageUrl: req.file ? req.file.path : null,
        products: products ? JSON.parse(products) : [],
        vendorId,
      });

      await category.save();

      res.status(201).json({
        success: true,
        category: {
          id: category._id,
          name: category.name,
          description: category.description,
          status: category.status,
          imageUrl: category.imageUrl ? `${process.env.UPLOAD_BASE_URL}/categories/${path.basename(category.imageUrl)}` : null,
          productCount: category.products ? category.products.length : 0,
          vendorId: category.vendorId,
          createdAt: category.createdAt,
        }
      });
    } catch (error) {
      console.error('Create vendor category error:', error);
      res.status(500).json({ success: false, message: 'Server error' });
    }
  }));
});

// ============================================================================
// CLIENT APP SPECIFIC API ENDPOINTS
// ============================================================================

// Customer login endpoint for client app
app.post('/api/customer/login', async (req: Request, res: Response) => {
  try {
    const { phone } = req.body;
    console.log('Customer login attempt with phone:', phone);

    if (!phone) {
      return res.status(400).json({ success: false, message: 'Phone number is required' });
    }

    // Find or create customer by phone
    let customer = await Customer.findOne({ phone });

    if (!customer) {
      // Create new customer if doesn't exist
      customer = new Customer({
        name: `Customer ${phone.slice(-4)}`, // Default name using last 4 digits
        phone,
        email: `customer${phone.slice(-4)}@example.com`, // Default email
        status: 'active',
        totalOrders: 0,
        totalSpent: 0,
        orders: []
      });
      await customer.save();
      console.log('Created new customer:', customer._id);
    }

    // Create tokens
    const accessToken = jwt.sign(
      { id: customer._id, phone: customer.phone, userType: 'customer' },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    const refreshToken = jwt.sign(
      { id: customer._id, phone: customer.phone, userType: 'customer' },
      JWT_SECRET,
      { expiresIn: '30d' }
    );

    res.json({
      success: true,
      accessToken,
      refreshToken,
      customer: {
        id: customer._id,
        name: customer.name,
        phone: customer.phone,
        email: customer.email,
        address: customer.address || null,
        location: customer.location || null
      }
    });
  } catch (error) {
    console.error('Customer login error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Delivery partner login endpoint for client app
app.post('/api/delivery/login', async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;
    console.log('Delivery login attempt with email:', email);

    if (!email || !password) {
      return res.status(400).json({ success: false, message: 'Email and password are required' });
    }

    // Find delivery partner by email
    const deliveryPartner = await DeliveryPartner.findOne({ email });
    if (!deliveryPartner) {
      return res.status(400).json({ success: false, message: 'Invalid email or password' });
    }

    // For now, we'll use a simple password check (in production, use bcrypt)
    // Since delivery partners don't have passwords in the current model, we'll add a default
    const defaultPassword = 'delivery123';
    if (password !== defaultPassword) {
      return res.status(400).json({ success: false, message: 'Invalid email or password' });
    }

    // Create tokens
    const accessToken = jwt.sign(
      { id: deliveryPartner._id, email: deliveryPartner.email, userType: 'delivery' },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    const refreshToken = jwt.sign(
      { id: deliveryPartner._id, email: deliveryPartner.email, userType: 'delivery' },
      JWT_SECRET,
      { expiresIn: '30d' }
    );

    res.json({
      success: true,
      accessToken,
      refreshToken,
      deliveryPartner: {
        id: deliveryPartner._id,
        name: deliveryPartner.name,
        email: deliveryPartner.email,
        phone: deliveryPartner.phone,
        vehicleType: deliveryPartner.vehicleType,
        area: deliveryPartner.area,
        status: deliveryPartner.status
      }
    });
  } catch (error) {
    console.error('Delivery login error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Refresh token endpoint for client app
app.post('/api/refresh-token', async (req: Request, res: Response) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({ success: false, message: 'Refresh token is required' });
    }

    // Verify refresh token
    const decoded = jwt.verify(refreshToken, JWT_SECRET) as any;

    // Create new access token
    const newAccessToken = jwt.sign(
      { id: decoded.id, userType: decoded.userType },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    // Create new refresh token
    const newRefreshToken = jwt.sign(
      { id: decoded.id, userType: decoded.userType },
      JWT_SECRET,
      { expiresIn: '30d' }
    );

    res.json({
      success: true,
      accessToken: newAccessToken,
      refreshToken: newRefreshToken
    });
  } catch (error) {
    console.error('Refresh token error:', error);
    res.status(401).json({ success: false, message: 'Invalid refresh token' });
  }
});

// Get user info endpoint for client app
app.get('/api/user', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const userId = req.user.id;
    const userType = req.user.userType;

    let user;
    if (userType === 'customer') {
      user = await Customer.findById(userId);
    } else if (userType === 'delivery') {
      user = await DeliveryPartner.findById(userId);
    } else {
      return res.status(400).json({ success: false, message: 'Invalid user type' });
    }

    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    res.json({
      success: true,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        userType,
        ...(userType === 'customer' && {
          address: user.address,
          location: user.location
        }),
        ...(userType === 'delivery' && {
          vehicleType: user.vehicleType,
          area: user.area,
          status: user.status
        })
      }
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Update user location endpoint for client app
app.patch('/api/user', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const userId = req.user.id;
    const userType = req.user.userType;
    const updateData = req.body;

    let user;
    if (userType === 'customer') {
      user = await Customer.findByIdAndUpdate(userId, updateData, { new: true });
    } else if (userType === 'delivery') {
      user = await DeliveryPartner.findByIdAndUpdate(userId, updateData, { new: true });
    } else {
      return res.status(400).json({ success: false, message: 'Invalid user type' });
    }

    if (!user) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    res.json({
      success: true,
      message: 'User updated successfully'
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get all categories for client app
app.get('/api/categories', async (req: Request, res: Response) => {
  try {
    const categories = await Category.find({ status: 'active' })
      .populate('products', 'name price discountPrice images status')
      .sort({ createdAt: -1 });

    const formattedCategories = categories.map(category => ({
      _id: category._id,
      name: category.name,
      description: category.description,
      imageUrl: category.imageUrl,
      products: category.products.filter((product: any) => product.status === 'active')
    }));

    res.json({
      success: true,
      categories: formattedCategories
    });
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get products by category ID for client app
app.get('/api/products/:categoryId', async (req: Request, res: Response) => {
  try {
    const { categoryId } = req.params;

    const products = await Product.find({
      category: categoryId,
      status: 'active'
    }).populate('category', 'name');

    const formattedProducts = products.map(product => ({
      _id: product._id,
      name: product.name,
      description: product.description,
      price: product.price,
      discountPrice: product.discountPrice,
      images: product.images,
      category: product.category,
      stock: product.stock
    }));

    res.json({
      success: true,
      products: formattedProducts
    });
  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Create order endpoint for client app
app.post('/api/order', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const { items, branch, totalPrice } = req.body;
    const customerId = req.user.id;

    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({ success: false, message: 'Order items are required' });
    }

    if (!branch || !totalPrice) {
      return res.status(400).json({ success: false, message: 'Branch and total price are required' });
    }

    // Find the branch to get vendor info
    const branchDoc = await Branch.findById(branch);
    if (!branchDoc) {
      return res.status(404).json({ success: false, message: 'Branch not found' });
    }

    // Create order
    const order = new Order({
      customer: customerId,
      items: items.map((item: any) => ({
        product: item.productId || item._id,
        name: item.name,
        price: item.price,
        quantity: item.quantity || 1,
        total: (item.price * (item.quantity || 1))
      })),
      totalPrice,
      finalAmount: totalPrice,
      branch,
      vendor: branchDoc.vendorId,
      status: 'pending',
      paymentStatus: 'pending'
    });

    await order.save();

    // Update customer's order history
    await Customer.findByIdAndUpdate(customerId, {
      $push: { orders: order._id },
      $inc: { totalOrders: 1, totalSpent: totalPrice }
    });

    res.status(201).json({
      success: true,
      order: {
        id: order._id,
        status: order.status,
        totalPrice: order.totalPrice,
        items: order.items,
        createdAt: order.createdAt
      }
    });
  } catch (error) {
    console.error('Create order error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get order by ID for client app
app.get('/api/order/:id', authenticateToken, async (req: Request & { user?: any }, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const userType = req.user.userType;

    const order = await Order.findById(id)
      .populate('customer', 'name phone')
      .populate('items.product', 'name images')
      .populate('deliveryPartner', 'name phone vehicleType')
      .populate('branch', 'name address');

    if (!order) {
      return res.status(404).json({ success: false, message: 'Order not found' });
    }

    // Check if user has permission to view this order
    if (userType === 'customer' && order.customer._id.toString() !== userId) {
      return res.status(403).json({ success: false, message: 'Not authorized to view this order' });
    }

    if (userType === 'delivery' && (!order.deliveryPartner || order.deliveryPartner._id.toString() !== userId)) {
      return res.status(403).json({ success: false, message: 'Not authorized to view this order' });
    }

    res.json({
      success: true,
      order: {
        id: order._id,
        customer: order.customer,
        items: order.items,
        totalPrice: order.totalPrice,
        status: order.status,
        paymentStatus: order.paymentStatus,
        deliveryAddress: order.deliveryAddress,
        deliveryPartner: order.deliveryPartner,
        branch: order.branch,
        estimatedDeliveryTime: order.estimatedDeliveryTime,
        createdAt: order.createdAt
      }
    });
  } catch (error) {
    console.error('Get order error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get available branches for client app
app.get('/api/branches', async (req: Request, res: Response) => {
  try {
    const branches = await Branch.find({ status: 'active' })
      .populate('vendorId', 'name')
      .sort({ createdAt: -1 });

    const formattedBranches = branches.map(branch => ({
      _id: branch._id,
      name: branch.name,
      description: branch.description,
      address: branch.address,
      city: branch.city,
      state: branch.state,
      pincode: branch.pincode,
      phone: branch.phone,
      location: branch.location,
      vendor: branch.vendorId
    }));

    res.json({
      success: true,
      branches: formattedBranches
    });
  } catch (error) {
    console.error('Get branches error:', error);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Start server
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  createDefaultAdmin();
  createDemoCustomers();
  createDemoConversations();
  createDemoData();
});

export {};
