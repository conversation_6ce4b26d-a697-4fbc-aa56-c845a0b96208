"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeliveryPartner = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const deliveryPartnerSchema = new mongoose_1.default.Schema({
    name: {
        type: String,
        required: true
    },
    email: {
        type: String,
        required: true
    },
    phone: {
        type: String,
        required: true
    },
    address: {
        type: String,
        required: true
    },
    governmentId: {
        type: String,
        required: true
    },
    drivingLicense: {
        type: String,
        required: true
    },
    vehicleNumber: {
        type: String,
        required: true
    },
    vehicleType: {
        type: String,
        enum: ['2-wheeler', '4-wheeler'],
        required: true
    },
    area: {
        type: String,
        required: true
    },
    status: {
        type: String,
        enum: ['active', 'inactive'],
        default: 'active'
    },
    photoUrl: {
        type: String
    },
    deliveryCount: {
        type: Number,
        default: 0
    },
    vendorId: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: 'Vendor'
    },
    branchId: {
        type: mongoose_1.default.Schema.Types.ObjectId,
        ref: 'Branch'
    },
    createdAt: {
        type: Date,
        default: Date.now
    }
});
exports.DeliveryPartner = mongoose_1.default.model('DeliveryPartner', deliveryPartnerSchema);
exports.default = exports.DeliveryPartner;
