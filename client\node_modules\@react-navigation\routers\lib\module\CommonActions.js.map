{"version": 3, "names": ["goBack", "type", "navigate", "args", "name", "params", "merge", "payload", "Error", "navigateDeprecated", "reset", "state", "setParams", "preload"], "sourceRoot": "../../src", "sources": ["CommonActions.tsx"], "mappings": ";;AA0DA,OAAO,SAASA,MAAMA,CAAA,EAAW;EAC/B,OAAO;IAAEC,IAAI,EAAE;EAAU,CAAC;AAC5B;AAeA,OAAO,SAASC,QAAQA,CAAC,GAAGC,IAAS,EAAU;EAC7C,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IAC/B,MAAM,CAACC,IAAI,EAAEC,MAAM,EAAEC,KAAK,CAAC,GAAGH,IAAI;IAElC,OAAO;MACLF,IAAI,EAAE,UAAU;MAChBM,OAAO,EAAE;QAAEH,IAAI;QAAEC,MAAM;QAAEC;MAAM;IACjC,CAAC;EACH,CAAC,MAAM;IACL,MAAMC,OAAO,GAAGJ,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAE7B,IAAI,EAAE,MAAM,IAAII,OAAO,CAAC,EAAE;MACxB,MAAM,IAAIC,KAAK,CACb,8JACF,CAAC;IACH;IAEA,OAAO;MAAEP,IAAI,EAAE,UAAU;MAAEM;IAAQ,CAAC;EACtC;AACF;AAEA,OAAO,SAASE,kBAAkBA,CAChC,GAAGN,IAG6C,EACxC;EACR,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IAC/B,OAAO;MACLF,IAAI,EAAE,qBAAqB;MAC3BM,OAAO,EAAE;QAAEH,IAAI,EAAED,IAAI,CAAC,CAAC,CAAC;QAAEE,MAAM,EAAEF,IAAI,CAAC,CAAC;MAAE;IAC5C,CAAC;EACH,CAAC,MAAM;IACL,MAAMI,OAAO,GAAGJ,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAE7B,IAAI,EAAE,MAAM,IAAII,OAAO,CAAC,EAAE;MACxB,MAAM,IAAIC,KAAK,CACb,oKACF,CAAC;IACH;IAEA,OAAO;MAAEP,IAAI,EAAE,qBAAqB;MAAEM;IAAQ,CAAC;EACjD;AACF;AAEA,OAAO,SAASG,KAAKA,CAACC,KAA6B,EAAU;EAC3D,OAAO;IAAEV,IAAI,EAAE,OAAO;IAAEM,OAAO,EAAEI;EAAM,CAAC;AAC1C;AAEA,OAAO,SAASC,SAASA,CAACP,MAAc,EAAU;EAChD,OAAO;IAAEJ,IAAI,EAAE,YAAY;IAAEM,OAAO,EAAE;MAAEF;IAAO;EAAE,CAAC;AACpD;AAEA,OAAO,SAASQ,OAAOA,CAACT,IAAY,EAAEC,MAAe,EAAU;EAC7D,OAAO;IAAEJ,IAAI,EAAE,SAAS;IAAEM,OAAO,EAAE;MAAEH,IAAI;MAAEC;IAAO;EAAE,CAAC;AACvD", "ignoreList": []}