{"version": 3, "file": "index.js", "sources": ["../src/defaultTemplate.ts", "../src/variables.ts", "../src/index.ts"], "sourcesContent": ["import type { Template } from './types'\n\nexport const defaultTemplate: Template = (variables, { tpl }) => {\n  return tpl`\n${variables.imports};\n\n${variables.interfaces};\n\nconst ${variables.componentName} = (${variables.props}) => (\n  ${variables.jsx}\n);\n \n${variables.exports};\n`\n}\n", "import { types as t, template } from '@babel/core'\nimport type { Options, TemplateVariables, JSXRuntimeImport } from './types'\nimport type { ImportDeclaration } from '@babel/types'\n\nconst tsOptionalPropertySignature = (\n  ...args: Parameters<typeof t.tsPropertySignature>\n) => {\n  return {\n    ...t.tsPropertySignature(...args),\n    optional: true,\n  } as t.TSPropertySignature\n}\n\ninterface Context {\n  opts: Options\n  interfaces: t.TSInterfaceDeclaration[]\n  props: (t.Identifier | t.ObjectPattern)[]\n  imports: t.ImportDeclaration[]\n  importSource: string\n}\n\nconst getOrCreateImport = (\n  { imports }: Context,\n  sourceValue: string,\n  importKind: ImportDeclaration['importKind'] = undefined,\n) => {\n  const existing = imports.find(\n    (imp) =>\n      imp.source.value === sourceValue &&\n      imp.importKind === importKind &&\n      !imp.specifiers.some(\n        (specifier) => specifier.type === 'ImportNamespaceSpecifier',\n      ),\n  )\n  if (existing) return existing\n  const imp = t.importDeclaration([], t.stringLiteral(sourceValue))\n  if (importKind !== undefined) {\n    imp.importKind = importKind\n  }\n  imports.push(imp)\n  return imp\n}\n\nconst tsTypeReferenceSVGProps = (ctx: Context) => {\n  if (ctx.opts.native) {\n    const identifier = t.identifier('SvgProps')\n    getOrCreateImport(ctx, 'react-native-svg', 'type').specifiers.push(\n      t.importSpecifier(identifier, identifier),\n    )\n    return t.tsTypeReference(identifier)\n  }\n  const identifier = t.identifier('SVGProps')\n  getOrCreateImport(ctx, ctx.importSource, 'type').specifiers.push(\n    t.importSpecifier(identifier, identifier),\n  )\n  return t.tsTypeReference(\n    identifier,\n    t.tsTypeParameterInstantiation([\n      t.tsTypeReference(t.identifier('SVGSVGElement')),\n    ]),\n  )\n}\n\nconst tsTypeReferenceSVGRef = (ctx: Context) => {\n  const identifier = t.identifier('Ref')\n  getOrCreateImport(ctx, ctx.importSource).specifiers.push(\n    t.importSpecifier(identifier, identifier),\n  )\n  return t.tsTypeReference(\n    identifier,\n    t.tsTypeParameterInstantiation([\n      t.tsTypeReference(t.identifier('SVGSVGElement')),\n    ]),\n  )\n}\n\nconst getJsxRuntimeImport = (cfg: JSXRuntimeImport) => {\n  const specifiers = (() => {\n    if (cfg.namespace)\n      return [t.importNamespaceSpecifier(t.identifier(cfg.namespace))]\n    if (cfg.defaultSpecifier) {\n      const identifier = t.identifier(cfg.defaultSpecifier)\n      return [t.importDefaultSpecifier(identifier)]\n    }\n    if (cfg.specifiers)\n      return cfg.specifiers.map((specifier) => {\n        const identifier = t.identifier(specifier)\n        return t.importSpecifier(identifier, identifier)\n      })\n    throw new Error(\n      `Specify \"namespace\", \"defaultSpecifier\", or \"specifiers\" in \"jsxRuntimeImport\" option`,\n    )\n  })()\n  return t.importDeclaration(specifiers, t.stringLiteral(cfg.source))\n}\n\nconst defaultJsxRuntimeImport: JSXRuntimeImport = {\n  source: 'react',\n  namespace: 'React',\n}\n\nconst defaultImportSource = 'react'\n\nexport const getVariables = ({\n  opts,\n  jsx,\n}: {\n  opts: Options\n  jsx: t.JSXElement\n}): TemplateVariables => {\n  const interfaces: t.TSInterfaceDeclaration[] = []\n  const props: (t.Identifier | t.ObjectPattern)[] = []\n  const imports: t.ImportDeclaration[] = []\n  const exports: (t.VariableDeclaration | t.ExportDeclaration | t.Statement)[] =\n    []\n  const ctx = {\n    importSource: opts.importSource ?? defaultImportSource,\n    exportIdentifier: t.identifier(opts.state.componentName),\n    opts,\n    interfaces,\n    props,\n    imports,\n    exports,\n  }\n\n  if (opts.jsxRuntime !== 'automatic') {\n    imports.push(\n      getJsxRuntimeImport(opts.jsxRuntimeImport ?? defaultJsxRuntimeImport),\n    )\n  }\n\n  if (opts.native) {\n    getOrCreateImport(ctx, 'react-native-svg').specifiers.push(\n      t.importDefaultSpecifier(t.identifier('Svg')),\n    )\n  }\n\n  if (opts.titleProp || opts.descProp) {\n    const properties = []\n    const propertySignatures = []\n    const createProperty = (attr: string) => {\n      return t.objectProperty(\n        t.identifier(attr),\n        t.identifier(attr),\n        false,\n        true,\n      )\n    }\n    const createSignature = (attr: string) => {\n      return tsOptionalPropertySignature(\n        t.identifier(attr),\n        t.tsTypeAnnotation(t.tsStringKeyword()),\n      )\n    }\n\n    if (opts.titleProp) {\n      properties.push(createProperty('title'), createProperty('titleId'))\n\n      if (opts.typescript) {\n        propertySignatures.push(\n          createSignature('title'),\n          createSignature('titleId'),\n        )\n      }\n    }\n\n    if (opts.descProp) {\n      properties.push(createProperty('desc'), createProperty('descId'))\n\n      if (opts.typescript) {\n        propertySignatures.push(\n          createSignature('desc'),\n          createSignature('descId'),\n        )\n      }\n    }\n\n    const prop = t.objectPattern(properties)\n    props.push(prop)\n    if (opts.typescript) {\n      interfaces.push(\n        t.tsInterfaceDeclaration(\n          t.identifier('SVGRProps'),\n          null,\n          null,\n          t.tSInterfaceBody(propertySignatures),\n        ),\n      )\n      prop.typeAnnotation = t.tsTypeAnnotation(\n        t.tsTypeReference(t.identifier('SVGRProps')),\n      )\n    }\n  }\n\n  if (opts.expandProps) {\n    const identifier = t.identifier('props')\n    if (t.isObjectPattern(props[0])) {\n      props[0].properties.push(t.restElement(identifier))\n      if (opts.typescript) {\n        props[0].typeAnnotation = t.tsTypeAnnotation(\n          t.tsIntersectionType([\n            tsTypeReferenceSVGProps(ctx),\n            (props[0].typeAnnotation as t.TSTypeAnnotation).typeAnnotation,\n          ]),\n        )\n      }\n    } else {\n      props.push(identifier)\n      if (opts.typescript) {\n        identifier.typeAnnotation = t.tsTypeAnnotation(\n          tsTypeReferenceSVGProps(ctx),\n        )\n      }\n    }\n  }\n\n  if (opts.ref) {\n    if (props.length === 0) {\n      props.push(t.identifier('_'))\n    }\n    const prop = t.identifier('ref')\n    props.push(prop)\n    if (opts.typescript) {\n      prop.typeAnnotation = t.tsTypeAnnotation(tsTypeReferenceSVGRef(ctx))\n    }\n    const forwardRef = t.identifier('forwardRef')\n    const ForwardRef = t.identifier('ForwardRef')\n    getOrCreateImport(ctx, ctx.importSource).specifiers.push(\n      t.importSpecifier(forwardRef, forwardRef),\n    )\n    exports.push(\n      t.variableDeclaration('const', [\n        t.variableDeclarator(\n          ForwardRef,\n          t.callExpression(forwardRef, [ctx.exportIdentifier]),\n        ),\n      ]),\n    )\n    ctx.exportIdentifier = ForwardRef\n  }\n\n  if (opts.memo) {\n    const memo = t.identifier('memo')\n    const Memo = t.identifier('Memo')\n    getOrCreateImport(ctx, ctx.importSource).specifiers.push(\n      t.importSpecifier(memo, memo),\n    )\n    exports.push(\n      t.variableDeclaration('const', [\n        t.variableDeclarator(\n          Memo,\n          t.callExpression(memo, [ctx.exportIdentifier]),\n        ),\n      ]),\n    )\n    ctx.exportIdentifier = Memo\n  }\n\n  if (opts.state.caller?.previousExport || opts.exportType === 'named') {\n    if (!opts.namedExport) {\n      throw new Error(`\"namedExport\" not specified`)\n    }\n    exports.push(\n      t.exportNamedDeclaration(null, [\n        t.exportSpecifier(ctx.exportIdentifier, t.identifier(opts.namedExport)),\n      ]),\n    )\n    if (opts.state.caller?.previousExport) {\n      const previousExportAst = template.ast(opts.state.caller.previousExport)\n      exports.push(\n        ...(Array.isArray(previousExportAst)\n          ? previousExportAst\n          : [previousExportAst]),\n      )\n    }\n  } else {\n    exports.push(t.exportDefaultDeclaration(ctx.exportIdentifier))\n  }\n  return {\n    componentName: opts.state.componentName,\n    props,\n    interfaces,\n    imports,\n    exports,\n    jsx,\n  }\n}\n", "/* eslint-disable @typescript-eslint/explicit-module-boundary-types */\nimport {\n  ConfigAPI,\n  NodePath,\n  types as t,\n  template as babelTemplate,\n  ParserOptions,\n} from '@babel/core'\nimport type { Options } from './types'\nimport { defaultTemplate } from './defaultTemplate'\nimport { getVariables } from './variables'\n\nexport type { Options, Template } from './types'\n\nconst plugin = (_: ConfigAPI, opts: Options) => {\n  const template = opts.template || defaultTemplate\n  const plugins: ParserOptions['plugins'] = opts.typescript\n    ? ['jsx', 'typescript']\n    : ['jsx']\n  const tpl = babelTemplate.smart({ plugins, preserveComments: true }).ast\n  return {\n    visitor: {\n      Program(path: NodePath<t.Program>) {\n        const jsx = (path.node.body[0] as t.ExpressionStatement)\n          .expression as t.JSXElement\n        const variables = getVariables({\n          opts,\n          jsx,\n        })\n        const body = template(variables, { options: opts, tpl })\n        path.node.body = Array.isArray(body) ? body : [body]\n        path.replaceWith(path.node)\n      },\n    },\n  }\n}\n\nexport default plugin\n"], "names": ["t", "imp", "identifier", "template", "babelTemplate"], "mappings": ";;;;AAEO,MAAM,eAA4B,GAAA,CAAC,SAAW,EAAA,EAAE,KAAU,KAAA;AAC/D,EAAO,OAAA,GAAA,CAAA;AAAA,EACP,SAAU,CAAA,OAAA,CAAA;AAAA;AAAA,EAEV,SAAU,CAAA,UAAA,CAAA;AAAA;AAAA,MAEJ,EAAA,SAAA,CAAU,oBAAoB,SAAU,CAAA,KAAA,CAAA;AAAA,EAAA,EAC5C,SAAU,CAAA,GAAA,CAAA;AAAA;AAAA;AAAA,EAGZ,SAAU,CAAA,OAAA,CAAA;AAAA,CAAA,CAAA;AAEZ,CAAA;;;;;;;;;;;;;;;;;;;;;ACVA,MAAM,2BAAA,GAA8B,IAC/B,IACA,KAAA;AACH,EAAA,OAAO,aACF,CAAA,cAAA,CAAA,EAAA,EAAAA,UAAA,CAAE,mBAAoB,CAAA,GAAG,IAAI,CAD3B,CAAA,EAAA;AAAA,IAEL,QAAU,EAAA,IAAA;AAAA,GACZ,CAAA,CAAA;AACF,CAAA,CAAA;AAUA,MAAM,oBAAoB,CACxB,EAAE,SACF,EAAA,WAAA,EACA,aAA8C,KAC3C,CAAA,KAAA;AACH,EAAA,MAAM,WAAW,OAAQ,CAAA,IAAA;AAAA,IACvB,CAACC,IACCA,KAAAA,IAAAA,CAAI,MAAO,CAAA,KAAA,KAAU,WACrBA,IAAAA,IAAAA,CAAI,UAAe,KAAA,UAAA,IACnB,CAACA,IAAAA,CAAI,UAAW,CAAA,IAAA;AAAA,MACd,CAAC,SAAc,KAAA,SAAA,CAAU,IAAS,KAAA,0BAAA;AAAA,KACpC;AAAA,GACJ,CAAA;AACA,EAAI,IAAA,QAAA;AAAU,IAAO,OAAA,QAAA,CAAA;AACrB,EAAM,MAAA,GAAA,GAAMD,WAAE,iBAAkB,CAAA,IAAIA,UAAE,CAAA,aAAA,CAAc,WAAW,CAAC,CAAA,CAAA;AAChE,EAAA,IAAI,eAAe,KAAW,CAAA,EAAA;AAC5B,IAAA,GAAA,CAAI,UAAa,GAAA,UAAA,CAAA;AAAA,GACnB;AACA,EAAA,OAAA,CAAQ,KAAK,GAAG,CAAA,CAAA;AAChB,EAAO,OAAA,GAAA,CAAA;AACT,CAAA,CAAA;AAEA,MAAM,uBAAA,GAA0B,CAAC,GAAiB,KAAA;AAChD,EAAI,IAAA,GAAA,CAAI,KAAK,MAAQ,EAAA;AACnB,IAAME,MAAAA,WAAAA,GAAaF,UAAE,CAAA,UAAA,CAAW,UAAU,CAAA,CAAA;AAC1C,IAAA,iBAAA,CAAkB,GAAK,EAAA,kBAAA,EAAoB,MAAM,CAAA,CAAE,UAAW,CAAA,IAAA;AAAA,MAC5DA,UAAA,CAAE,eAAgBE,CAAAA,WAAAA,EAAYA,WAAU,CAAA;AAAA,KAC1C,CAAA;AACA,IAAO,OAAAF,UAAA,CAAE,gBAAgBE,WAAU,CAAA,CAAA;AAAA,GACrC;AACA,EAAM,MAAA,UAAA,GAAaF,UAAE,CAAA,UAAA,CAAW,UAAU,CAAA,CAAA;AAC1C,EAAA,iBAAA,CAAkB,GAAK,EAAA,GAAA,CAAI,YAAc,EAAA,MAAM,EAAE,UAAW,CAAA,IAAA;AAAA,IAC1DA,UAAA,CAAE,eAAgB,CAAA,UAAA,EAAY,UAAU,CAAA;AAAA,GAC1C,CAAA;AACA,EAAA,OAAOA,UAAE,CAAA,eAAA;AAAA,IACP,UAAA;AAAA,IACAA,WAAE,4BAA6B,CAAA;AAAA,MAC7BA,UAAE,CAAA,eAAA,CAAgBA,UAAE,CAAA,UAAA,CAAW,eAAe,CAAC,CAAA;AAAA,KAChD,CAAA;AAAA,GACH,CAAA;AACF,CAAA,CAAA;AAEA,MAAM,qBAAA,GAAwB,CAAC,GAAiB,KAAA;AAC9C,EAAM,MAAA,UAAA,GAAaA,UAAE,CAAA,UAAA,CAAW,KAAK,CAAA,CAAA;AACrC,EAAA,iBAAA,CAAkB,GAAK,EAAA,GAAA,CAAI,YAAY,CAAA,CAAE,UAAW,CAAA,IAAA;AAAA,IAClDA,UAAA,CAAE,eAAgB,CAAA,UAAA,EAAY,UAAU,CAAA;AAAA,GAC1C,CAAA;AACA,EAAA,OAAOA,UAAE,CAAA,eAAA;AAAA,IACP,UAAA;AAAA,IACAA,WAAE,4BAA6B,CAAA;AAAA,MAC7BA,UAAE,CAAA,eAAA,CAAgBA,UAAE,CAAA,UAAA,CAAW,eAAe,CAAC,CAAA;AAAA,KAChD,CAAA;AAAA,GACH,CAAA;AACF,CAAA,CAAA;AAEA,MAAM,mBAAA,GAAsB,CAAC,GAA0B,KAAA;AACrD,EAAA,MAAM,cAAc,MAAM;AACxB,IAAA,IAAI,GAAI,CAAA,SAAA;AACN,MAAO,OAAA,CAACA,WAAE,wBAAyB,CAAAA,UAAA,CAAE,WAAW,GAAI,CAAA,SAAS,CAAC,CAAC,CAAA,CAAA;AACjE,IAAA,IAAI,IAAI,gBAAkB,EAAA;AACxB,MAAA,MAAM,UAAa,GAAAA,UAAA,CAAE,UAAW,CAAA,GAAA,CAAI,gBAAgB,CAAA,CAAA;AACpD,MAAA,OAAO,CAACA,UAAA,CAAE,sBAAuB,CAAA,UAAU,CAAC,CAAA,CAAA;AAAA,KAC9C;AACA,IAAA,IAAI,GAAI,CAAA,UAAA;AACN,MAAA,OAAO,GAAI,CAAA,UAAA,CAAW,GAAI,CAAA,CAAC,SAAc,KAAA;AACvC,QAAM,MAAA,UAAA,GAAaA,UAAE,CAAA,UAAA,CAAW,SAAS,CAAA,CAAA;AACzC,QAAO,OAAAA,UAAA,CAAE,eAAgB,CAAA,UAAA,EAAY,UAAU,CAAA,CAAA;AAAA,OAChD,CAAA,CAAA;AACH,IAAA,MAAM,IAAI,KAAA;AAAA,MACR,CAAA,qFAAA,CAAA;AAAA,KACF,CAAA;AAAA,GACC,GAAA,CAAA;AACH,EAAA,OAAOA,WAAE,iBAAkB,CAAA,UAAA,EAAYA,WAAE,aAAc,CAAA,GAAA,CAAI,MAAM,CAAC,CAAA,CAAA;AACpE,CAAA,CAAA;AAEA,MAAM,uBAA4C,GAAA;AAAA,EAChD,MAAQ,EAAA,OAAA;AAAA,EACR,SAAW,EAAA,OAAA;AACb,CAAA,CAAA;AAEA,MAAM,mBAAsB,GAAA,OAAA,CAAA;AAErB,MAAM,eAAe,CAAC;AAAA,EAC3B,IAAA;AAAA,EACA,GAAA;AACF,CAGyB,KAAA;AA7GzB,EAAA,IAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAAA;AA8GE,EAAA,MAAM,aAAyC,EAAC,CAAA;AAChD,EAAA,MAAM,QAA4C,EAAC,CAAA;AACnD,EAAA,MAAM,UAAiC,EAAC,CAAA;AACxC,EAAA,MAAM,UACJ,EAAC,CAAA;AACH,EAAA,MAAM,GAAM,GAAA;AAAA,IACV,YAAA,EAAA,CAAc,EAAK,GAAA,IAAA,CAAA,YAAA,KAAL,IAAqB,GAAA,EAAA,GAAA,mBAAA;AAAA,IACnC,gBAAkB,EAAAA,UAAA,CAAE,UAAW,CAAA,IAAA,CAAK,MAAM,aAAa,CAAA;AAAA,IACvD,IAAA;AAAA,IACA,UAAA;AAAA,IACA,KAAA;AAAA,IACA,OAAA;AAAA,IACA,OAAA;AAAA,GACF,CAAA;AAEA,EAAI,IAAA,IAAA,CAAK,eAAe,WAAa,EAAA;AACnC,IAAQ,OAAA,CAAA,IAAA;AAAA,MACN,mBAAoB,CAAA,CAAA,EAAA,GAAA,IAAA,CAAK,gBAAL,KAAA,IAAA,GAAA,EAAA,GAAyB,uBAAuB,CAAA;AAAA,KACtE,CAAA;AAAA,GACF;AAEA,EAAA,IAAI,KAAK,MAAQ,EAAA;AACf,IAAkB,iBAAA,CAAA,GAAA,EAAK,kBAAkB,CAAA,CAAE,UAAW,CAAA,IAAA;AAAA,MACpDA,UAAE,CAAA,sBAAA,CAAuBA,UAAE,CAAA,UAAA,CAAW,KAAK,CAAC,CAAA;AAAA,KAC9C,CAAA;AAAA,GACF;AAEA,EAAI,IAAA,IAAA,CAAK,SAAa,IAAA,IAAA,CAAK,QAAU,EAAA;AACnC,IAAA,MAAM,aAAa,EAAC,CAAA;AACpB,IAAA,MAAM,qBAAqB,EAAC,CAAA;AAC5B,IAAM,MAAA,cAAA,GAAiB,CAAC,IAAiB,KAAA;AACvC,MAAA,OAAOA,UAAE,CAAA,cAAA;AAAA,QACPA,UAAA,CAAE,WAAW,IAAI,CAAA;AAAA,QACjBA,UAAA,CAAE,WAAW,IAAI,CAAA;AAAA,QACjB,KAAA;AAAA,QACA,IAAA;AAAA,OACF,CAAA;AAAA,KACF,CAAA;AACA,IAAM,MAAA,eAAA,GAAkB,CAAC,IAAiB,KAAA;AACxC,MAAO,OAAA,2BAAA;AAAA,QACLA,UAAA,CAAE,WAAW,IAAI,CAAA;AAAA,QACjBA,UAAE,CAAA,gBAAA,CAAiBA,UAAE,CAAA,eAAA,EAAiB,CAAA;AAAA,OACxC,CAAA;AAAA,KACF,CAAA;AAEA,IAAA,IAAI,KAAK,SAAW,EAAA;AAClB,MAAA,UAAA,CAAW,KAAK,cAAe,CAAA,OAAO,CAAG,EAAA,cAAA,CAAe,SAAS,CAAC,CAAA,CAAA;AAElE,MAAA,IAAI,KAAK,UAAY,EAAA;AACnB,QAAmB,kBAAA,CAAA,IAAA;AAAA,UACjB,gBAAgB,OAAO,CAAA;AAAA,UACvB,gBAAgB,SAAS,CAAA;AAAA,SAC3B,CAAA;AAAA,OACF;AAAA,KACF;AAEA,IAAA,IAAI,KAAK,QAAU,EAAA;AACjB,MAAA,UAAA,CAAW,KAAK,cAAe,CAAA,MAAM,CAAG,EAAA,cAAA,CAAe,QAAQ,CAAC,CAAA,CAAA;AAEhE,MAAA,IAAI,KAAK,UAAY,EAAA;AACnB,QAAmB,kBAAA,CAAA,IAAA;AAAA,UACjB,gBAAgB,MAAM,CAAA;AAAA,UACtB,gBAAgB,QAAQ,CAAA;AAAA,SAC1B,CAAA;AAAA,OACF;AAAA,KACF;AAEA,IAAM,MAAA,IAAA,GAAOA,UAAE,CAAA,aAAA,CAAc,UAAU,CAAA,CAAA;AACvC,IAAA,KAAA,CAAM,KAAK,IAAI,CAAA,CAAA;AACf,IAAA,IAAI,KAAK,UAAY,EAAA;AACnB,MAAW,UAAA,CAAA,IAAA;AAAA,QACTA,UAAE,CAAA,sBAAA;AAAA,UACAA,UAAA,CAAE,WAAW,WAAW,CAAA;AAAA,UACxB,IAAA;AAAA,UACA,IAAA;AAAA,UACAA,UAAA,CAAE,gBAAgB,kBAAkB,CAAA;AAAA,SACtC;AAAA,OACF,CAAA;AACA,MAAA,IAAA,CAAK,iBAAiBA,UAAE,CAAA,gBAAA;AAAA,QACtBA,UAAE,CAAA,eAAA,CAAgBA,UAAE,CAAA,UAAA,CAAW,WAAW,CAAC,CAAA;AAAA,OAC7C,CAAA;AAAA,KACF;AAAA,GACF;AAEA,EAAA,IAAI,KAAK,WAAa,EAAA;AACpB,IAAM,MAAA,UAAA,GAAaA,UAAE,CAAA,UAAA,CAAW,OAAO,CAAA,CAAA;AACvC,IAAA,IAAIA,UAAE,CAAA,eAAA,CAAgB,KAAM,CAAA,CAAC,CAAC,CAAG,EAAA;AAC/B,MAAA,KAAA,CAAM,CAAC,CAAE,CAAA,UAAA,CAAW,KAAKA,UAAE,CAAA,WAAA,CAAY,UAAU,CAAC,CAAA,CAAA;AAClD,MAAA,IAAI,KAAK,UAAY,EAAA;AACnB,QAAM,KAAA,CAAA,CAAC,CAAE,CAAA,cAAA,GAAiBA,UAAE,CAAA,gBAAA;AAAA,UAC1BA,WAAE,kBAAmB,CAAA;AAAA,YACnB,wBAAwB,GAAG,CAAA;AAAA,YAC1B,KAAA,CAAM,CAAC,CAAA,CAAE,cAAsC,CAAA,cAAA;AAAA,WACjD,CAAA;AAAA,SACH,CAAA;AAAA,OACF;AAAA,KACK,MAAA;AACL,MAAA,KAAA,CAAM,KAAK,UAAU,CAAA,CAAA;AACrB,MAAA,IAAI,KAAK,UAAY,EAAA;AACnB,QAAA,UAAA,CAAW,iBAAiBA,UAAE,CAAA,gBAAA;AAAA,UAC5B,wBAAwB,GAAG,CAAA;AAAA,SAC7B,CAAA;AAAA,OACF;AAAA,KACF;AAAA,GACF;AAEA,EAAA,IAAI,KAAK,GAAK,EAAA;AACZ,IAAI,IAAA,KAAA,CAAM,WAAW,CAAG,EAAA;AACtB,MAAA,KAAA,CAAM,IAAK,CAAAA,UAAA,CAAE,UAAW,CAAA,GAAG,CAAC,CAAA,CAAA;AAAA,KAC9B;AACA,IAAM,MAAA,IAAA,GAAOA,UAAE,CAAA,UAAA,CAAW,KAAK,CAAA,CAAA;AAC/B,IAAA,KAAA,CAAM,KAAK,IAAI,CAAA,CAAA;AACf,IAAA,IAAI,KAAK,UAAY,EAAA;AACnB,MAAA,IAAA,CAAK,cAAiB,GAAAA,UAAA,CAAE,gBAAiB,CAAA,qBAAA,CAAsB,GAAG,CAAC,CAAA,CAAA;AAAA,KACrE;AACA,IAAM,MAAA,UAAA,GAAaA,UAAE,CAAA,UAAA,CAAW,YAAY,CAAA,CAAA;AAC5C,IAAM,MAAA,UAAA,GAAaA,UAAE,CAAA,UAAA,CAAW,YAAY,CAAA,CAAA;AAC5C,IAAA,iBAAA,CAAkB,GAAK,EAAA,GAAA,CAAI,YAAY,CAAA,CAAE,UAAW,CAAA,IAAA;AAAA,MAClDA,UAAA,CAAE,eAAgB,CAAA,UAAA,EAAY,UAAU,CAAA;AAAA,KAC1C,CAAA;AACA,IAAQ,OAAA,CAAA,IAAA;AAAA,MACNA,UAAA,CAAE,oBAAoB,OAAS,EAAA;AAAA,QAC7BA,UAAE,CAAA,kBAAA;AAAA,UACA,UAAA;AAAA,UACAA,WAAE,cAAe,CAAA,UAAA,EAAY,CAAC,GAAA,CAAI,gBAAgB,CAAC,CAAA;AAAA,SACrD;AAAA,OACD,CAAA;AAAA,KACH,CAAA;AACA,IAAA,GAAA,CAAI,gBAAmB,GAAA,UAAA,CAAA;AAAA,GACzB;AAEA,EAAA,IAAI,KAAK,IAAM,EAAA;AACb,IAAM,MAAA,IAAA,GAAOA,UAAE,CAAA,UAAA,CAAW,MAAM,CAAA,CAAA;AAChC,IAAM,MAAA,IAAA,GAAOA,UAAE,CAAA,UAAA,CAAW,MAAM,CAAA,CAAA;AAChC,IAAA,iBAAA,CAAkB,GAAK,EAAA,GAAA,CAAI,YAAY,CAAA,CAAE,UAAW,CAAA,IAAA;AAAA,MAClDA,UAAA,CAAE,eAAgB,CAAA,IAAA,EAAM,IAAI,CAAA;AAAA,KAC9B,CAAA;AACA,IAAQ,OAAA,CAAA,IAAA;AAAA,MACNA,UAAA,CAAE,oBAAoB,OAAS,EAAA;AAAA,QAC7BA,UAAE,CAAA,kBAAA;AAAA,UACA,IAAA;AAAA,UACAA,WAAE,cAAe,CAAA,IAAA,EAAM,CAAC,GAAA,CAAI,gBAAgB,CAAC,CAAA;AAAA,SAC/C;AAAA,OACD,CAAA;AAAA,KACH,CAAA;AACA,IAAA,GAAA,CAAI,gBAAmB,GAAA,IAAA,CAAA;AAAA,GACzB;AAEA,EAAA,IAAA,CAAA,CAAI,UAAK,KAAM,CAAA,MAAA,KAAX,mBAAmB,cAAkB,KAAA,IAAA,CAAK,eAAe,OAAS,EAAA;AACpE,IAAI,IAAA,CAAC,KAAK,WAAa,EAAA;AACrB,MAAM,MAAA,IAAI,MAAM,CAA6B,2BAAA,CAAA,CAAA,CAAA;AAAA,KAC/C;AACA,IAAQ,OAAA,CAAA,IAAA;AAAA,MACNA,UAAA,CAAE,uBAAuB,IAAM,EAAA;AAAA,QAC7BA,UAAA,CAAE,gBAAgB,GAAI,CAAA,gBAAA,EAAkBA,WAAE,UAAW,CAAA,IAAA,CAAK,WAAW,CAAC,CAAA;AAAA,OACvE,CAAA;AAAA,KACH,CAAA;AACA,IAAA,IAAA,CAAI,EAAK,GAAA,IAAA,CAAA,KAAA,CAAM,MAAX,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAmB,cAAgB,EAAA;AACrC,MAAA,MAAM,oBAAoBG,aAAS,CAAA,GAAA,CAAI,IAAK,CAAA,KAAA,CAAM,OAAO,cAAc,CAAA,CAAA;AACvE,MAAQ,OAAA,CAAA,IAAA;AAAA,QACN,GAAI,KAAM,CAAA,OAAA,CAAQ,iBAAiB,CAC/B,GAAA,iBAAA,GACA,CAAC,iBAAiB,CAAA;AAAA,OACxB,CAAA;AAAA,KACF;AAAA,GACK,MAAA;AACL,IAAA,OAAA,CAAQ,IAAK,CAAAH,UAAA,CAAE,wBAAyB,CAAA,GAAA,CAAI,gBAAgB,CAAC,CAAA,CAAA;AAAA,GAC/D;AACA,EAAO,OAAA;AAAA,IACL,aAAA,EAAe,KAAK,KAAM,CAAA,aAAA;AAAA,IAC1B,KAAA;AAAA,IACA,UAAA;AAAA,IACA,OAAA;AAAA,IACA,OAAA;AAAA,IACA,GAAA;AAAA,GACF,CAAA;AACF,CAAA;;AChRM,MAAA,MAAA,GAAS,CAAC,CAAA,EAAc,IAAkB,KAAA;AAC9C,EAAM,MAAA,QAAA,GAAW,KAAK,QAAY,IAAA,eAAA,CAAA;AAClC,EAAM,MAAA,OAAA,GAAoC,KAAK,UAC3C,GAAA,CAAC,OAAO,YAAY,CAAA,GACpB,CAAC,KAAK,CAAA,CAAA;AACV,EAAM,MAAA,GAAA,GAAMI,cAAc,KAAM,CAAA,EAAE,SAAS,gBAAkB,EAAA,IAAA,EAAM,CAAE,CAAA,GAAA,CAAA;AACrE,EAAO,OAAA;AAAA,IACL,OAAS,EAAA;AAAA,MACP,QAAQ,IAA2B,EAAA;AACjC,QAAA,MAAM,GAAO,GAAA,IAAA,CAAK,IAAK,CAAA,IAAA,CAAK,CAAC,CAC1B,CAAA,UAAA,CAAA;AACH,QAAA,MAAM,YAAY,YAAa,CAAA;AAAA,UAC7B,IAAA;AAAA,UACA,GAAA;AAAA,SACD,CAAA,CAAA;AACD,QAAA,MAAM,OAAO,QAAS,CAAA,SAAA,EAAW,EAAE,OAAS,EAAA,IAAA,EAAM,KAAK,CAAA,CAAA;AACvD,QAAK,IAAA,CAAA,IAAA,CAAK,OAAO,KAAM,CAAA,OAAA,CAAQ,IAAI,CAAI,GAAA,IAAA,GAAO,CAAC,IAAI,CAAA,CAAA;AACnD,QAAK,IAAA,CAAA,WAAA,CAAY,KAAK,IAAI,CAAA,CAAA;AAAA,OAC5B;AAAA,KACF;AAAA,GACF,CAAA;AACF;;;;"}