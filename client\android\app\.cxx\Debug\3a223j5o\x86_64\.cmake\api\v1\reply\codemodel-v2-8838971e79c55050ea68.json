{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 7, 8, 9, 10, 11], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "RNCGeolocationSpec_autolinked_build", "jsonFile": "directory-RNCGeolocationSpec_autolinked_build-Debug-414591809347d2dab198.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "lottiereactnative_autolinked_build", "jsonFile": "directory-lottiereactnative_autolinked_build-Debug-be3d2b9af6e415f14c05.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni", "targetIndexes": [6]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-ca0d53cc64a002b6c5bc.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [7]}, {"build": "RNMmkvSpec_autolinked_build", "jsonFile": "directory-RNMmkvSpec_autolinked_build-Debug-a9cfa7e8acb49442a210.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "RNMmkvSpec_cxxmodule_autolinked_build", "childIndexes": [6], "jsonFile": "directory-RNMmkvSpec_cxxmodule_autolinked_build-Debug-e5506ad0fe63807b0dce.json", "minimumCMakeVersion": {"string": "3.9.0"}, "parentIndex": 0, "projectIndex": 1, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-mmkv/android", "targetIndexes": [2]}, {"build": "RNMmkvSpec_cxxmodule_autolinked_build/core", "jsonFile": "directory-RNMmkvSpec_cxxmodule_autolinked_build.core-Debug-bd047ef9f7de469e3899.json", "minimumCMakeVersion": {"string": "3.10.0"}, "parentIndex": 5, "projectIndex": 2, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-mmkv/MMKV/Core", "targetIndexes": [1]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-Debug-d7ca2b8cccb0152fb5bd.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [8]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-b0bc2965c4b953c4102c.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [11]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-9ad1313a82eacc3dd4ca.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [9]}, {"build": "rnsvg_autolinked_build", "jsonFile": "directory-rnsvg_autolinked_build-Debug-eaaa337278a79fa65f60.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-svg/android/src/main/jni", "targetIndexes": [10]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-02d605895dacaed2e81c.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [5]}], "name": "Debug", "projects": [{"childIndexes": [1], "directoryIndexes": [0, 1, 2, 3, 4, 7, 8, 9, 10, 11], "name": "appmodules", "targetIndexes": [0, 3, 4, 5, 6, 7, 8, 9, 10, 11]}, {"childIndexes": [2], "directoryIndexes": [5], "name": "ReactNativeMmkv", "parentIndex": 0, "targetIndexes": [2]}, {"directoryIndexes": [6], "name": "core", "parentIndex": 1, "targetIndexes": [1]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-2e0ba66c9e895116775b.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 6, "id": "core::@1b9a7d546b295b7d0867", "jsonFile": "target-core-Debug-7bd2635f8cf69523e7e2.json", "name": "core", "projectIndex": 2}, {"directoryIndex": 5, "id": "react-native-mmkv::@4ae6a1e65d3e68ba0197", "jsonFile": "target-react-native-mmkv-Debug-f1a4118cfceb5b4322be.json", "name": "react-native-mmkv", "projectIndex": 1}, {"directoryIndex": 1, "id": "react_codegen_RNCGeolocationSpec::@1b959fcb56e23f7716ba", "jsonFile": "target-react_codegen_RNCGeolocationSpec-Debug-e8e4685a68de311aa0ff.json", "name": "react_codegen_RNCGeolocationSpec", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_RNMmkvSpec::@7541eabbae598da31a69", "jsonFile": "target-react_codegen_RNMmkvSpec-Debug-14f2e0131844cd7f7a75.json", "name": "react_codegen_RNMmkvSpec", "projectIndex": 0}, {"directoryIndex": 11, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-e85f60eaac66963fce4e.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_lottiereactnative::@0fa4dc904d7e359a99fb", "jsonFile": "target-react_codegen_lottiereactnative-Debug-4542a1ececebbe92078c.json", "name": "react_codegen_lottiereactnative", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-5ae19bba7c37e53e1c42.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-Debug-0dc7be2cac6f8b9aa39d.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 9, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-e2324a499483233b3969.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 10, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65", "jsonFile": "target-react_codegen_rnsvg-Debug-0de998d4361ad8c59219.json", "name": "react_codegen_rnsvg", "projectIndex": 0}, {"directoryIndex": 8, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-969cb0015d7abbc4a843.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/x86_64", "source": "D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}