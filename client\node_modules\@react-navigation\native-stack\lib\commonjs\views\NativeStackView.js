"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.NativeStackView = NativeStackView;
var _elements = require("@react-navigation/elements");
var _native = require("@react-navigation/native");
var React = _interopRequireWildcard(require("react"));
var _reactNative = require("react-native");
var _useAnimatedHeaderHeight = require("../utils/useAnimatedHeaderHeight.js");
var _jsxRuntime = require("react/jsx-runtime");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
const TRANSPARENT_PRESENTATIONS = ['transparentModal', 'containedTransparentModal'];
function NativeStackView({
  state,
  descriptors,
  describe
}) {
  const parentHeaderBack = React.useContext(_elements.HeaderBackContext);
  const {
    buildHref
  } = (0, _native.useLinkBuilder)();
  const preloadedDescriptors = state.preloadedRoutes.reduce((acc, route) => {
    acc[route.key] = acc[route.key] || describe(route, true);
    return acc;
  }, {});
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.SafeAreaProviderCompat, {
    children: state.routes.concat(state.preloadedRoutes).map((route, i) => {
      const isFocused = state.index === i;
      const previousKey = state.routes[i - 1]?.key;
      const nextKey = state.routes[i + 1]?.key;
      const previousDescriptor = previousKey ? descriptors[previousKey] : undefined;
      const nextDescriptor = nextKey ? descriptors[nextKey] : undefined;
      const {
        options,
        navigation,
        render
      } = descriptors[route.key] ?? preloadedDescriptors[route.key];
      const headerBack = previousDescriptor ? {
        title: (0, _elements.getHeaderTitle)(previousDescriptor.options, previousDescriptor.route.name),
        href: buildHref(previousDescriptor.route.name, previousDescriptor.route.params)
      } : parentHeaderBack;
      const canGoBack = headerBack != null;
      const {
        header,
        headerShown,
        headerBackImageSource,
        headerLeft,
        headerTransparent,
        headerBackTitle,
        presentation,
        contentStyle,
        ...rest
      } = options;
      const nextPresentation = nextDescriptor?.options.presentation;
      const isPreloaded = preloadedDescriptors[route.key] !== undefined && descriptors[route.key] === undefined;
      return /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.Screen, {
        focused: isFocused,
        route: route,
        navigation: navigation,
        headerShown: headerShown,
        headerTransparent: headerTransparent,
        header: header !== undefined ? header({
          back: headerBack,
          options,
          route,
          navigation
        }) : /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.Header, {
          ...rest,
          back: headerBack,
          title: (0, _elements.getHeaderTitle)(options, route.name),
          headerLeft: typeof headerLeft === 'function' ? ({
            label,
            ...rest
          }) => headerLeft({
            ...rest,
            label: headerBackTitle ?? label
          }) : headerLeft === undefined && canGoBack ? ({
            tintColor,
            label,
            ...rest
          }) => /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.HeaderBackButton, {
            ...rest,
            label: headerBackTitle ?? label,
            tintColor: tintColor,
            backImage: headerBackImageSource !== undefined ? () => /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.Image, {
              source: headerBackImageSource,
              resizeMode: "contain",
              tintColor: tintColor,
              style: styles.backImage
            }) : undefined,
            onPress: navigation.goBack
          }) : headerLeft,
          headerTransparent: headerTransparent
        }),
        style: [_reactNative.StyleSheet.absoluteFill, {
          display: (isFocused || nextPresentation != null && TRANSPARENT_PRESENTATIONS.includes(nextPresentation)) && !isPreloaded ? 'flex' : 'none'
        }, presentation != null && TRANSPARENT_PRESENTATIONS.includes(presentation) ? {
          backgroundColor: 'transparent'
        } : null],
        children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_elements.HeaderBackContext.Provider, {
          value: headerBack,
          children: /*#__PURE__*/(0, _jsxRuntime.jsx)(AnimatedHeaderHeightProvider, {
            children: /*#__PURE__*/(0, _jsxRuntime.jsx)(_reactNative.View, {
              style: [styles.contentContainer, contentStyle],
              children: render()
            })
          })
        })
      }, route.key);
    })
  });
}
const AnimatedHeaderHeightProvider = ({
  children
}) => {
  const headerHeight = (0, _elements.useHeaderHeight)();
  const [animatedHeaderHeight] = React.useState(() => new _reactNative.Animated.Value(headerHeight));
  React.useEffect(() => {
    animatedHeaderHeight.setValue(headerHeight);
  }, [animatedHeaderHeight, headerHeight]);
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(_useAnimatedHeaderHeight.AnimatedHeaderHeightContext.Provider, {
    value: animatedHeaderHeight,
    children: children
  });
};
const styles = _reactNative.StyleSheet.create({
  contentContainer: {
    flex: 1
  },
  backImage: {
    height: 24,
    width: 24,
    margin: 3
  }
});
//# sourceMappingURL=NativeStackView.js.map