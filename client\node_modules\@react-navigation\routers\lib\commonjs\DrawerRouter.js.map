{"version": 3, "names": ["_nonSecure", "require", "_Tab<PERSON><PERSON>er", "DrawerActions", "exports", "TabActions", "openDrawer", "type", "closeDrawer", "toggle<PERSON>rawer", "DrawerRouter", "defaultStatus", "rest", "router", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDrawerInHistory", "state", "Boolean", "history", "some", "it", "addDrawerToHistory", "status", "removeDrawerFromHistory", "filter", "getInitialState", "routeNames", "routeParamList", "routeGetIdList", "default", "stale", "key", "nanoid", "getRehydratedState", "partialState", "getStateForRouteFocus", "result", "getStateForAction", "action", "options", "index", "actionCreators"], "sourceRoot": "../../src", "sources": ["DrawerRouter.tsx"], "mappings": ";;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAAC,UAAA,GAAAD,OAAA;AAmEO,MAAME,aAAa,GAAAC,OAAA,CAAAD,aAAA,GAAG;EAC3B,GAAGE,qBAAU;EACbC,UAAUA,CAAA,EAAqB;IAC7B,OAAO;MAAEC,IAAI,EAAE;IAAc,CAAC;EAChC,CAAC;EACDC,WAAWA,CAAA,EAAqB;IAC9B,OAAO;MAAED,IAAI,EAAE;IAAe,CAAC;EACjC,CAAC;EACDE,YAAYA,CAAA,EAAqB;IAC/B,OAAO;MAAEF,IAAI,EAAE;IAAgB,CAAC;EAClC;AACF,CAAC;AAEM,SAASG,YAAYA,CAAC;EAC3BC,aAAa,GAAG,QAAQ;EACxB,GAAGC;AACgB,CAAC,EAGpB;EACA,MAAMC,MAAM,GAAG,IAAAC,oBAAS,EAACF,IAAI,CAG5B;EAED,MAAMG,iBAAiB,GACrBC,KAEsD,IACnDC,OAAO,CAACD,KAAK,CAACE,OAAO,EAAEC,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAACb,IAAI,KAAK,QAAQ,CAAC,CAAC;EAE/D,MAAMc,kBAAkB,GACtBL,KAA2C,IACF;IACzC,IAAID,iBAAiB,CAACC,KAAK,CAAC,EAAE;MAC5B,OAAOA,KAAK;IACd;IAEA,OAAO;MACL,GAAGA,KAAK;MACRE,OAAO,EAAE,CACP,GAAGF,KAAK,CAACE,OAAO,EAChB;QACEX,IAAI,EAAE,QAAQ;QACde,MAAM,EAAEX,aAAa,KAAK,MAAM,GAAG,QAAQ,GAAG;MAChD,CAAC;IAEL,CAAC;EACH,CAAC;EAED,MAAMY,uBAAuB,GAC3BP,KAA2C,IACF;IACzC,IAAI,CAACD,iBAAiB,CAACC,KAAK,CAAC,EAAE;MAC7B,OAAOA,KAAK;IACd;IAEA,OAAO;MACL,GAAGA,KAAK;MACRE,OAAO,EAAEF,KAAK,CAACE,OAAO,CAACM,MAAM,CAAEJ,EAAE,IAAKA,EAAE,CAACb,IAAI,KAAK,QAAQ;IAC5D,CAAC;EACH,CAAC;EAED,MAAMD,UAAU,GACdU,KAA2C,IACF;IACzC,IAAIL,aAAa,KAAK,MAAM,EAAE;MAC5B,OAAOY,uBAAuB,CAACP,KAAK,CAAC;IACvC;IAEA,OAAOK,kBAAkB,CAACL,KAAK,CAAC;EAClC,CAAC;EAED,MAAMR,WAAW,GACfQ,KAA2C,IACF;IACzC,IAAIL,aAAa,KAAK,MAAM,EAAE;MAC5B,OAAOU,kBAAkB,CAACL,KAAK,CAAC;IAClC;IAEA,OAAOO,uBAAuB,CAACP,KAAK,CAAC;EACvC,CAAC;EAED,OAAO;IACL,GAAGH,MAAM;IAETN,IAAI,EAAE,QAAQ;IAEdkB,eAAeA,CAAC;MAAEC,UAAU;MAAEC,cAAc;MAAEC;IAAe,CAAC,EAAE;MAC9D,MAAMZ,KAAK,GAAGH,MAAM,CAACY,eAAe,CAAC;QACnCC,UAAU;QACVC,cAAc;QACdC;MACF,CAAC,CAAC;MAEF,OAAO;QACL,GAAGZ,KAAK;QACRa,OAAO,EAAElB,aAAa;QACtBmB,KAAK,EAAE,KAAK;QACZvB,IAAI,EAAE,QAAQ;QACdwB,GAAG,EAAE,UAAU,IAAAC,iBAAM,EAAC,CAAC;MACzB,CAAC;IACH,CAAC;IAEDC,kBAAkBA,CAChBC,YAAY,EACZ;MAAER,UAAU;MAAEC,cAAc;MAAEC;IAAe,CAAC,EAC9C;MACA,IAAIM,YAAY,CAACJ,KAAK,KAAK,KAAK,EAAE;QAChC,OAAOI,YAAY;MACrB;MAEA,IAAIlB,KAAK,GAAGH,MAAM,CAACoB,kBAAkB,CAACC,YAAY,EAAE;QAClDR,UAAU;QACVC,cAAc;QACdC;MACF,CAAC,CAAC;MAEF,IAAIb,iBAAiB,CAACmB,YAAY,CAAC,EAAE;QACnC;QACAlB,KAAK,GAAGO,uBAAuB,CAACP,KAAK,CAAC;QACtCA,KAAK,GAAGK,kBAAkB,CAACL,KAAK,CAAC;MACnC;MAEA,OAAO;QACL,GAAGA,KAAK;QACRa,OAAO,EAAElB,aAAa;QACtBJ,IAAI,EAAE,QAAQ;QACdwB,GAAG,EAAE,UAAU,IAAAC,iBAAM,EAAC,CAAC;MACzB,CAAC;IACH,CAAC;IAEDG,qBAAqBA,CAACnB,KAAK,EAAEe,GAAG,EAAE;MAChC,MAAMK,MAAM,GAAGvB,MAAM,CAACsB,qBAAqB,CAACnB,KAAK,EAAEe,GAAG,CAAC;MAEvD,OAAOvB,WAAW,CAAC4B,MAAM,CAAC;IAC5B,CAAC;IAEDC,iBAAiBA,CAACrB,KAAK,EAAEsB,MAAM,EAAEC,OAAO,EAAE;MACxC,QAAQD,MAAM,CAAC/B,IAAI;QACjB,KAAK,aAAa;UAChB,OAAOD,UAAU,CAACU,KAAK,CAAC;QAE1B,KAAK,cAAc;UACjB,OAAOR,WAAW,CAACQ,KAAK,CAAC;QAE3B,KAAK,eAAe;UAClB,IAAID,iBAAiB,CAACC,KAAK,CAAC,EAAE;YAC5B,OAAOO,uBAAuB,CAACP,KAAK,CAAC;UACvC;UAEA,OAAOK,kBAAkB,CAACL,KAAK,CAAC;QAElC,KAAK,SAAS;QACd,KAAK,UAAU;QACf,KAAK,qBAAqB;UAAE;YAC1B,MAAMoB,MAAM,GAAGvB,MAAM,CAACwB,iBAAiB,CAACrB,KAAK,EAAEsB,MAAM,EAAEC,OAAO,CAAC;YAE/D,IAAIH,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACI,KAAK,KAAKxB,KAAK,CAACwB,KAAK,EAAE;cAClD,OAAOhC,WAAW,CAAC4B,MAA8C,CAAC;YACpE;YAEA,OAAOA,MAAM;UACf;QAEA,KAAK,SAAS;UACZ,IAAIrB,iBAAiB,CAACC,KAAK,CAAC,EAAE;YAC5B,OAAOO,uBAAuB,CAACP,KAAK,CAAC;UACvC;UAEA,OAAOH,MAAM,CAACwB,iBAAiB,CAACrB,KAAK,EAAEsB,MAAM,EAAEC,OAAO,CAAC;QAEzD;UACE,OAAO1B,MAAM,CAACwB,iBAAiB,CAACrB,KAAK,EAAEsB,MAAM,EAAEC,OAAO,CAAC;MAC3D;IACF,CAAC;IAEDE,cAAc,EAAEtC;EAClB,CAAC;AACH", "ignoreList": []}