{"version": 3, "names": ["dependencyConfig", "getDependencyConfig", "platformName", "projectConfig", "getProjectConfig"], "sources": ["../../src/config/index.ts"], "sourcesContent": ["import {\n  getDependencyConfig,\n  getProjectConfig,\n} from '@react-native-community/cli-platform-apple';\n\nexport const dependencyConfig = getDependencyConfig({platformName: 'ios'});\nexport const projectConfig = getProjectConfig({platformName: 'ios'});\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAKO,MAAMA,gBAAgB,GAAG,IAAAC,uCAAmB,EAAC;EAACC,YAAY,EAAE;AAAK,CAAC,CAAC;AAAC;AACpE,MAAMC,aAAa,GAAG,IAAAC,oCAAgB,EAAC;EAACF,YAAY,EAAE;AAAK,CAAC,CAAC;AAAC"}