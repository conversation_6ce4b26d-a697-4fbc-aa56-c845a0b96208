# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: appmodules
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target appmodules


#############################################
# Order-only phony target for appmodules

build cmake_object_order_depends_target_appmodules: phony || cmake_object_order_depends_target_core cmake_object_order_depends_target_react-native-mmkv cmake_object_order_depends_target_react_codegen_RNCGeolocationSpec cmake_object_order_depends_target_react_codegen_RNMmkvSpec cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec cmake_object_order_depends_target_react_codegen_lottiereactnative cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen cmake_object_order_depends_target_react_codegen_rnreanimated cmake_object_order_depends_target_react_codegen_rnscreens cmake_object_order_depends_target_react_codegen_rnsvg cmake_object_order_depends_target_react_codegen_safeareacontext

build CMakeFiles/appmodules.dir/D_/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o: CXX_COMPILER__appmodules_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\D_\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\generated\autolinking\src\main\jni\autolinking.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir\D_\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\generated\autolinking\src\main\jni
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libappmodules.pdb

build CMakeFiles/appmodules.dir/OnLoad.cpp.o: CXX_COMPILER__appmodules_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libappmodules.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target appmodules


#############################################
# Link the shared library D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libappmodules.so

build D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libappmodules.so: CXX_SHARED_LIBRARY_LINKER__appmodules_Debug RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/RNCGeolocationSpec-generated.cpp.o RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/ComponentDescriptors.cpp.o RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/EventEmitters.cpp.o RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/Props.cpp.o RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/f3ee4c34af00dbdcf11b60ec1cbc8a99/RNCGeolocationSpecJSI-generated.cpp.o RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/ShadowNodes.cpp.o RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/States.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/lottiereactnative-generated.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ComponentDescriptors.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/EventEmitters.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/Props.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ShadowNodes.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/States.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/b078f66e3fc321a06b399965439ac881/lottiereactnativeJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/RNMmkvSpec-generated.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ComponentDescriptors.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/EventEmitters.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/Props.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/RNMmkvSpecJSI-generated.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ShadowNodes.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/RNVectorIconsSpecJSI-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o CMakeFiles/appmodules.dir/D_/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o CMakeFiles/appmodules.dir/OnLoad.cpp.o | D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact-native-mmkv.so D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_safeareacontext.so D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_rnscreens.so D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_rnsvg.so RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/libz.so C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/liblog.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so || D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact-native-mmkv.so D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_rnscreens.so D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_rnsvg.so D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_safeareacontext.so RNCGeolocationSpec_autolinked_build/react_codegen_RNCGeolocationSpec RNMmkvSpec_autolinked_build/react_codegen_RNMmkvSpec RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec lottiereactnative_autolinked_build/react_codegen_lottiereactnative rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen rnreanimated_autolinked_build/react_codegen_rnreanimated
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact-native-mmkv.so  D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_safeareacontext.so  D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_rnscreens.so  D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_rnsvg.so  RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a  C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/libz.so  C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/liblog.so  -landroid  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  -latomic -lm
  OBJECT_DIR = CMakeFiles\appmodules.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libappmodules.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_FILE = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libappmodules.so
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libappmodules.pdb
  RSP_FILE = CMakeFiles\appmodules.rsp


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNCGeolocationSpec


#############################################
# Order-only phony target for react_codegen_RNCGeolocationSpec

build cmake_object_order_depends_target_react_codegen_RNCGeolocationSpec: phony || RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir

build RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/RNCGeolocationSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNCGeolocationSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/RNCGeolocationSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCGeolocationSpec
  DEP_FILE = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\RNCGeolocationSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir
  OBJECT_FILE_DIR = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir
  TARGET_COMPILE_PDB = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\
  TARGET_PDB = ""

build RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNCGeolocationSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNCGeolocationSpec
  DEP_FILE = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\react\renderer\components\RNCGeolocationSpec\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir
  OBJECT_FILE_DIR = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\react\renderer\components\RNCGeolocationSpec
  TARGET_COMPILE_PDB = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\
  TARGET_PDB = ""

build RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNCGeolocationSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNCGeolocationSpec
  DEP_FILE = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\react\renderer\components\RNCGeolocationSpec\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir
  OBJECT_FILE_DIR = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\react\renderer\components\RNCGeolocationSpec
  TARGET_COMPILE_PDB = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\
  TARGET_PDB = ""

build RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNCGeolocationSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNCGeolocationSpec
  DEP_FILE = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\react\renderer\components\RNCGeolocationSpec\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir
  OBJECT_FILE_DIR = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\react\renderer\components\RNCGeolocationSpec
  TARGET_COMPILE_PDB = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\
  TARGET_PDB = ""

build RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/f3ee4c34af00dbdcf11b60ec1cbc8a99/RNCGeolocationSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNCGeolocationSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec/RNCGeolocationSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCGeolocationSpec
  DEP_FILE = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\f3ee4c34af00dbdcf11b60ec1cbc8a99\RNCGeolocationSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir
  OBJECT_FILE_DIR = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\f3ee4c34af00dbdcf11b60ec1cbc8a99
  TARGET_COMPILE_PDB = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\
  TARGET_PDB = ""

build RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNCGeolocationSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNCGeolocationSpec
  DEP_FILE = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\react\renderer\components\RNCGeolocationSpec\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir
  OBJECT_FILE_DIR = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\react\renderer\components\RNCGeolocationSpec
  TARGET_COMPILE_PDB = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\
  TARGET_PDB = ""

build RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNCGeolocationSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNCGeolocationSpec
  DEP_FILE = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\react\renderer\components\RNCGeolocationSpec\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/react/renderer/components/RNCGeolocationSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir
  OBJECT_FILE_DIR = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\react\renderer\components\RNCGeolocationSpec
  TARGET_COMPILE_PDB = RNCGeolocationSpec_autolinked_build\CMakeFiles\react_codegen_RNCGeolocationSpec.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNCGeolocationSpec

build RNCGeolocationSpec_autolinked_build/react_codegen_RNCGeolocationSpec: phony RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/RNCGeolocationSpec-generated.cpp.o RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/ComponentDescriptors.cpp.o RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/EventEmitters.cpp.o RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/Props.cpp.o RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/f3ee4c34af00dbdcf11b60ec1cbc8a99/RNCGeolocationSpecJSI-generated.cpp.o RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/ShadowNodes.cpp.o RNCGeolocationSpec_autolinked_build/CMakeFiles/react_codegen_RNCGeolocationSpec.dir/react/renderer/components/RNCGeolocationSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNCGeolocationSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\RNCGeolocationSpec_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNCGeolocationSpec_autolinked_build/edit_cache: phony RNCGeolocationSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNCGeolocationSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\RNCGeolocationSpec_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNCGeolocationSpec_autolinked_build/rebuild_cache: phony RNCGeolocationSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_lottiereactnative


#############################################
# Order-only phony target for react_codegen_lottiereactnative

build cmake_object_order_depends_target_react_codegen_lottiereactnative: phony || lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/lottiereactnative-generated.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/lottiereactnative-generated.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\lottiereactnative-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  TARGET_COMPILE_PDB = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\
  TARGET_PDB = ""

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative
  TARGET_COMPILE_PDB = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\
  TARGET_PDB = ""

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative
  TARGET_COMPILE_PDB = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\
  TARGET_PDB = ""

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/Props.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/Props.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative
  TARGET_COMPILE_PDB = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\
  TARGET_PDB = ""

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative
  TARGET_COMPILE_PDB = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\
  TARGET_PDB = ""

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/States.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/States.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\react\renderer\components\lottiereactnative
  TARGET_COMPILE_PDB = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\
  TARGET_PDB = ""

build lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/b078f66e3fc321a06b399965439ac881/lottiereactnativeJSI-generated.cpp.o: CXX_COMPILER__react_codegen_lottiereactnative_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative/lottiereactnativeJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_lottiereactnative
  DEP_FILE = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\b078f66e3fc321a06b399965439ac881\lottiereactnativeJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/react/renderer/components/lottiereactnative -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir
  OBJECT_FILE_DIR = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\b078f66e3fc321a06b399965439ac881
  TARGET_COMPILE_PDB = lottiereactnative_autolinked_build\CMakeFiles\react_codegen_lottiereactnative.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_lottiereactnative

build lottiereactnative_autolinked_build/react_codegen_lottiereactnative: phony lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/lottiereactnative-generated.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ComponentDescriptors.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/EventEmitters.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/Props.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/ShadowNodes.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/react/renderer/components/lottiereactnative/States.cpp.o lottiereactnative_autolinked_build/CMakeFiles/react_codegen_lottiereactnative.dir/b078f66e3fc321a06b399965439ac881/lottiereactnativeJSI-generated.cpp.o


#############################################
# Utility command for edit_cache

build lottiereactnative_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\lottiereactnative_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build lottiereactnative_autolinked_build/edit_cache: phony lottiereactnative_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build lottiereactnative_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\lottiereactnative_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build lottiereactnative_autolinked_build/rebuild_cache: phony lottiereactnative_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rngesturehandler_codegen


#############################################
# Order-only phony target for react_codegen_rngesturehandler_codegen

build cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen: phony || rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\rngesturehandler_codegenJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\rngesturehandler_codegen-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o


#############################################
# Utility command for edit_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\rngesturehandler_codegen_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rngesturehandler_codegen_autolinked_build/edit_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\rngesturehandler_codegen_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rngesturehandler_codegen_autolinked_build/rebuild_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNMmkvSpec


#############################################
# Order-only phony target for react_codegen_RNMmkvSpec

build cmake_object_order_depends_target_react_codegen_RNMmkvSpec: phony || RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir

build RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/RNMmkvSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNMmkvSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/RNMmkvSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNMmkvSpec
  DEP_FILE = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\RNMmkvSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir
  OBJECT_FILE_DIR = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\
  TARGET_PDB = ""

build RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNMmkvSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNMmkvSpec
  DEP_FILE = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\react\renderer\components\RNMmkvSpec\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir
  OBJECT_FILE_DIR = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\react\renderer\components\RNMmkvSpec
  TARGET_COMPILE_PDB = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\
  TARGET_PDB = ""

build RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNMmkvSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNMmkvSpec
  DEP_FILE = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\react\renderer\components\RNMmkvSpec\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir
  OBJECT_FILE_DIR = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\react\renderer\components\RNMmkvSpec
  TARGET_COMPILE_PDB = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\
  TARGET_PDB = ""

build RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNMmkvSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNMmkvSpec
  DEP_FILE = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\react\renderer\components\RNMmkvSpec\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir
  OBJECT_FILE_DIR = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\react\renderer\components\RNMmkvSpec
  TARGET_COMPILE_PDB = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\
  TARGET_PDB = ""

build RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/RNMmkvSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNMmkvSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/RNMmkvSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNMmkvSpec
  DEP_FILE = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\react\renderer\components\RNMmkvSpec\RNMmkvSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir
  OBJECT_FILE_DIR = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\react\renderer\components\RNMmkvSpec
  TARGET_COMPILE_PDB = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\
  TARGET_PDB = ""

build RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNMmkvSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNMmkvSpec
  DEP_FILE = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\react\renderer\components\RNMmkvSpec\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir
  OBJECT_FILE_DIR = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\react\renderer\components\RNMmkvSpec
  TARGET_COMPILE_PDB = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\
  TARGET_PDB = ""

build RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNMmkvSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNMmkvSpec
  DEP_FILE = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\react\renderer\components\RNMmkvSpec\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir
  OBJECT_FILE_DIR = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\react\renderer\components\RNMmkvSpec
  TARGET_COMPILE_PDB = RNMmkvSpec_autolinked_build\CMakeFiles\react_codegen_RNMmkvSpec.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNMmkvSpec

build RNMmkvSpec_autolinked_build/react_codegen_RNMmkvSpec: phony RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/RNMmkvSpec-generated.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ComponentDescriptors.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/EventEmitters.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/Props.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/RNMmkvSpecJSI-generated.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ShadowNodes.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNMmkvSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\RNMmkvSpec_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNMmkvSpec_autolinked_build/edit_cache: phony RNMmkvSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNMmkvSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\RNMmkvSpec_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNMmkvSpec_autolinked_build/rebuild_cache: phony RNMmkvSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react-native-mmkv


#############################################
# Order-only phony target for react-native-mmkv

build cmake_object_order_depends_target_react-native-mmkv: phony || cmake_object_order_depends_target_core cmake_object_order_depends_target_react_codegen_RNMmkvSpec

build RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/src/main/cpp/AndroidLogger.cpp.o: CXX_COMPILER__react-native-mmkv_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/src/main/cpp/AndroidLogger.cpp || cmake_object_order_depends_target_react-native-mmkv
  DEFINES = -Dreact_native_mmkv_EXPORTS
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\CMakeFiles\react-native-mmkv.dir\src\main\cpp\AndroidLogger.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\CMakeFiles\react-native-mmkv.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\CMakeFiles\react-native-mmkv.dir\src\main\cpp
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\CMakeFiles\react-native-mmkv.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact-native-mmkv.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/e43a40a6aaf7464539a6fc7c6d856cb1/react-native-mmkv/cpp/MmkvHostObject.cpp.o: CXX_COMPILER__react-native-mmkv_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/cpp/MmkvHostObject.cpp || cmake_object_order_depends_target_react-native-mmkv
  DEFINES = -Dreact_native_mmkv_EXPORTS
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\CMakeFiles\react-native-mmkv.dir\e43a40a6aaf7464539a6fc7c6d856cb1\react-native-mmkv\cpp\MmkvHostObject.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\CMakeFiles\react-native-mmkv.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\CMakeFiles\react-native-mmkv.dir\e43a40a6aaf7464539a6fc7c6d856cb1\react-native-mmkv\cpp
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\CMakeFiles\react-native-mmkv.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact-native-mmkv.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/e43a40a6aaf7464539a6fc7c6d856cb1/react-native-mmkv/cpp/NativeMmkvModule.cpp.o: CXX_COMPILER__react-native-mmkv_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/cpp/NativeMmkvModule.cpp || cmake_object_order_depends_target_react-native-mmkv
  DEFINES = -Dreact_native_mmkv_EXPORTS
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\CMakeFiles\react-native-mmkv.dir\e43a40a6aaf7464539a6fc7c6d856cb1\react-native-mmkv\cpp\NativeMmkvModule.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/../cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/react/renderer/components/RNMmkvSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\CMakeFiles\react-native-mmkv.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\CMakeFiles\react-native-mmkv.dir\e43a40a6aaf7464539a6fc7c6d856cb1\react-native-mmkv\cpp
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\CMakeFiles\react-native-mmkv.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact-native-mmkv.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target react-native-mmkv


#############################################
# Link the shared library D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact-native-mmkv.so

build D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact-native-mmkv.so: CXX_SHARED_LIBRARY_LINKER__react-native-mmkv_Debug RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/RNMmkvSpec-generated.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ComponentDescriptors.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/EventEmitters.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/Props.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/RNMmkvSpecJSI-generated.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/ShadowNodes.cpp.o RNMmkvSpec_autolinked_build/CMakeFiles/react_codegen_RNMmkvSpec.dir/react/renderer/components/RNMmkvSpec/States.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/src/main/cpp/AndroidLogger.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/e43a40a6aaf7464539a6fc7c6d856cb1/react-native-mmkv/cpp/MmkvHostObject.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/react-native-mmkv.dir/e43a40a6aaf7464539a6fc7c6d856cb1/react-native-mmkv/cpp/NativeMmkvModule.cpp.o | RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/liblog.so C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/libz.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so || RNMmkvSpec_autolinked_build/react_codegen_RNMmkvSpec RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a  C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/liblog.so  -landroid  C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/24/libz.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  -latomic -lm
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\CMakeFiles\react-native-mmkv.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact-native-mmkv.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\CMakeFiles\react-native-mmkv.dir\
  TARGET_FILE = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact-native-mmkv.so
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact-native-mmkv.pdb


#############################################
# Utility command for edit_cache

build RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\RNMmkvSpec_cxxmodule_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNMmkvSpec_cxxmodule_autolinked_build/edit_cache: phony RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\RNMmkvSpec_cxxmodule_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNMmkvSpec_cxxmodule_autolinked_build/rebuild_cache: phony RNMmkvSpec_cxxmodule_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for STATIC_LIBRARY target core


#############################################
# Order-only phony target for core

build cmake_object_order_depends_target_core: phony || RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/MMKV.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\MMKV.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_Android.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/MMKV_Android.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\MMKV_Android.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_IO.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/MMKV_IO.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\MMKV_IO.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_OSX.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/MMKV_OSX.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\MMKV_OSX.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKVLog.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/MMKVLog.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\MMKVLog.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKVLog_Android.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/MMKVLog_Android.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\MMKVLog_Android.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputData.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/CodedInputData.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\CodedInputData.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputData_OSX.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/CodedInputData_OSX.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\CodedInputData_OSX.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputDataCrypt.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/CodedInputDataCrypt.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\CodedInputDataCrypt.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputDataCrypt_OSX.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/CodedInputDataCrypt_OSX.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\CodedInputDataCrypt_OSX.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedOutputData.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/CodedOutputData.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\CodedOutputData.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/KeyValueHolder.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/KeyValueHolder.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\KeyValueHolder.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/PBUtility.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/PBUtility.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\PBUtility.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MiniPBCoder.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/MiniPBCoder.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\MiniPBCoder.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MiniPBCoder_OSX.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/MiniPBCoder_OSX.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\MiniPBCoder_OSX.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMBuffer.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/MMBuffer.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\MMBuffer.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/InterProcessLock.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\InterProcessLock.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock_Win32.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/InterProcessLock_Win32.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\InterProcessLock_Win32.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock_Android.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/InterProcessLock_Android.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\InterProcessLock_Android.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/MemoryFile.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\MemoryFile.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Android.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/MemoryFile_Android.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\MemoryFile_Android.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Linux.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/MemoryFile_Linux.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\MemoryFile_Linux.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Win32.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/MemoryFile_Win32.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\MemoryFile_Win32.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_OSX.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/MemoryFile_OSX.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\MemoryFile_OSX.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/ThreadLock.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/ThreadLock.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\ThreadLock.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/ThreadLock_Win32.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/ThreadLock_Win32.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\ThreadLock_Win32.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/AESCrypt.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/aes/AESCrypt.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\aes\AESCrypt.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\aes
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aes_core.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_aes_core.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\aes\openssl\openssl_aes_core.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\aes\openssl
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_cfb128.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_cfb128.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\aes\openssl\openssl_cfb128.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\aes\openssl
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_md5_dgst.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_md5_dgst.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\aes\openssl\openssl_md5_dgst.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\aes\openssl
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_md5_one.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_md5_one.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\aes\openssl\openssl_md5_one.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\aes\openssl
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/crc32/crc32_armv8.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/crc32/crc32_armv8.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\crc32\crc32_armv8.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\crc32
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/crc32/zlib/crc32.cpp.o: CXX_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/crc32/zlib/crc32.cpp || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\crc32\zlib\crc32.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -std=c++20
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\crc32\zlib
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aesv8-armx.S.o: ASM_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_aesv8-armx.S || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\aes\openssl\openssl_aesv8-armx.S.o.d
  FLAGS = -x assembler-with-cpp -march=armv8-a+crypto -fno-limit-debug-info  -fPIC
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\aes\openssl
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aes-armv4.S.o: ASM_COMPILER__core_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/aes/openssl/openssl_aes-armv4.S || cmake_object_order_depends_target_core
  DEP_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\aes\openssl\openssl_aes-armv4.S.o.d
  FLAGS = -x assembler-with-cpp -march=armv8-a+crypto -fno-limit-debug-info  -fPIC
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  OBJECT_FILE_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\aes\openssl
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb


# =============================================================================
# Link build statements for STATIC_LIBRARY target core


#############################################
# Link the static library RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.a

build RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a: CXX_STATIC_LIBRARY_LINKER__core_Debug RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_Android.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_IO.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKV_OSX.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKVLog.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMKVLog_Android.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputData.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputData_OSX.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputDataCrypt.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedInputDataCrypt_OSX.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/CodedOutputData.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/KeyValueHolder.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/PBUtility.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MiniPBCoder.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MiniPBCoder_OSX.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MMBuffer.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock_Win32.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/InterProcessLock_Android.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Android.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Linux.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_Win32.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/MemoryFile_OSX.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/ThreadLock.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/ThreadLock_Win32.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/AESCrypt.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aes_core.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_cfb128.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_md5_dgst.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_md5_one.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/crc32/crc32_armv8.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/crc32/zlib/crc32.cpp.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aesv8-armx.S.o RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/core.dir/aes/openssl/openssl_aes-armv4.S.o
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  OBJECT_DIR = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\CMakeFiles\core.dir\core.pdb
  TARGET_FILE = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.a
  TARGET_PDB = RNMmkvSpec_cxxmodule_autolinked_build\core\libcore.pdb


#############################################
# Utility command for edit_cache

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\RNMmkvSpec_cxxmodule_autolinked_build\core && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNMmkvSpec_cxxmodule_autolinked_build/core/edit_cache: phony RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\RNMmkvSpec_cxxmodule_autolinked_build\core && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNMmkvSpec_cxxmodule_autolinked_build/core/rebuild_cache: phony RNMmkvSpec_cxxmodule_autolinked_build/core/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnreanimated


#############################################
# Order-only phony target for react_codegen_rnreanimated

build cmake_object_order_depends_target_react_codegen_rnreanimated: phony || rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\rnreanimatedJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\rnreanimated-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rnreanimated

build rnreanimated_autolinked_build/react_codegen_rnreanimated: phony rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnreanimated_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\rnreanimated_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnreanimated_autolinked_build/edit_cache: phony rnreanimated_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\rnreanimated_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnreanimated_autolinked_build/rebuild_cache: phony rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Order-only phony target for react_codegen_safeareacontext

build cmake_object_order_depends_target_react_codegen_safeareacontext: phony || safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a079583fbad2ccb00d24b5a3377a5b45/RNCSafeAreaViewShadowNode.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\a079583fbad2ccb00d24b5a3377a5b45\RNCSafeAreaViewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\a079583fbad2ccb00d24b5a3377a5b45
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c90756518d0c833d1eb9bebd6ceb0d0f/safeareacontext/RNCSafeAreaViewState.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\c90756518d0c833d1eb9bebd6ceb0d0f\safeareacontext\RNCSafeAreaViewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\c90756518d0c833d1eb9bebd6ceb0d0f\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7c00d21af07c291807a849cf68ba53cf/safeareacontext/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\7c00d21af07c291807a849cf68ba53cf\safeareacontext\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\7c00d21af07c291807a849cf68ba53cf\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8230730d5d98182474333995fc1e12d8/components/safeareacontext/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\8230730d5d98182474333995fc1e12d8\components\safeareacontext\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\8230730d5d98182474333995fc1e12d8\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8230730d5d98182474333995fc1e12d8/components/safeareacontext/Props.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\8230730d5d98182474333995fc1e12d8\components\safeareacontext\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\8230730d5d98182474333995fc1e12d8\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8230730d5d98182474333995fc1e12d8/components/safeareacontext/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\8230730d5d98182474333995fc1e12d8\components\safeareacontext\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\8230730d5d98182474333995fc1e12d8\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8230730d5d98182474333995fc1e12d8/components/safeareacontext/States.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\8230730d5d98182474333995fc1e12d8\components\safeareacontext\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\8230730d5d98182474333995fc1e12d8\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/23e67fc329de7dccb14d0961fa9e0c77/safeareacontextJSI-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\23e67fc329de7dccb14d0961fa9e0c77\safeareacontextJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\23e67fc329de7dccb14d0961fa9e0c77
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/114bcbba536a9b1bb6fa5330e32c5c43/codegen/jni/safeareacontext-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\114bcbba536a9b1bb6fa5330e32c5c43\codegen\jni\safeareacontext-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\114bcbba536a9b1bb6fa5330e32c5c43\codegen\jni
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Link the shared library D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_safeareacontext.so

build D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_safeareacontext.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_safeareacontext_Debug safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/a079583fbad2ccb00d24b5a3377a5b45/RNCSafeAreaViewShadowNode.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c90756518d0c833d1eb9bebd6ceb0d0f/safeareacontext/RNCSafeAreaViewState.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/7c00d21af07c291807a849cf68ba53cf/safeareacontext/ComponentDescriptors.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8230730d5d98182474333995fc1e12d8/components/safeareacontext/EventEmitters.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8230730d5d98182474333995fc1e12d8/components/safeareacontext/Props.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8230730d5d98182474333995fc1e12d8/components/safeareacontext/ShadowNodes.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/8230730d5d98182474333995fc1e12d8/components/safeareacontext/States.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/23e67fc329de7dccb14d0961fa9e0c77/safeareacontextJSI-generated.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/114bcbba536a9b1bb6fa5330e32c5c43/codegen/jni/safeareacontext-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  -latomic -lm
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_safeareacontext.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_FILE = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_safeareacontext.so
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb


#############################################
# Utility command for edit_cache

build safeareacontext_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\safeareacontext_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build safeareacontext_autolinked_build/edit_cache: phony safeareacontext_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\safeareacontext_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/rebuild_cache: phony safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Order-only phony target for react_codegen_rnscreens

build cmake_object_order_depends_target_react_codegen_rnscreens: phony || rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d996dceb211187fa93fa79b8fdad8ee2/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\d996dceb211187fa93fa79b8fdad8ee2\components\rnscreens\RNSFullWindowOverlayShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\d996dceb211187fa93fa79b8fdad8ee2\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d996dceb211187fa93fa79b8fdad8ee2/components/rnscreens/RNSModalScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\d996dceb211187fa93fa79b8fdad8ee2\components\rnscreens\RNSModalScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\d996dceb211187fa93fa79b8fdad8ee2\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c33c22593dee6ccd8dcb5bc340f46ad6/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\c33c22593dee6ccd8dcb5bc340f46ad6\renderer\components\rnscreens\RNSScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\c33c22593dee6ccd8dcb5bc340f46ad6\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a0e01e1003fe7ae2710d0f50dc63072c/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\a0e01e1003fe7ae2710d0f50dc63072c\rnscreens\RNSScreenStackHeaderConfigShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\a0e01e1003fe7ae2710d0f50dc63072c\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d996dceb211187fa93fa79b8fdad8ee2/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\d996dceb211187fa93fa79b8fdad8ee2\components\rnscreens\RNSScreenStackHeaderConfigState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\d996dceb211187fa93fa79b8fdad8ee2\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a0e01e1003fe7ae2710d0f50dc63072c/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\a0e01e1003fe7ae2710d0f50dc63072c\rnscreens\RNSScreenStackHeaderSubviewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\a0e01e1003fe7ae2710d0f50dc63072c\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a0e01e1003fe7ae2710d0f50dc63072c/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\a0e01e1003fe7ae2710d0f50dc63072c\rnscreens\RNSScreenStackHeaderSubviewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\a0e01e1003fe7ae2710d0f50dc63072c\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9fe063b77578968dff0561a066b60480/react/renderer/components/rnscreens/RNSScreenState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\9fe063b77578968dff0561a066b60480\react\renderer\components\rnscreens\RNSScreenState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\9fe063b77578968dff0561a066b60480\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\rnscreens.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/71f4d03358aa51ab3ac956a1012f0049/renderer/components/rnscreens/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\71f4d03358aa51ab3ac956a1012f0049\renderer\components\rnscreens\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\71f4d03358aa51ab3ac956a1012f0049\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5fb4b083101ddeea553a84cc890606b8/react/renderer/components/rnscreens/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\5fb4b083101ddeea553a84cc890606b8\react\renderer\components\rnscreens\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\5fb4b083101ddeea553a84cc890606b8\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/21b202d6f80ef16f9737c67dbe58a374/jni/react/renderer/components/rnscreens/Props.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\21b202d6f80ef16f9737c67dbe58a374\jni\react\renderer\components\rnscreens\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\21b202d6f80ef16f9737c67dbe58a374\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/21b202d6f80ef16f9737c67dbe58a374/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\21b202d6f80ef16f9737c67dbe58a374\jni\react\renderer\components\rnscreens\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\21b202d6f80ef16f9737c67dbe58a374\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/21b202d6f80ef16f9737c67dbe58a374/jni/react/renderer/components/rnscreens/States.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\21b202d6f80ef16f9737c67dbe58a374\jni\react\renderer\components\rnscreens\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\21b202d6f80ef16f9737c67dbe58a374\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnscreens.pdb

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/71f4d03358aa51ab3ac956a1012f0049/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\71f4d03358aa51ab3ac956a1012f0049\renderer\components\rnscreens\rnscreensJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\71f4d03358aa51ab3ac956a1012f0049\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnscreens.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Link the shared library D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnscreens.so

build D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_rnscreens.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnscreens_Debug rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d996dceb211187fa93fa79b8fdad8ee2/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d996dceb211187fa93fa79b8fdad8ee2/components/rnscreens/RNSModalScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/c33c22593dee6ccd8dcb5bc340f46ad6/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a0e01e1003fe7ae2710d0f50dc63072c/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/d996dceb211187fa93fa79b8fdad8ee2/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a0e01e1003fe7ae2710d0f50dc63072c/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/a0e01e1003fe7ae2710d0f50dc63072c/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9fe063b77578968dff0561a066b60480/react/renderer/components/rnscreens/RNSScreenState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/71f4d03358aa51ab3ac956a1012f0049/renderer/components/rnscreens/ComponentDescriptors.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5fb4b083101ddeea553a84cc890606b8/react/renderer/components/rnscreens/EventEmitters.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/21b202d6f80ef16f9737c67dbe58a374/jni/react/renderer/components/rnscreens/Props.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/21b202d6f80ef16f9737c67dbe58a374/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/21b202d6f80ef16f9737c67dbe58a374/jni/react/renderer/components/rnscreens/States.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/71f4d03358aa51ab3ac956a1012f0049/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnscreens.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_FILE = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnscreens.so
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnscreens.pdb


#############################################
# Utility command for edit_cache

build rnscreens_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\rnscreens_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnscreens_autolinked_build/edit_cache: phony rnscreens_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\rnscreens_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnscreens_autolinked_build/rebuild_cache: phony rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnsvg


#############################################
# Order-only phony target for react_codegen_rnsvg

build cmake_object_order_depends_target_react_codegen_rnsvg: phony || rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/ef4ef394e63c6eb45574ed2682627aa5/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\ef4ef394e63c6eb45574ed2682627aa5\cpp\react\renderer\components\rnsvg\RNSVGImageShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\ef4ef394e63c6eb45574ed2682627aa5\cpp\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/7454bf9ae9b1e127d86000e90e066e75/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\7454bf9ae9b1e127d86000e90e066e75\common\cpp\react\renderer\components\rnsvg\RNSVGImageState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\7454bf9ae9b1e127d86000e90e066e75\common\cpp\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/650e8db975c2a08ac07a388fdb640f2d/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\650e8db975c2a08ac07a388fdb640f2d\react\renderer\components\rnsvg\RNSVGLayoutableShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\650e8db975c2a08ac07a388fdb640f2d\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/7454bf9ae9b1e127d86000e90e066e75/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\7454bf9ae9b1e127d86000e90e066e75\common\cpp\react\renderer\components\rnsvg\RNSVGShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\7454bf9ae9b1e127d86000e90e066e75\common\cpp\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/rnsvg.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\rnsvg.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/0f394722275748a3b318be03e4260359/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\0f394722275748a3b318be03e4260359\jni\react\renderer\components\rnsvg\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\0f394722275748a3b318be03e4260359\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/35550c58ca4a58409094a9f47e98b328/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\35550c58ca4a58409094a9f47e98b328\codegen\jni\react\renderer\components\rnsvg\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\35550c58ca4a58409094a9f47e98b328\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/88f36a3b35e07696b8128f1742f031b9/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\88f36a3b35e07696b8128f1742f031b9\source\codegen\jni\react\renderer\components\rnsvg\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\88f36a3b35e07696b8128f1742f031b9\source\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/35550c58ca4a58409094a9f47e98b328/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\35550c58ca4a58409094a9f47e98b328\codegen\jni\react\renderer\components\rnsvg\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\35550c58ca4a58409094a9f47e98b328\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/88f36a3b35e07696b8128f1742f031b9/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\88f36a3b35e07696b8128f1742f031b9\source\codegen\jni\react\renderer\components\rnsvg\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\88f36a3b35e07696b8128f1742f031b9\source\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnsvg.pdb

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/0f394722275748a3b318be03e4260359/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\0f394722275748a3b318be03e4260359\jni\react\renderer\components\rnsvg\rnsvgJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\0f394722275748a3b318be03e4260359\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnsvg.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnsvg


#############################################
# Link the shared library D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnsvg.so

build D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_rnsvg.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnsvg_Debug rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/ef4ef394e63c6eb45574ed2682627aa5/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/7454bf9ae9b1e127d86000e90e066e75/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/650e8db975c2a08ac07a388fdb640f2d/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/7454bf9ae9b1e127d86000e90e066e75/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/0f394722275748a3b318be03e4260359/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/35550c58ca4a58409094a9f47e98b328/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/88f36a3b35e07696b8128f1742f031b9/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/35550c58ca4a58409094a9f47e98b328/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/88f36a3b35e07696b8128f1742f031b9/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/0f394722275748a3b318be03e4260359/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=77 -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so  -latomic -lm
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnsvg.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_FILE = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnsvg.so
  TARGET_PDB = D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\build\intermediates\cxx\Debug\3a223j5o\obj\arm64-v8a\libreact_codegen_rnsvg.pdb


#############################################
# Utility command for edit_cache

build rnsvg_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\rnsvg_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnsvg_autolinked_build/edit_cache: phony rnsvg_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnsvg_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\rnsvg_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnsvg_autolinked_build/rebuild_cache: phony rnsvg_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNVectorIconsSpec


#############################################
# Order-only phony target for react_codegen_RNVectorIconsSpec

build cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec: phony || RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/RNVectorIconsSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\RNVectorIconsSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/RNVectorIconsSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\796ad459e5c56493b6ccb0d610a9a963\RNVectorIconsSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\796ad459e5c56493b6ccb0d610a9a963
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -ID:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/9cd38d273aa80e0d66466b04adf6ea09/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.10.2/transforms/495638402d0fc5d3374d8dd464a0b611/transformed/react-android-0.77.0-debug/prefab/modules/reactnative/include
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNVectorIconsSpec

build RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec: phony RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/RNVectorIconsSpecJSI-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNVectorIconsSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\RNVectorIconsSpec_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNVectorIconsSpec_autolinked_build/edit_cache: phony RNVectorIconsSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNVectorIconsSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D D:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a\RNVectorIconsSpec_autolinked_build && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup -BD:\Zave\aC5d55GwXZEbEbfnCDrKUR\ZAVE\29-05\client\android\app\.cxx\Debug\3a223j5o\arm64-v8a"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNVectorIconsSpec_autolinked_build/rebuild_cache: phony RNVectorIconsSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build appmodules: phony D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libappmodules.so

build core: phony RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a

build libappmodules.so: phony D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libappmodules.so

build libcore.a: phony RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a

build libreact-native-mmkv.so: phony D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact-native-mmkv.so

build libreact_codegen_rnscreens.so: phony D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_rnscreens.so

build libreact_codegen_rnsvg.so: phony D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_rnsvg.so

build libreact_codegen_safeareacontext.so: phony D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_safeareacontext.so

build react-native-mmkv: phony D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact-native-mmkv.so

build react_codegen_RNCGeolocationSpec: phony RNCGeolocationSpec_autolinked_build/react_codegen_RNCGeolocationSpec

build react_codegen_RNMmkvSpec: phony RNMmkvSpec_autolinked_build/react_codegen_RNMmkvSpec

build react_codegen_RNVectorIconsSpec: phony RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec

build react_codegen_lottiereactnative: phony lottiereactnative_autolinked_build/react_codegen_lottiereactnative

build react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

build react_codegen_rnreanimated: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

build react_codegen_rnscreens: phony D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_rnscreens.so

build react_codegen_rnsvg: phony D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_rnsvg.so

build react_codegen_safeareacontext: phony D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_safeareacontext.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a

build all: phony D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libappmodules.so RNCGeolocationSpec_autolinked_build/all lottiereactnative_autolinked_build/all rngesturehandler_codegen_autolinked_build/all RNMmkvSpec_autolinked_build/all RNMmkvSpec_cxxmodule_autolinked_build/all rnreanimated_autolinked_build/all safeareacontext_autolinked_build/all rnscreens_autolinked_build/all rnsvg_autolinked_build/all RNVectorIconsSpec_autolinked_build/all

# =============================================================================

#############################################
# Folder: D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a/RNCGeolocationSpec_autolinked_build

build RNCGeolocationSpec_autolinked_build/all: phony RNCGeolocationSpec_autolinked_build/react_codegen_RNCGeolocationSpec

# =============================================================================

#############################################
# Folder: D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a/RNMmkvSpec_autolinked_build

build RNMmkvSpec_autolinked_build/all: phony RNMmkvSpec_autolinked_build/react_codegen_RNMmkvSpec

# =============================================================================

#############################################
# Folder: D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a/RNMmkvSpec_cxxmodule_autolinked_build

build RNMmkvSpec_cxxmodule_autolinked_build/all: phony D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact-native-mmkv.so RNMmkvSpec_cxxmodule_autolinked_build/core/all

# =============================================================================

#############################################
# Folder: D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a/RNMmkvSpec_cxxmodule_autolinked_build/core

build RNMmkvSpec_cxxmodule_autolinked_build/core/all: phony RNMmkvSpec_cxxmodule_autolinked_build/core/libcore.a

# =============================================================================

#############################################
# Folder: D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a/RNVectorIconsSpec_autolinked_build

build RNVectorIconsSpec_autolinked_build/all: phony RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec

# =============================================================================

#############################################
# Folder: D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a/lottiereactnative_autolinked_build

build lottiereactnative_autolinked_build/all: phony lottiereactnative_autolinked_build/react_codegen_lottiereactnative

# =============================================================================

#############################################
# Folder: D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a/rngesturehandler_codegen_autolinked_build

build rngesturehandler_codegen_autolinked_build/all: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

# =============================================================================

#############################################
# Folder: D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a/rnreanimated_autolinked_build

build rnreanimated_autolinked_build/all: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

# =============================================================================

#############################################
# Folder: D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a/rnscreens_autolinked_build

build rnscreens_autolinked_build/all: phony D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_rnscreens.so

# =============================================================================

#############################################
# Folder: D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a/rnsvg_autolinked_build

build rnsvg_autolinked_build/all: phony D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_rnsvg.so

# =============================================================================

#############################################
# Folder: D:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a/safeareacontext_autolinked_build

build safeareacontext_autolinked_build/all: phony D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/intermediates/cxx/Debug/3a223j5o/obj/arm64-v8a/libreact_codegen_safeareacontext.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a/CMakeFiles/cmake.verify_globs | C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeASMCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeASMInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineASMCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestASMCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-ASM.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-ASM.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeASMCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a/CMakeFiles/VerifyGlobs.cmake D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeASMCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeASMInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineASMCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestASMCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-ASM.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-ASM.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeASMCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/arm64-v8a/CMakeFiles/VerifyGlobs.cmake D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/.cxx/Debug/3a223j5o/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/29-05/client/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/@react-native-community/geolocation/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/lottie-react-native/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/MMKV/Core/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-mmkv/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt D$:/Zave/aC5d55GwXZEbEbfnCDrKUR/ZAVE/client/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
