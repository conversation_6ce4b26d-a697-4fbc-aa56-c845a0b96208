"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getInfo = getInfo;
function _execa() {
  const data = _interopRequireDefault(require("execa"));
  _execa = function () {
    return data;
  };
  return data;
}
function _fastXmlParser() {
  const data = require("fast-xml-parser");
  _fastXmlParser = function () {
    return data;
  };
  return data;
}
function fs() {
  const data = _interopRequireWildcard(require("fs"));
  fs = function () {
    return data;
  };
  return data;
}
function path() {
  const data = _interopRequireWildcard(require("path"));
  path = function () {
    return data;
  };
  return data;
}
function _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== "function") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }
function _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function isErrorLike(err) {
  return Boolean(err && typeof err === 'object' && 'message' in err && typeof err.message === 'string');
}
function parseTargetList(json) {
  try {
    const info = JSON.parse(json);
    if ('project' in info) {
      return info.project;
    } else if ('workspace' in info) {
      return info.workspace;
    }
    return undefined;
  } catch (error) {
    if (isErrorLike(error)) {
      const match = error.message.match(/xcodebuild: error: (.*)/);
      if (match) {
        throw new Error(match[0]);
      }
    }
    throw error;
  }
}
function getInfo(projectInfo, sourceDir) {
  if (!projectInfo.isWorkspace) {
    const xcodebuild = _execa().default.sync('xcodebuild', ['-list', '-json']);
    return parseTargetList(xcodebuild.stdout);
  }
  const xmlParser = new (_fastXmlParser().XMLParser)({
    ignoreAttributes: false
  });
  const xcworkspacedata = path().join(sourceDir, projectInfo.name, 'contents.xcworkspacedata');
  const workspace = fs().readFileSync(xcworkspacedata, {
    encoding: 'utf-8'
  });
  const fileRef = xmlParser.parse(workspace).Workspace.FileRef;
  const refs = Array.isArray(fileRef) ? fileRef : [fileRef];
  return refs.reduce((result, ref) => {
    const location = ref['@_location'];
    if (!location.endsWith('.xcodeproj')) {
      return result;
    }

    // Ignore the project generated by CocoaPods
    if (location.endsWith('/Pods.xcodeproj')) {
      return result;
    }
    const xcodebuild = _execa().default.sync('xcodebuild', ['-list', '-json', '-project', path().join(sourceDir, location.replace('group:', ''))]);
    const info = parseTargetList(xcodebuild.stdout);
    if (!info) {
      return result;
    }
    const schemes = info.schemes;

    // If this is the first project, use it as the "main" project
    if (!result) {
      if (!Array.isArray(schemes)) {
        info.schemes = [];
      }
      return info;
    }
    if (!Array.isArray(result.schemes)) {
      throw new Error("This shouldn't happen since we set it earlier");
    }

    // For subsequent projects, merge schemes list
    if (Array.isArray(schemes) && schemes.length > 0) {
      result.schemes = result.schemes.concat(schemes);
    }
    return result;
  }, undefined);
}

//# sourceMappingURL=/Users/<USER>/Developer/oss/rncli/packages/cli-platform-apple/build/tools/getInfo.js.map